﻿using Irix.Entities;
using Irix.Persistence.Contract;
using Irix.Persistence.Data;
using Logger.Contract;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Caching.Memory;
using Repricer.Cache;
using RePricer.Util;
using System.Collections.Concurrent;
using System.Data;
using System.Diagnostics;
using Constant = RePricer.Constants.PersistanceConstant;
using constants = RePricer.Constants.ServiceConstants;

namespace Irix.Persistence
{
    public class MasterPersistence : PersistanceBase, IMasterPersistence
    {
        private readonly ILogger _log;
        private string _className = nameof(MasterPersistence);
        private readonly IMemoryCache _memoryCache;
        private readonly int _maxRetries = 3;  // Maximum number of retries
        private readonly int _initialDelayMilliseconds = 2000; // Initial delay for retry (2 seconds)
        private readonly object _lock_LoadExternalApiData = new object(); // Initial delay for retry (2 seconds)
        private readonly object _lock_GetAllRoomDetailsFromDatabaseAsync = new object(); // Initial delay for retry (2 seconds)
        private readonly object _lock_GetAllBoardMappingsAsync = new object(); // Initial delay for retry (2 seconds)
        private readonly object _lock_InsertOrUpdateMultiSupplierLog = new object(); // Initial delay for retry (2 seconds)
        private static ConcurrentDictionary<string, DateTime> _lastRunTimesGeneric = new ConcurrentDictionary<string, DateTime>();

        public MasterPersistence(ILogger log, IMemoryCache memoryCache)
        {
            _log = log;
            _className = nameof(MasterPersistence);
            _memoryCache = memoryCache;
            _lastRunTimesGeneric = new ConcurrentDictionary<string, DateTime>();
        }

        public List<CurrencyExchangeRates> LoadExchangeRates()
        {
            var watch = Stopwatch.StartNew();

            var currencyExchangeRates = new List<CurrencyExchangeRates>();
            try
            {
                using SqlConnection connection = Connection();
                {
                    connection.Open();

                    using SqlDataReader reader = ExecuteStoredProcedureAsync(connection, Constant.GetExchangeRatesForChangeCurrency).GetAwaiter().GetResult();
                    {
                        var masterData = new MasterData();
                        while (reader.Read())
                        {
                            currencyExchangeRates.Add(masterData.ExchangeRangeData(reader));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = Constant.LoadExchangeRates,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            //_log.Info($"MasterPersistence|LoadExchangeRates|{elapsedTimeInSeconds} in {watch.Elapsed}");
            return currencyExchangeRates;
        }

        public List<EmailTemplate> LoadEmailData(int RePricerId, DateTime? fromDate, DateTime? toDate)
        {
            var watch = Stopwatch.StartNew();

            var EmailTemplates = new List<EmailTemplate>();
            try
            {
                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.GetEmailData, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                // Add the RePricerId parameter
                command.Parameters.AddWithValue(Constant.RePricerId, RePricerId);
                command.Parameters.AddWithValue(Constant.ReservationId, DBNull.Value);

                // Add the fromDate and toDate parameters with only the date part
                command.Parameters.AddWithValue(Constant.FromDate, fromDate.HasValue ? (object)fromDate.Value.Date : DBNull.Value);
                command.Parameters.AddWithValue(Constant.ToDate, toDate.HasValue ? (object)toDate.Value.Date : DBNull.Value);

                using SqlDataReader reader = command.ExecuteReader();
                var masterData = new MasterData();
                while (reader.Read())
                {
                    EmailTemplates.Add(masterData.GetEmailTemplates(reader));
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = Constant.LoadExchangeRates,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            //_log.Info($"MasterPersistence|LoadEmailData|{elapsedTimeInSeconds}|{RePricerId} in {watch.Elapsed}");
            return EmailTemplates;
        }

        public List<EmailContentResult> LoadEmailContent(int RePricerId, DateTime? fromDate, DateTime? toDate)
        {
            var watch = Stopwatch.StartNew();

            var emailContents = new List<EmailContentResult>();
            try
            {
                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.GetEmailContent, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                // Add the RePricerId parameter
                command.Parameters.AddWithValue(Constant.RePricerId, RePricerId);

                command.Parameters.AddWithValue(Constant.FromDate, fromDate.HasValue ? (object)fromDate.Value.Date : DBNull.Value);
                command.Parameters.AddWithValue(Constant.ToDate, toDate.HasValue ? (object)toDate.Value.Date : DBNull.Value);

                using SqlDataReader reader = command.ExecuteReader();
                var masterData = new MasterData();
                while (reader.Read())
                {
                    emailContents.Add(masterData.GetEmailContent(reader));
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = Constant.LoadExchangeRates,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            //_log.Info($"MasterPersistence|LoadEmailContent|{elapsedTimeInSeconds}|{RePricerId} in {watch.Elapsed}");
            return emailContents;
        }

        public GetEmailContentData LoadEmailContentData(int RePricerId, DateTime? fromDate, DateTime? toDate)
        {
            var watch = Stopwatch.StartNew();

            var EmailContentList = new GetEmailContentData();
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand(Constant.GetEmailDataContent, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        command.Parameters.AddWithValue(Constant.RePricerId, RePricerId);
                        command.Parameters.AddWithValue(Constant.FromDate, fromDate.HasValue ? (object)fromDate.Value.Date : DBNull.Value);
                        command.Parameters.AddWithValue(Constant.ToDate, toDate.HasValue ? (object)toDate.Value.Date : DBNull.Value);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            var masterData = new MasterData();

                            while (reader.Read())
                            {
                                try
                                {
                                    EmailContentList.reservationtable = masterData.GetReservationTableData(reader);

                                    if (reader.NextResult())
                                    {
                                        EmailContentList.reservationtablelog = masterData.GetReservationTableLogData(reader);
                                    }

                                    if (reader.NextResult())
                                    {
                                        EmailContentList.preBookLogs = masterData.GetPreBookLogDataList(reader);
                                    }
                                    if (reader.NextResult() && reader.Read())
                                    {
                                        EmailContentList.filteredreservationcount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "filteredreservationcount");
                                    }
                                    if (reader.NextResult() && reader.Read())
                                    {
                                        EmailContentList.distinctreservationsavedownloadCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "distinctreservationsavedownloadCount");
                                    }

                                    if (reader.NextResult() && reader.Read())
                                    {
                                        EmailContentList.distinctreservationindbbysupplier = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "distinctreservationindbbysupplier");
                                    }

                                    if (reader.NextResult() && reader.Read())
                                    {
                                        EmailContentList.totaluniquebooking = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "totaluniquebooking");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    var irixErrorEntity = new IrixErrorEntity
                                    {
                                        ClassName = Constant.MasterPersistence,
                                        MethodName = Constant.LoadExchangeRates,
                                    };
                                    _log.Error(irixErrorEntity, ex);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = Constant.LoadExchangeRates,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            //_log.Info($"MasterPersistence|LoadEmailContentData|{elapsedTimeInSeconds}|{RePricerId} in {watch.Elapsed}");
            return EmailContentList;
        }

        public GetEmailContentData LoadSupplierEmailContentData(int RePricerId, DateTime? fromDate, DateTime? toDate)
        {
            var watch = Stopwatch.StartNew();

            var EmailContentList = new GetEmailContentData();
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand(Constant.GetSupplierEmailDataContent, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        command.Parameters.AddWithValue(Constant.RePricerId, RePricerId);
                        command.Parameters.AddWithValue(Constant.FromDate, fromDate.HasValue ? (object)fromDate.Value.Date : DBNull.Value);
                        command.Parameters.AddWithValue(Constant.ToDate, toDate.HasValue ? (object)toDate.Value.Date : DBNull.Value);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            var masterData = new MasterData();

                            while (reader.Read())
                            {
                                try
                                {
                                    EmailContentList.reservationtable = masterData.GetReservationTableData(reader);

                                    if (reader.NextResult() && reader.Read())
                                    {
                                        EmailContentList.reservationtablelog = masterData.GetReservationTableLogData(reader);
                                    }

                                    if (reader.NextResult() && reader.Read())
                                    {
                                        EmailContentList.preBookLogs = masterData.GetPreBookLogDataList(reader);
                                    }
                                    if (reader.NextResult() && reader.Read())
                                    {
                                        EmailContentList.filteredreservationcount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "filteredreservationcount");
                                    }
                                    if (reader.NextResult() && reader.Read())
                                    {
                                        EmailContentList.distinctreservationsavedownloadCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "distinctreservationsavedownloadCount");
                                    }

                                    if (reader.NextResult() && reader.Read())
                                    {
                                        EmailContentList.distinctreservationindbbysupplier = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "distinctreservationindbbysupplier");
                                    }

                                    if (reader.NextResult() && reader.Read())
                                    {
                                        EmailContentList.totaluniquebooking = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "totaluniquebooking");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    var irixErrorEntity = new IrixErrorEntity
                                    {
                                        ClassName = Constant.MasterPersistence,
                                        MethodName = Constant.LoadExchangeRates,
                                    };
                                    _log.Error(irixErrorEntity, ex);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = Constant.LoadExchangeRates,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            //_log.Info($"MasterPersistence|LoadEmailContentData|{elapsedTimeInSeconds}|{RePricerId} in {watch.Elapsed}");
            return EmailContentList;
        }

        public PreBookResult GetPreBookResult(PrebookRequest prebookRequest)
        {
            var watch = Stopwatch.StartNew();

            var preBookResult = new PreBookResult();
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand(Constant.GetPrebookResult, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        command.Parameters.AddWithValue(Constant.RePricerId, prebookRequest.RepricerId);
                        command.Parameters.AddWithValue(Constant.ReservationId, prebookRequest.ReservationId);
                        command.Parameters.AddWithValue(Constant.PageNumber, prebookRequest.PageNumber);
                        command.Parameters.AddWithValue(Constant.PageSize, prebookRequest.PageSize);
                        command.Parameters.AddWithValue(Constant.FromDate, prebookRequest.FromDate);
                        command.Parameters.AddWithValue(Constant.ToDate, prebookRequest.ToDate);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            var masterData = new MasterData();

                            if (reader.HasRows)
                            {
                                while (reader.Read())
                                {
                                    try
                                    {
                                        var Total_Unique_Booking = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "Total_Unique_Booking");
                                        if (reader.NextResult() && reader.HasRows)
                                        {
                                            preBookResult.NotifiedPreBook = masterData.GetPreBookResult(reader);
                                        }

                                        if (reader.NextResult() && reader.HasRows)
                                        {
                                            preBookResult.Summary = masterData.GetSummary(reader);
                                            preBookResult.Summary.Total_Unique_Booking = Total_Unique_Booking;
                                        }

                                        if (reader.NextResult() && reader.HasRows)
                                        {
                                            preBookResult.Action = masterData.ActionData(reader);
                                        }
                                        if (reader.NextResult() && reader.Read())
                                        {
                                            preBookResult.Summary.Reservations_Matched_ClientCriteria = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "filteredreservationcount");
                                        }
                                        if (reader.NextResult() && reader.Read())
                                        {
                                            preBookResult.Summary.Total_Refundable_Reservation = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "totalrefundablereservation");
                                        }

                                        if (reader.NextResult() && reader.HasRows)
                                        {
                                            preBookResult.actionTaken = masterData.ReadActionTakenFromDB(reader);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        var irixErrorEntity = new IrixErrorEntity
                                        {
                                            ClassName = Constant.MasterPersistence,
                                            MethodName = Constant.LoadExchangeRates,
                                        };
                                        _log.Error(irixErrorEntity, ex);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = Constant.LoadExchangeRates,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            //_log.Info($"MasterPersistence|GetPreBookResult|{elapsedTimeInSeconds}|{prebookRequest} in {watch.Elapsed}");
            return preBookResult;
        }

        public List<ActionsTaken> GetBookingActionsTaken(PrebookRequest prebookRequest)
        {
            var watch = Stopwatch.StartNew();
            var actionTakenList = new List<ActionsTaken>();
            var isAllIdsFoundInCache = true;
            var isSpecificId = !string.IsNullOrEmpty(prebookRequest?.ReservationId) ? true : false;
            var repricerId = prebookRequest?.RepricerId ?? 0;
            try
            {
                var reservationIds = prebookRequest?.ReservationId?.Split(',') ?? new string[1];
                int.TryParse(reservationIds?.FirstOrDefault() ?? "0", out var reservationId);

                var cacheKeyActionTakenList = $"RepricerId_{repricerId}_actionTakenList_LoadFromDatabaseWithRetry";
                if (isSpecificId)
                {
                    cacheKeyActionTakenList = $"RepricerId_{repricerId}_{prebookRequest?.ReservationId}_actionTakenList_LoadFromDatabaseWithRetry";
                }

                if (!_memoryCache.TryGetValue(cacheKeyActionTakenList, out List<ActionsTaken> actionTakenListCopy) && prebookRequest?.IsCached == true)
                {
                    if (RedisCacheHelper.KeyExists(cacheKeyActionTakenList))
                    {
                        actionTakenList = RedisCacheHelper.Get<List<ActionsTaken>>(cacheKeyActionTakenList);
                    }
                }
                if (actionTakenListCopy?.Any() == true)
                {
                    actionTakenList = SerializeDeSerializeHelper.DeSerialize<List<ActionsTaken>>(SerializeDeSerializeHelper.Serialize(actionTakenListCopy));
                }

                if (reservationIds != null && reservationIds.Any() && reservationIds.Any(x => x != null))
                {
                    var reservationIdSet = new HashSet<int>(reservationIds
                        .Where(x => !string.IsNullOrEmpty(x))
                        .Select(x => Convert.ToInt32(x)));

                    foreach (var rid in reservationIdSet)
                    {
                        try
                        {
                            var actionTaken = actionTakenList?.FirstOrDefault(x => x.repricerId == repricerId && x.reservationId == rid);

                            if (actionTaken == null)
                            {
                                isAllIdsFoundInCache = false;
                                break;
                            }
                        }
                        catch (Exception ex)
                        {
                            _log.Error("Error while processing reservationId: " + rid, ex);
                        }
                    }
                }

                if (isAllIdsFoundInCache != true || prebookRequest?.IsCached == false || actionTakenList == null || actionTakenList?.Count == 0)
                {
                    var prebookRequestCopy = SerializeDeSerializeHelper.DeSerialize<PrebookRequest>(SerializeDeSerializeHelper.Serialize(prebookRequest));
                    prebookRequestCopy.ReservationId = null;

                    actionTakenList = LoadFromDatabaseWithRetry(prebookRequestCopy);

                    if (actionTakenList != null && actionTakenList.Count > 0 && !isSpecificId)
                    {
                        _memoryCache.Set(cacheKeyActionTakenList, actionTakenList, TimeSpan.FromMinutes(5));
                        RedisCacheHelper.Set(cacheKeyActionTakenList, actionTakenList, TimeSpan.FromDays(7));
                    }
                    else
                    {
                        if (isSpecificId)
                        {
                            var defaultAction = actionTakenList?.FirstOrDefault
                                    (x =>
                                        x.repricerId == prebookRequest.RepricerId
                                        && x.reservationId.ToString() == prebookRequest.ReservationId
                                    );

                            if (defaultAction == null)
                            {
                                actionTakenList = new List<ActionsTaken>();

                                defaultAction = new ActionsTaken
                                {
                                    repricerId = repricerId,
                                    reservationId = reservationId,
                                    ActionId = 8,
                                    ActionName = "N-Other",
                                    HMNotes = "No action taken yet."
                                };
                                actionTakenList.Add(defaultAction);
                                _memoryCache.Set(cacheKeyActionTakenList, actionTakenList, TimeSpan.FromHours(2));
                            }
                        }
                    }
                }

                watch.Stop();
                var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = Constant.LoadExchangeRates,
                };
                _log.Error(irixErrorEntity, ex);
                throw; // Re-throw the exception after logging
            }

            return actionTakenList;
        }

        private List<ActionsTaken> LoadFromDatabaseWithRetry(PrebookRequest prebookRequest)
        {
            int attempt = 1;
            int delayMilliseconds = _initialDelayMilliseconds;

            while (attempt < _maxRetries)
            {
                try
                {
                    // Try to load data from the database
                    using (SqlConnection connection = new SqlConnection(_connectionString))
                    {
                        connection.Open();

                        using (SqlCommand command = new SqlCommand(Constant.GetBookingActionsTaken, connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.CommandTimeout = _commandTimeout;

                            command.Parameters.AddWithValue(Constant.RePricerId, prebookRequest.RepricerId);
                            command.Parameters.AddWithValue(Constant.ReservationId, prebookRequest.ReservationId);
                            command.Parameters.AddWithValue(Constant.PageNumber, prebookRequest.PageNumber);
                            command.Parameters.AddWithValue(Constant.PageSize, prebookRequest.PageSize);
                            command.Parameters.AddWithValue(Constant.FromDate, prebookRequest.FromDate);
                            command.Parameters.AddWithValue(Constant.ToDate, prebookRequest.ToDate);

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                var masterData = new MasterData();
                                return masterData.ReadActionTakenFromDB(reader);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    attempt++;

                    // Log retry attempt
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = Constant.MasterPersistence,
                        MethodName = Constant.LoadExchangeRates,
                        Params = $"Attempt {attempt} of {_maxRetries}"
                    };
                    _log.Error(irixErrorEntity, ex);

                    // If the maximum retries have been reached, throw the exception
                    if (attempt >= _maxRetries)
                    {
                        throw;
                    }

                    // Wait before retrying
                    Task.Delay(delayMilliseconds).Wait();

                    // Exponential backoff: Increase the delay for the next retry
                    delayMilliseconds *= 2;
                }
            }

            // If all retries fail, return an empty list
            return new List<ActionsTaken>();
        }

        public List<MaxProfit> GetMaxProfitData(int RepricerId, string procedurename)
        {
            string cacheKey = $"{procedurename}_{RepricerId}";

            var maxProfit = new List<MaxProfit>();

            try
            {
                if (RedisCacheHelper.KeyExists(cacheKey))
                {
                    maxProfit = RedisCacheHelper.Get<List<MaxProfit>>(cacheKey);
                }
                else
                {
                    using (SqlConnection connection = new SqlConnection(_connectionString))
                    {
                        connection.Open();

                        using (SqlCommand command = new SqlCommand(procedurename, connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.CommandTimeout = _commandTimeout;

                            command.Parameters.AddWithValue(Constant.RePricerId, RepricerId);

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    var masterData = new MasterData();
                                    maxProfit = masterData.GetmaxProfit(reader);
                                    RedisCacheHelper.Set(cacheKey, maxProfit, TimeSpan.FromHours(2));
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = Constant.LoadExchangeRates,
                };
                _log.Error(irixErrorEntity, ex);
                throw; // Re-throw the exception after logging
            }

            return maxProfit;
        }

        public decimal GetRealizedGain(int RepricerId)
        {
            decimal RealizedGain = 0.0m;
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand(Constant.GetRealizedGain, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        command.Parameters.AddWithValue(Constant.RePricerId, RepricerId);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.HasRows)
                            {
                                while (reader.Read())
                                {
                                    RealizedGain = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "RealizedGain");
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = "GetRealizedGain",
                };
                _log.Error(irixErrorEntity, ex);
                throw; // Re-throw the exception after logging
            }

            return RealizedGain;
        }

        public PreBookResult GetRecommendationEdgeCaseCP(PrebookRequest prebookRequest)
        {
            var watch = Stopwatch.StartNew();

            var preBookResult = new PreBookResult();
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand(Constant.GetRecommendationEdgeCaseCP, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        command.Parameters.AddWithValue(Constant.RePricerId, prebookRequest.RepricerId);
                        command.Parameters.AddWithValue(Constant.ReservationId, prebookRequest.ReservationId);
                        command.Parameters.AddWithValue(Constant.PageNumber, prebookRequest.PageNumber);
                        command.Parameters.AddWithValue(Constant.PageSize, prebookRequest.PageSize);
                        command.Parameters.AddWithValue(Constant.FromDate, prebookRequest.FromDate);
                        command.Parameters.AddWithValue(Constant.ToDate, prebookRequest.ToDate);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            var masterData = new MasterData();

                            if (reader.HasRows)
                            {
                                while (reader.Read())
                                {
                                    try
                                    {
                                        var Total_Unique_Booking = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "Total_Unique_Booking");
                                        if (reader.NextResult() && reader.HasRows)
                                        {
                                            preBookResult.NotifiedPreBook = masterData.GetPreBookResult(reader);
                                        }

                                        if (reader.NextResult() && reader.HasRows)
                                        {
                                            preBookResult.Summary = masterData.GetSummary(reader);
                                            preBookResult.Summary.Total_Unique_Booking = Total_Unique_Booking;
                                        }

                                        if (reader.NextResult() && reader.HasRows)
                                        {
                                            preBookResult.Action = masterData.ActionData(reader);
                                        }
                                        if (reader.NextResult() && reader.Read())
                                        {
                                            preBookResult.Summary.Reservations_Matched_ClientCriteria = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "filteredreservationcount");
                                        }
                                        if (reader.NextResult() && reader.Read())
                                        {
                                            preBookResult.Summary.Total_Refundable_Reservation = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "totalrefundablereservation");
                                        }
                                        if (reader.NextResult() && reader.HasRows)
                                        {
                                            preBookResult.actionTaken = masterData.ReadActionTakenFromDB(reader);
                                        }
                                        //if (reader.NextResult() && reader.Read())
                                        //{
                                        //    try
                                        //    {
                                        //        preBookResult.Summary.Reservations_Matched_ClientCriteria = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "filteredreservationcount");
                                        //    }
                                        //    catch(Exception ex)
                                        //    {
                                        //    }
                                        //}
                                        //if (reader.NextResult() && reader.Read())
                                        //{
                                        //    preBookResult.Summary.Total_Refundable_Reservation = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "totalrefundablereservation");
                                        //}
                                    }
                                    catch (Exception ex)
                                    {
                                        var irixErrorEntity = new IrixErrorEntity
                                        {
                                            ClassName = Constant.MasterPersistence,
                                            MethodName = Constant.LoadExchangeRates,
                                        };
                                        _log.Error(irixErrorEntity, ex);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = Constant.LoadExchangeRates,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            //_log.Info($"MasterPersistence|GetPreBookResult|{elapsedTimeInSeconds}|{prebookRequest} in {watch.Elapsed}");
            return preBookResult;
        }

        public PreBookResult GetRecommendationEdgeCasePR(PrebookRequest prebookRequest)
        {
            var watch = Stopwatch.StartNew();

            var preBookResult = new PreBookResult();
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand(Constant.GetRecommendationEdgeCasePR, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        command.Parameters.AddWithValue(Constant.RePricerId, prebookRequest.RepricerId);
                        command.Parameters.AddWithValue(Constant.ReservationId, prebookRequest.ReservationId);
                        command.Parameters.AddWithValue(Constant.PageNumber, prebookRequest.PageNumber);
                        command.Parameters.AddWithValue(Constant.PageSize, prebookRequest.PageSize);
                        command.Parameters.AddWithValue(Constant.FromDate, prebookRequest.FromDate);
                        command.Parameters.AddWithValue(Constant.ToDate, prebookRequest.ToDate);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            var masterData = new MasterData();

                            if (reader.HasRows)
                            {
                                while (reader.Read())
                                {
                                    try
                                    {
                                        var Total_Unique_Booking = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "Total_Unique_Booking");
                                        if (reader.NextResult() && reader.HasRows)
                                        {
                                            preBookResult.NotifiedPreBook = masterData.GetPreBookResult(reader);
                                        }

                                        if (reader.NextResult() && reader.HasRows)
                                        {
                                            preBookResult.Summary = masterData.GetSummary(reader);
                                            preBookResult.Summary.Total_Unique_Booking = Total_Unique_Booking;
                                        }

                                        if (reader.NextResult() && reader.HasRows)
                                        {
                                            preBookResult.Action = masterData.ActionData(reader);
                                        }
                                        if (reader.NextResult() && reader.Read())
                                        {
                                            preBookResult.Summary.Reservations_Matched_ClientCriteria = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "filteredreservationcount");
                                        }
                                        if (reader.NextResult() && reader.Read())
                                        {
                                            preBookResult.Summary.Total_Refundable_Reservation = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "totalrefundablereservation");
                                        }
                                        if (reader.NextResult() && reader.HasRows)
                                        {
                                            preBookResult.actionTaken = masterData.ReadActionTakenFromDB(reader);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        var irixErrorEntity = new IrixErrorEntity
                                        {
                                            ClassName = Constant.MasterPersistence,
                                            MethodName = Constant.LoadExchangeRates,
                                        };
                                        _log.Error(irixErrorEntity, ex);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = Constant.LoadExchangeRates,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            //_log.Info($"MasterPersistence|GetPreBookResult|{elapsedTimeInSeconds}|{prebookRequest} in {watch.Elapsed}");
            return preBookResult;
        }

        public List<ActionsTaken> GetBookedAction(PrebookRequest prebookRequest)
        {
            var watch = Stopwatch.StartNew();

            var preBookResult = new List<ActionsTaken>();
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand(Constant.GetBookedAction, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        command.Parameters.AddWithValue(Constant.RePricerId, prebookRequest.RepricerId);
                        command.Parameters.AddWithValue(Constant.ReservationId, prebookRequest.ReservationId);
                        command.Parameters.AddWithValue(Constant.PageNumber, prebookRequest.PageNumber);
                        command.Parameters.AddWithValue(Constant.PageSize, prebookRequest.PageSize);
                        command.Parameters.AddWithValue(Constant.FromDate, prebookRequest.FromDate);
                        command.Parameters.AddWithValue(Constant.ToDate, prebookRequest.ToDate);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            var masterData = new MasterData();

                            if (reader.HasRows)
                            {
                                while (reader.Read())
                                {
                                    try
                                    {
                                        ActionsTaken action = new ActionsTaken
                                        {
                                            repricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerId"),
                                            reservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                                            ExtraData = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ExtraData"),
                                            HMNotes = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "HMNotes"),
                                            NewBookingId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "NewBookingId"),
                                            ActionId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ActionId"),
                                            RealizedGain = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "NewBookingPrice"),
                                            createdById = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "createdById"),
                                            createdByName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "createdByName"),
                                            ActionName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ActionName"),
                                            createdOn = DbPropertyHelper.DateTimePropertyFromRow(reader, "createdOn")
                                        };
                                        preBookResult.Add(action);
                                    }
                                    catch (Exception ex)
                                    {
                                        var irixErrorEntity = new IrixErrorEntity
                                        {
                                            ClassName = Constant.MasterPersistence,
                                            MethodName = Constant.LoadExchangeRates,
                                        };
                                        _log.Error(irixErrorEntity, ex);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = Constant.LoadExchangeRates,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            //_log.Info($"MasterPersistence|GetPreBookResult|{elapsedTimeInSeconds}|{prebookRequest} in {watch.Elapsed}");
            return preBookResult;
        }

        public async Task InsertUserMapping(int RePricerId, string UserId)
        {
            try
            {
                using SqlConnection connection = new SqlConnection(_RePricerconnectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.InsertUserMapping, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                command.Parameters.AddWithValue(Constant.RePricerId, RePricerId);
                command.Parameters.AddWithValue(Constant.UserId, UserId);

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = Constant.InsertReservationDataAsync
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public async Task<List<UserMappingResult>> GetUserMapping(int RePricerId = 0)
        {
            List<UserMappingResult> userMappings = new List<UserMappingResult>();
            try
            {
                using SqlConnection connection = RePricerConnection();
                connection.Open();
                SqlParameter[] parameters =
                  {
                 new SqlParameter(Constant.RePricerId, SqlDbType.Int) { Value = RePricerId }
                 };

                using SqlDataReader reader = ExecuteStoredProcedureAsync(connection, Constant.GetUserMapping, parameters).GetAwaiter().GetResult();

                while (reader.Read())
                {
                    UserMappingResult userMapping = new UserMappingResult
                    {
                        RepricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerId"),
                        UserMappingId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "UserMappingId"),
                        UserId = DbPropertyHelper.StringPropertyFromRow(reader, "UserId"),
                        AdminId = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "AdminId")
                    };

                    userMappings.Add(userMapping);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = "GetUserMapping",
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            return userMappings;
        }

        public RepricerReportResponse FetchViewReportFromDatabase(RepricerReportRequest request)
        {
            var reports = new RepricerReportResponse();
            var roomDetails = new List<GiataRoomMapping>();
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("dbo.usp_get_ResevationReports_V1", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;
                        command.Parameters.AddWithValue("@RepricerId", request.RepricerId);

                        using (var reader = command.ExecuteReader())
                        {
                            var masterData = new MasterData();
                            reports.ReservationReports = masterData.GetReservationReports(reader);
                            if (reader.NextResult() && reader.HasRows)
                            {
                                reports.Action = masterData.ActionData(reader);
                            }

                            if (reader.NextResult() && reader.HasRows)
                            {
                                roomDetails = masterData.GiataMappingRoomDetailsReader(reader);
                            }
                        }
                    }
                }
                if (reports?.ReservationReports.Count > 0 && roomDetails?.Count > 0)
                {
                    foreach (var item in reports.ReservationReports)
                    {
                        try
                        {
                            if (item?.Reservation != null)
                            {
                                item.Reservation.MappingDetails = roomDetails.FirstOrDefault(x => x.MappingId == item.Reservation.MappingId);
                            }
                            if (item?.Prebook != null)
                            {
                                // Update mapping details for the primary prebook
                                item.Prebook.MappingDetails = roomDetails.FirstOrDefault(x => x.MappingId == item.Prebook.MappingId);
                            }
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = Constant.MasterPersistence,
                                MethodName = nameof(FetchViewReportFromDatabase),
                                Params = "Error during MappingDetails update",
                                RePricerId = item.RepricerId,
                                ReservationId = item.ReservationId
                            };
                            _log.Error(irixErrorEntity, ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = nameof(FetchViewReportFromDatabase),
                };
                _log.Error(irixErrorEntity, ex);
            }
            if (reports != null && reports.ReservationReports != null && reports.ReservationReports.Count > 0)
            {
                reports.ReservationReports = reports.ReservationReports.OrderByDescending(x => x.CreatedDate).ToList();
            }
            return reports;
        }

        public RepricerReportResponse GetInvoiceData(CommonReportRequest reservationRequest)
        {
            var reservationReports = new RepricerReportResponse();
            var currentKey = $"GetInvoceDataFromDB_{reservationRequest.RepricerId}_{reservationRequest.FromDate}_{reservationRequest.ToDate}";

            try
            {
                if ((reservationRequest.IsCached == true || reservationRequest.IsCached == null))
                {
                    if (_memoryCache.TryGetValue(currentKey, out var cachedReports))
                    {
                        cachedReports = SerializeDeSerializeHelper.DeSerialize<RepricerReportResponse>(SerializeDeSerializeHelper.Serialize(cachedReports));
                        return (RepricerReportResponse)cachedReports;
                    }
                }
                if ((reservationRequest.IsCached == true || reservationRequest.IsCached == null) && RedisCacheHelper.KeyExists(currentKey))
                {
                    reservationReports = RedisCacheHelper.Get<RepricerReportResponse>(currentKey);
                    if (reservationReports != null)
                    {
                        _memoryCache.Set(currentKey, reservationReports, TimeSpan.FromHours(20));
                        return reservationReports;
                    }
                }
                reservationReports = new RepricerReportResponse();

                if (reservationReports?.ReservationReports == null || reservationReports?.ReservationReports?.Count == 0)
                {
                    reservationReports = FetchInvoceDataFromDB(reservationRequest);
                    RedisCacheHelper.Set(currentKey, reservationReports, TimeSpan.FromDays(20));
                    _memoryCache.Set(currentKey, reservationReports, TimeSpan.FromMinutes(10));
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = nameof(GetInvoiceData),
                };
                _log.Error(irixErrorEntity, ex);
            }

            return reservationReports;
        }

        private RepricerReportResponse FetchInvoceDataFromDB(CommonReportRequest request)
        {
            var reports = new RepricerReportResponse();
            using (var connection = new SqlConnection(_connectionString))
            {
                connection.Open();
                using (var command = new SqlCommand("dbo.usp_Get_Invoice", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.CommandTimeout = _commandTimeout;
                    command.Parameters.AddWithValue("@RepricerId", request.RepricerId);
                    command.Parameters.AddWithValue("@FromDate", request.FromDate);
                    command.Parameters.AddWithValue("@ToDate", request.ToDate);

                    using (var reader = command.ExecuteReader())
                    {
                        var masterData = new MasterData();
                        reports.ReservationReports = masterData.GetReservationReports(reader);
                        if (reader.NextResult() && reader.HasRows)
                        {
                            reports.Action = masterData.ActionData(reader);
                        }
                    }
                }
            }
            return reports;
        }

        #region ReservationReportSummary

        private static readonly ConcurrentDictionary<int, bool> _isRunningReservationReportSummaryMap = new();
        private static readonly ConcurrentDictionary<int, object> _reservationReportLocks = new();

        public ReservationReportCalculation ReservationReportSummary(DashboardSummaryRequest reservationReportRequest, bool isCached = true)
        {
            var result = new ReservationReportCalculation
            {
                SummarizedView = new List<SummarizedView>()
            };

            if (reservationReportRequest == null)
                return result;

            int repricerId = reservationReportRequest.RepricerId;
            string _method = nameof(ReservationReportSummary);

            isCached = isCached && (reservationReportRequest?.isCached ?? true);
            reservationReportRequest.isCached = isCached;

            string cacheKey = $"{_method}_{repricerId}_{reservationReportRequest.preBookFromDate}_{reservationReportRequest.preBookToDate}_{reservationReportRequest.FirstCreatedFromDate}_{reservationReportRequest.FirstCreatedToDate}";

            var cacheManager = new CacheAndTaskManagerPersistence<int, ReservationReportCalculation>(
                _memoryCache,
                _log,
                _reservationReportLocks,
                _isRunningReservationReportSummaryMap
            );

            try
            {
                result = cacheManager.GetOrFetch
                (
                    key: repricerId,
                    cacheKey: cacheKey,
                    fetchFromDbFunc: () => Task.Run(() => ReservationReportSummaryFromDB(reservationReportRequest, cacheKey)),
                    cacheUpdateFunc: dbResult =>
                    {
                        _memoryCache.Set(cacheKey, dbResult, TimeSpan.FromMinutes(5));
                        RedisCacheHelper.Set(cacheKey, dbResult, TimeSpan.FromHours(2));
                        return Task.CompletedTask;
                    },
                    isCached: isCached,
                    methodName: _method
                );
            }
            catch (Exception ex)
            {
                _log.Error(new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = _method
                }, ex);
            }
            finally
            {
                _reservationReportLocks.TryRemove(repricerId, out _);
            }

            return result;
        }

        /*
        public ReservationReportCalculation ReservationReportSummary(DashboardSummaryRequest reservationReportRequest, bool isCached = true)
        {
            var result = new ReservationReportCalculation
            {
                SummarizedView = new List<SummarizedView>()
            };

            if (reservationReportRequest == null)
                return result;

            var repricerId = reservationReportRequest.RepricerId;

            // Assign method name to variable for reusability
            var _method = nameof(ReservationReportSummary);

            // Set cache-related values upfront
            isCached = isCached && (reservationReportRequest?.isCached ?? true);
            reservationReportRequest.isCached = isCached;

            string newCacheKey = $"{_method}_{repricerId}_{reservationReportRequest.preBookFromDate}_{reservationReportRequest.preBookToDate}_{reservationReportRequest.FirstCreatedFromDate}_{reservationReportRequest.FirstCreatedToDate}";

            try
            {
                if (IsReportAlreadyRunning(repricerId, isCached, newCacheKey, ref result))
                {
                    return result;
                }

                var lockObj = _reservationReportLocks.GetOrAdd(repricerId, _ => new object());

                lock (lockObj)
                {
                    if (!IsReportRunning(repricerId))
                    {
                        StartReportProcessing(repricerId, reservationReportRequest, newCacheKey, ref result);
                    }
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, _method);
            }
            finally
            {
                ReleaseLock(repricerId);
            }

            return result;
        }
        */

        #region Helper Methods

        private bool IsReportAlreadyRunning(int repricerId, bool isCached, string newCacheKey, ref ReservationReportCalculation result)
        {
            if (_isRunningReservationReportSummaryMap.ContainsKey(repricerId) && _isRunningReservationReportSummaryMap[repricerId])
            {
                if (isCached)
                {
                    return TryReturnFromCache(newCacheKey, ref result);
                }

                // Start background task to update the cache if needed
                StartBackgroundCacheUpdate(repricerId, newCacheKey);
                return true;
            }

            return false;
        }

        private bool TryReturnFromCache(string cacheKey, ref ReservationReportCalculation result)
        {
            // Check in memory cache first
            if (_memoryCache.TryGetValue(cacheKey, out ReservationReportCalculation memoryCached))
            {
                result = Common.DeepClone(memoryCached);
                return true;
            }

            // Check Redis if not found in memory cache
            if (RedisCacheHelper.KeyExists(cacheKey))
            {
                var redisCached = RedisCacheHelper.Get<ReservationReportCalculation>(cacheKey);
                _memoryCache.Set(cacheKey, redisCached, TimeSpan.FromMinutes(5)); // Cache in memory for 5 minutes
                result = Common.DeepClone(redisCached);
                return true;
            }

            return false;
        }

        private void StartBackgroundCacheUpdate(int repricerId, string cacheKey)
        {
            // Background task to update cache (Memory + Redis)
            Task.Run(() =>
            {
                var lockObj = _reservationReportLocks.GetOrAdd(repricerId, _ => new object());
                lock (lockObj)
                {
                    try
                    {
                        // Fetch the data from DB and update both caches
                        var dbResult = ReservationReportSummaryFromDB(null, cacheKey);
                        _memoryCache.Set(cacheKey, dbResult, TimeSpan.FromMinutes(5)); // Memory Cache for 5 minutes
                        RedisCacheHelper.Set(cacheKey, dbResult, TimeSpan.FromHours(2)); // Redis Cache for 2 hours
                    }
                    catch (Exception ex)
                    {
                        _log.Error(new IrixErrorEntity
                        {
                            ClassName = _className,
                            MethodName = nameof(ReservationReportSummary),
                        }, ex);
                    }
                }
            });
        }

        private bool IsReportRunning(int repricerId)
        {
            return _isRunningReservationReportSummaryMap.ContainsKey(repricerId) && _isRunningReservationReportSummaryMap[repricerId];
        }

        private void StartReportProcessing(int repricerId, DashboardSummaryRequest reservationReportRequest, string cacheKey, ref ReservationReportCalculation result)
        {
            _isRunningReservationReportSummaryMap[repricerId] = true;

            var dbResult = ReservationReportSummaryFromDB(reservationReportRequest, cacheKey);
            if (dbResult?.SummarizedView?.Count > 0)
            {
                result = dbResult;
            }

            _memoryCache.Set(cacheKey, result, TimeSpan.FromMinutes(5)); // Memory Cache for 5 minutes
            RedisCacheHelper.Set(cacheKey, result, TimeSpan.FromHours(2)); // Redis Cache for 2 hours

            _isRunningReservationReportSummaryMap[repricerId] = false;
        }

        private void HandleError(Exception ex, string methodName)
        {
            _log.Error(new IrixErrorEntity
            {
                ClassName = _className,
                MethodName = methodName,
            }, ex);
        }

        private void ReleaseLock(int repricerId)
        {
            _reservationReportLocks.TryRemove(repricerId, out _);
        }

        #endregion Helper Methods

        public bool IsAllowRun(string taskKey, TimeSpan timeSpan)
        {
            var _methodName = nameof(IsAllowRun);
            var redisKey = _methodName;
            var now = DateTime.UtcNow;

            _lastRunTimesGeneric = RedisCacheHelper.Get<ConcurrentDictionary<string, DateTime>>(redisKey)
                    ?? new ConcurrentDictionary<string, DateTime>();

            if (!_lastRunTimesGeneric.TryGetValue(taskKey, out var lastRunTime) || (now - lastRunTime) >= timeSpan)
            {
                _lastRunTimesGeneric.AddOrUpdate(taskKey, now, (_, _) => now);
                RedisCacheHelper.Set(redisKey, _lastRunTimesGeneric);
                return true;
            }
            return false;
        }

        #endregion ReservationReportSummary

        // Background tasks to reload data and refresh cache
        private ReservationReportCalculation ReservationReportSummaryFromDB(DashboardSummaryRequest reservationReportRequest, string currentKey)
        {
            var reports = new ReservationReportCalculation
            {
                SummarizedView = new List<SummarizedView>()
            };
            try
            {
                if (IsAllowRun(currentKey, TimeSpan.FromSeconds(30)) && (reservationReportRequest?.isCached == false || !RedisCacheHelper.KeyExists(currentKey)))
                {
                    //Console.WriteLine($"\n{DateTime.Now}\t\tDB hit\n {SerializeDeSerializeHelper.Serialize(reservationReportRequest)}");
                    using (var connection = new SqlConnection(_connectionString))
                    {
                        using (var command = new SqlCommand("dbo.usp_get_ReservationReportCalc", connection))
                        {
                            connection.Open();
                            command.CommandType = CommandType.StoredProcedure;
                            command.CommandTimeout = _commandTimeout;

                            command.Parameters.AddWithValue("@RepricerId", reservationReportRequest.RepricerId);
                            command.Parameters.AddWithValue("@CreatedFromDate", reservationReportRequest.preBookFromDate);
                            command.Parameters.AddWithValue("@CreatedToDate", reservationReportRequest.preBookToDate);
                            command.Parameters.AddWithValue("@FirstCreatedFromDate", reservationReportRequest.FirstCreatedFromDate);
                            command.Parameters.AddWithValue("@FirstCreatedToDate", reservationReportRequest.FirstCreatedToDate);
                            command.Parameters.AddWithValue("@iscaching", reservationReportRequest.isCached);

                            using (var reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    reports.RePricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RePricerId");
                                    reports.LifetimePossibleOptimisationProfit = DbPropertyHelper.DecimalPropertyFromRow(reader, "LifetimePossibleOptimisationProfit");
                                    reports.LifetimeOptimisedReservationPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "lifetimeoptimsedReservationPrice");
                                    reports.LifetimeOptimisedPrebookPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "lifetimeoptimisedPrebookPrice");
                                    reports.RealizedGain = DbPropertyHelper.DecimalPropertyFromRow(reader, "RealizedGain");
                                    reports.RealizedGainCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RealizedGainCount");
                                    reports.MissedGain = DbPropertyHelper.DecimalPropertyFromRow(reader, "MissedGain");
                                    reports.MissedGainCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "MissedGainCount");
                                    reports.OpenGain = DbPropertyHelper.DecimalPropertyFromRow(reader, "OpenGain");
                                    reports.OpenGainCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "OpenGainCount");
                                    reports.LifetimeOptimisedBookingCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "LifetimeOptimisedBookingCount");
                                    reports.TotalUniqueBookings = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "TotalUniqueBooking");
                                    reports.FilteredReservationCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "filteredReservationCount");
                                    reports.TotalRefundableReservation = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "TotalRefundableReservation");
                                }

                                if (reader.NextResult())
                                {
                                    while (reader.Read())
                                    {
                                        var summarizedView = new SummarizedView
                                        {
                                            ReportType = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reportType"),
                                            ReservationPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "ReservationPrice"),
                                            PrebookPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "PrebookPrice"),
                                            Profit = DbPropertyHelper.DecimalPropertyFromRow(reader, "Profit"),
                                            MaxProfit = DbPropertyHelper.DecimalPropertyFromRow(reader, "MaxProfit"),
                                            Currency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Currency"),
                                            ProfitPercentage = DbPropertyHelper.DecimalPropertyFromRow(reader, "ProfitPercentage"),
                                            RealizedGain = DbPropertyHelper.DecimalPropertyFromRow(reader, "RealizedGain"),
                                            RealizedGainCount = DbPropertyHelper.Int32PropertyFromRow(reader, "realizedGainCount"),
                                            ReservationsCount = DbPropertyHelper.Int32PropertyFromRow(reader, "TotalReservationID")
                                        };

                                        reports.SummarizedView.Add(summarizedView);
                                    }
                                }
                            }
                        }
                    }

                    // Store the refreshed report in both Redis and memory cache
                    RedisCacheHelper.Set(currentKey, reports, TimeSpan.FromHours(20));
                    _memoryCache.Set(currentKey, reports, DateTimeOffset.UtcNow.AddMinutes(5));
                }
                else
                {
                    //Console.WriteLine($"\n{DateTime.Now}\t\tRedis cache hit\n {SerializeDeSerializeHelper.Serialize(reservationReportRequest)}");
                    var redisCachedReports = RedisCacheHelper.Get<ReservationReportCalculation>(currentKey);
                    _memoryCache.Set(currentKey, redisCachedReports, TimeSpan.FromMinutes(5)); // Cache in memory for 5 mins
                    reports = Common.DeepClone(redisCachedReports); // Return the data from Redis
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(ReservationReportSummaryFromDB),
                };
                _log.Error(irixErrorEntity, ex);
            }
            return reports;
        }

        public List<DateWiseResponse> DateWiseCount(DateWiseRequest prebookRequest, bool iscaching = true)
        {
            var currentKey = $"DateWiseCount_{prebookRequest.RepricerId}_{prebookRequest.FromDate}_{prebookRequest.ToDate}";

            var dateWiseResponses = new List<DateWiseResponse>();

            try
            {
                if (iscaching && RedisCacheHelper.KeyExists(currentKey))
                {
                    dateWiseResponses = RedisCacheHelper.Get<List<DateWiseResponse>>(currentKey);
                }
                else
                {
                    using (SqlConnection connection = new SqlConnection(_connectionString))
                    {
                        connection.Open();

                        using (SqlCommand command = new SqlCommand(Constant.ReservationDateWiseCount, connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.CommandTimeout = _commandTimeout;

                            command.Parameters.AddWithValue(Constant.RePricerId, prebookRequest.RepricerId);
                            if (prebookRequest.FromDate != null)
                            {
                                command.Parameters.AddWithValue(Constant.FromDate, prebookRequest.FromDate);
                            }
                            if (prebookRequest.ToDate != null)
                            {
                                command.Parameters.AddWithValue(Constant.ToDate, prebookRequest.ToDate);
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    while (reader.Read())
                                    {
                                        var dateWiseResponse = new DateWiseResponse
                                        {
                                            Date = (Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Date"))).ToString(constants.datetypeDB),
                                            Count = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationCount")
                                        };
                                        dateWiseResponses.Add(dateWiseResponse);
                                    }
                                }
                            }
                        }
                        RedisCacheHelper.Set(currentKey, dateWiseResponses, TimeSpan.FromDays(30));
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = "DateWiseCount",
                };
                _log.Error(irixErrorEntity, ex);
                throw; // Re-throw the exception after logging
            }

            return dateWiseResponses;
        }

        public ExternalApi LoadExternalApiData(string service)
        {
            ExternalApi externalApi = null;
            var cacheKey = $"ExternalApiData_{service}";

            try
            {
                // 1. Check Memory Cache First
                _memoryCache.TryGetValue(cacheKey, out externalApi);
                if (externalApi != null)
                {
                    return externalApi; // Return from memory cache if available
                }

                // 2. Check Redis Cache if not in memory cache
                externalApi = RedisCacheHelper.Get<ExternalApi>(cacheKey);
                if (externalApi != null)
                {
                    // If found in Redis, also add it to memory cache for faster access in the future
                    _memoryCache.Set(cacheKey, externalApi, DateTimeOffset.Now.AddDays(7));
                    return externalApi; // Return from Redis cache
                }

                // 3. If not found in cache, query the database
                lock (_lock_LoadExternalApiData)
                {
                    using (SqlConnection connection = new SqlConnection(_connectionString))
                    {
                        connection.Open();

                        using (SqlCommand command = new SqlCommand(Constant.GetExternalApiByService, connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.CommandTimeout = _commandTimeout; // Timeout for command execution
                            command.Parameters.AddWithValue(Constant.Service, service ?? (object)DBNull.Value);

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                var masterData = new MasterData();
                                if (reader.Read())
                                {
                                    externalApi = masterData.GetExternalApi(reader); // Map data to ExternalApi object
                                }
                            }
                        }
                    }

                    // 4. If data is found, cache it in both Redis and Memory Cache
                    if (externalApi != null)
                    {
                        // Set to Redis cache with a long expiration (e.g., 60 days)
                        RedisCacheHelper.Set(cacheKey, externalApi, TimeSpan.FromDays(7));

                        // Also set to Memory Cache for quicker access next time
                        _memoryCache.Set(cacheKey, externalApi, DateTimeOffset.Now.AddDays(7));  // 1-day expiration for MemoryCache
                    }
                }
            }
            catch (SqlException sqlEx) when (sqlEx.Number == -2) // SQL Timeout exception code is -2
            {
                // Handle SQL Timeout specifically
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = nameof(LoadExternalApiData)
                };
                _log.Error(irixErrorEntity, new TimeoutException("Database operation timed out", sqlEx));
                throw new TimeoutException("Database operation timed out", sqlEx);
            }
            catch (SqlException sqlEx)
            {
                // Handle other SQL exceptions (e.g., connection issues)
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = nameof(LoadExternalApiData)
                };
                _log.Error(irixErrorEntity, sqlEx);
                throw;
            }
            catch (Exception ex)
            {
                // General exception handling for unexpected errors
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.MasterPersistence,
                    MethodName = nameof(LoadExternalApiData)
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            return externalApi;
        }

        public async Task InsertRoomDetailsAsync(GiataMappingRoomDetails roomDetails, string reservationId, int repricerId)
        {
            SqlConnection connection = null;
            SqlCommand command = null;

            try
            {
                var watch = Stopwatch.StartNew();
                connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                command = new SqlCommand("dbo.usp_GiataInsertRoomDetails", connection)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = _commandTimeout
                };

                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                command.Parameters.AddWithValue("@PropertyName", CleanString(roomDetails.PropertyName));
                command.Parameters.AddWithValue("@GroupName", CleanString(roomDetails.GroupName));
                command.Parameters.AddWithValue("@GroupID", roomDetails.GroupID);
                command.Parameters.AddWithValue("@RoomName", CleanString(roomDetails.RoomName));
                command.Parameters.AddWithValue("@GroupConfidence", roomDetails.GroupConfidence);
                command.Parameters.AddWithValue("@BedDetailDescription", roomDetails.BedDetailDescription ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@RoomCount", roomDetails.RoomCount);
                command.Parameters.AddWithValue("@Accessible", roomDetails.Accessible);
                command.Parameters.AddWithValue("@NonRefundable", roomDetails.NonRefundable);
                command.Parameters.AddWithValue("@Annex", roomDetails.Annex);
                command.Parameters.AddWithValue("@SingleUse", roomDetails.SingleUse);
                command.Parameters.AddWithValue("@SharedBed", roomDetails.SharedBed);
                command.Parameters.AddWithValue("@AverageRoomType", roomDetails.AverageRoomType ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@AverageRoomClasses", roomDetails.AverageRoomClasses ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@AverageRoomViews", roomDetails.AverageRoomViews ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@RepricerID", repricerId);
                command.Parameters.AddWithValue("@ReservationID", reservationId);
                command.Parameters.AddWithValue("@Supplier", roomDetails.Supplier ?? (object)DBNull.Value);

                // Execute the stored procedure
                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(InsertRoomDetailsAsync)
                };
                _log.Error(irixErrorEntity, ex);
            }
            finally
            {
                // Ensure the connection is always closed if it hasn't been disposed of
                if (connection != null && connection.State != ConnectionState.Closed)
                {
                    await connection.CloseAsync();
                    connection.Dispose();
                }
                if (command != null)
                {
                    command.Dispose();
                }
            }
        }

        // Clean up the input string by removing unwanted characters
        private string CleanString(string input)
        {
            try
            {
                if (string.IsNullOrEmpty(input))
                    return input;

                input = input.Replace("&nbsp;", " ");

                input = input.Replace("\r\n", " ")   // Windows newline
                             .Replace("\n", " ")    // Unix newline
                             .Replace("\r", " ");   // Old Mac newline

                input = input.Trim();

                input = System.Text.RegularExpressions.Regex.Replace(input, "<[^>]*>", string.Empty);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(CleanString)
                };
                _log.Error(irixErrorEntity, ex);
            }
            return input;
        }

        public async Task<GiataMappingRoomDetails> GetGiataRoomDetailsAsync(string propertyName, string roomName, int repricerId, string reservationId, string providers = null)
        {
            GiataMappingRoomDetails roomDetails = null;

            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (SqlCommand command = new SqlCommand("dbo.usp_GetRoomDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        command.Parameters.AddWithValue("@RoomName", roomName ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@RepricerID", repricerId);
                        command.Parameters.AddWithValue("@ReservationID", reservationId ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@PropertyName", propertyName ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Supplier", providers ?? (object)DBNull.Value);

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                roomDetails = new GiataMappingRoomDetails
                                {
                                    GIATAMappingID = reader.GetInt32(reader.GetOrdinal("GIATAMappingID")),
                                    PropertyName = reader.IsDBNull(reader.GetOrdinal("PropertyName")) ? null : reader.GetString(reader.GetOrdinal("PropertyName")),
                                    GroupName = reader.IsDBNull(reader.GetOrdinal("GroupName")) ? null : reader.GetString(reader.GetOrdinal("GroupName")),
                                    GroupID = reader.GetInt32(reader.GetOrdinal("GroupID")),
                                    RoomName = reader.IsDBNull(reader.GetOrdinal("RoomName")) ? null : reader.GetString(reader.GetOrdinal("RoomName")),
                                    GroupConfidence = reader.GetInt32(reader.GetOrdinal("GroupConfidence")),
                                    BedDetailDescription = reader.IsDBNull(reader.GetOrdinal("BedDetailDescription")) ? null : reader.GetString(reader.GetOrdinal("BedDetailDescription")),
                                    RoomCount = reader.GetInt32(reader.GetOrdinal("RoomCount")),
                                    Accessible = reader.GetBoolean(reader.GetOrdinal("Accessible")),
                                    NonRefundable = reader.GetBoolean(reader.GetOrdinal("NonRefundable")),
                                    Annex = reader.GetBoolean(reader.GetOrdinal("Annex")),
                                    SingleUse = reader.GetBoolean(reader.GetOrdinal("SingleUse")),
                                    SharedBed = reader.GetBoolean(reader.GetOrdinal("SharedBed")),
                                    AverageRoomType = reader.IsDBNull(reader.GetOrdinal("AverageRoomType")) ? null : reader.GetString(reader.GetOrdinal("AverageRoomType")),
                                    AverageRoomClasses = reader.IsDBNull(reader.GetOrdinal("AverageRoomClasses")) ? null : reader.GetString(reader.GetOrdinal("AverageRoomClasses")),
                                    AverageRoomViews = reader.IsDBNull(reader.GetOrdinal("AverageRoomViews")) ? null : reader.GetString(reader.GetOrdinal("AverageRoomViews")),
                                    RepricerID = reader.GetInt32(reader.GetOrdinal("RepricerID")),
                                    ReservationId = reader.IsDBNull(reader.GetOrdinal("ReservationID")) ? null : reader.GetString(reader.GetOrdinal("ReservationID")),
                                    Supplier = reader.IsDBNull(reader.GetOrdinal("Supplier")) ? null : reader.GetString(reader.GetOrdinal("Supplier"))
                                };
                                return roomDetails;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetGiataRoomDetailsAsync)
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            return roomDetails;  // Will return null if no data is found
        }

        public async Task<List<GiataMappingRoomDetails>> GetAllRoomDetailsFromDatabaseAsync()
        {
            var roomDetailsList = new List<GiataMappingRoomDetails>();
            lock (_lock_GetAllRoomDetailsFromDatabaseAsync)
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    var query = "SELECT * FROM dbo.VW_GiataRoomMapping"; // No parameters, loading all data

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.CommandTimeout = _commandTimeout;
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                try
                                {
                                    var roomMapping = new GiataMappingRoomDetails
                                    {
                                        GIATAMappingID = reader.GetInt32(reader.GetOrdinal("GIATAMappingID")),
                                        PropertyName = reader.IsDBNull(reader.GetOrdinal("propertyName")) ? null : reader.GetString(reader.GetOrdinal("propertyName")),
                                        GroupName = reader.IsDBNull(reader.GetOrdinal("groupName")) ? null : reader.GetString(reader.GetOrdinal("groupName")),
                                        GroupID = (int)(reader.IsDBNull(reader.GetOrdinal("groupID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("groupID"))),
                                        RoomName = reader.IsDBNull(reader.GetOrdinal("roomName")) ? null : reader.GetString(reader.GetOrdinal("roomName")),
                                        GroupConfidence = (int)(reader.IsDBNull(reader.GetOrdinal("groupConfidence")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("groupConfidence"))),
                                        BedDetailDescription = reader.IsDBNull(reader.GetOrdinal("bedDetailDescription")) ? null : reader.GetString(reader.GetOrdinal("bedDetailDescription")),
                                        RoomCount = (int)(reader.IsDBNull(reader.GetOrdinal("roomCount")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("roomCount"))),
                                        Accessible = reader.GetBoolean(reader.GetOrdinal("accessible")),
                                        NonRefundable = reader.GetBoolean(reader.GetOrdinal("nonRefundable")),
                                        Annex = reader.GetBoolean(reader.GetOrdinal("annex")),
                                        SingleUse = reader.GetBoolean(reader.GetOrdinal("singleUse")),
                                        SharedBed = reader.GetBoolean(reader.GetOrdinal("sharedBed")),
                                        AverageRoomType = reader.IsDBNull(reader.GetOrdinal("averageRoomType")) ? null : reader.GetString(reader.GetOrdinal("averageRoomType")),
                                        AverageRoomClasses = reader.IsDBNull(reader.GetOrdinal("averageRoomClasses")) ? null : reader.GetString(reader.GetOrdinal("averageRoomClasses")),
                                        AverageRoomViews = reader.IsDBNull(reader.GetOrdinal("averageRoomViews")) ? null : reader.GetString(reader.GetOrdinal("averageRoomViews")),
                                        RepricerID = reader.GetInt32(reader.GetOrdinal("repricerID")),
                                        ReservationId = reader.IsDBNull(reader.GetOrdinal("reservationID")) ? null : reader.GetString(reader.GetOrdinal("reservationID")),
                                        Supplier = reader.IsDBNull(reader.GetOrdinal("Supplier")) ? null : reader.GetString(reader.GetOrdinal("Supplier"))
                                    };
                                    if (roomMapping != null)
                                    {
                                        roomDetailsList.Add(roomMapping);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    var irixErrorEntity = new IrixErrorEntity
                                    {
                                        ClassName = _className,
                                        MethodName = nameof(GetAllRoomDetailsFromDatabaseAsync)
                                    };
                                    _log.Error(irixErrorEntity, ex);
                                }
                            }
                        }
                    }
                }
            }

            return roomDetailsList;
        }

        public async Task<List<BoardMapping>> GetAllBoardMappingsAsync()
        {
            var boardMappings = new List<BoardMapping>();
            const int maxRetryAttempts = 3;
            int retryCount = 0;

            while (retryCount < maxRetryAttempts)
            {
                try
                {
                    lock (_lock_GetAllBoardMappingsAsync)  // Ensures only one thread accesses this block at a time
                    {
                        using SqlConnection connection = new SqlConnection(_connectionString);
                        using SqlCommand command = new SqlCommand("GetAllBoardMappings", connection)
                        {
                            CommandType = CommandType.StoredProcedure,
                            CommandTimeout = _commandTimeout // Timeout set to 120 minutes (7200 seconds)
                        };

                        connection.Open();

                        using SqlDataReader reader = command.ExecuteReader();
                        while (reader.Read())
                        {
                            try
                            {
                                boardMappings.Add(new BoardMapping
                                {
                                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                                    Board = reader.GetString(reader.GetOrdinal("Board")),
                                    BoardGroupName = reader.GetString(reader.GetOrdinal("BoardGroupName")),
                                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate")),
                                    LastUpdatedDate = reader.GetDateTime(reader.GetOrdinal("LastUpdatedDate"))
                                });
                            }
                            catch (Exception ex)
                            {
                                var irixErrorEntity = new IrixErrorEntity
                                {
                                    ClassName = _className,
                                    MethodName = nameof(GetAllBoardMappingsAsync),
                                    Params = ex.Message
                                };
                                _log.Error(irixErrorEntity, ex);
                            }
                        }

                        break;
                    }
                }
                catch (SqlException sqlEx) when (sqlEx.Number == -2) // SQL Timeout error code
                {
                    retryCount++;
                    _log.Info($"SQL Timeout occurred in GetAllBoardMappingsAsync (attempt {retryCount}): {sqlEx.Message}");
                    if (retryCount >= maxRetryAttempts)
                    {
                        _log.Info("Max retry attempts reached for GetAllBoardMappingsAsync.");
                        throw; // After max retries, throw the exception
                    }
                    // Wait before retrying (exponential backoff)
                    await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, retryCount)));
                }
                catch (Exception ex)
                {
                    // Log other exceptions and rethrow them
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(GetAllBoardMappingsAsync),
                        Params = ex.Message
                    };
                    _log.Error(irixErrorEntity, ex);
                    throw;
                }
            }

            return boardMappings;
        }

        public int InsertOrUpdateMultiSupplierLog(MultiSupplierlog multiSupplierlog)
        {
            const int maxRetryAttempts = 3;
            int retryCount = 0;
            int rowsAffected = 0;

            while (retryCount < maxRetryAttempts)
            {
                try
                {
                    lock (_lock_InsertOrUpdateMultiSupplierLog)  // Thread-safe lock to serialize access
                    {
                        using SqlConnection connection = new SqlConnection(_connectionString);
                        connection.Open();

                        using SqlCommand command = new SqlCommand("dbo.usp_ins_MultiSupplierLog", connection)
                        {
                            CommandType = CommandType.StoredProcedure,
                            CommandTimeout = _commandTimeout
                        };

                        // Add parameters
                        command.Parameters.AddWithValue("@RePricerId", multiSupplierlog.RePricerId);
                        command.Parameters.AddWithValue("@ReservationId", multiSupplierlog.ReservationId);
                        command.Parameters.AddWithValue("@ReasonCode", multiSupplierlog.ReasonCode);
                        command.Parameters.AddWithValue("@ReasonMessage", multiSupplierlog.ReasonMessage);

                        rowsAffected = command.ExecuteNonQuery();

                        // Exit the retry loop if execution is successful
                        break;
                    }
                }
                catch (SqlException sqlEx) when (sqlEx.Number == -2) // SQL Timeout error code
                {
                    retryCount++;
                    _log.Info($"SQL Timeout occurred in InsertOrUpdateMultiSupplierLog (attempt {retryCount}): {sqlEx.Message}");
                    if (retryCount >= maxRetryAttempts)
                    {
                        _log.Info($"Max retry attempts reached for InsertOrUpdateMultiSupplierLog.");
                        throw; // After max retries, throw the exception
                    }
                    // Wait before retrying (exponential backoff)
                    Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, retryCount))).Wait();
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = nameof(MultiSupplierlog),
                        MethodName = nameof(InsertOrUpdateMultiSupplierLog),
                        Params = ex.Message
                    };
                    _log.Error(irixErrorEntity, ex);
                    throw;
                }
            }

            return rowsAffected;
        }

        public async Task<List<GiataDbResp>> InsertGiataRoomsBulkAsync(List<BulkGiataRoom> rooms)
        {
            var giataRoomMappings = new List<GiataDbResp>();

            try
            {
                DataTable dtRooms = ConvertToDataTable(rooms);

                using (SqlConnection conn = new SqlConnection(_connectionString))
                {
                    await conn.OpenAsync();

                    using (SqlCommand cmd = new SqlCommand("dbo.usp_ins_bulkgiataroom", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        SqlParameter param = new SqlParameter("@GiataRoomDetails", SqlDbType.Structured)
                        {
                            TypeName = "dbo.GiataRoomsType",
                            Value = dtRooms
                        };
                        cmd.Parameters.Add(param);

                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var giataRoomMapping = new GiataDbResp
                                {
                                    MappingId = reader.GetInt32(reader.GetOrdinal("MappingId")),
                                    RoomName = reader.GetString(reader.GetOrdinal("RoomName"))
                                };
                                giataRoomMappings.Add(giataRoomMapping);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(InsertGiataRoomsBulkAsync)
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            return giataRoomMappings;
        }

        private DataTable ConvertToDataTable(List<BulkGiataRoom> rooms)
        {
            DataTable table = new DataTable();

            table.Columns.Add("propertyName", typeof(string));
            table.Columns.Add("groupName", typeof(string));
            table.Columns.Add("groupID", typeof(int));
            table.Columns.Add("roomName", typeof(string));
            table.Columns.Add("groupConfidence", typeof(int));
            table.Columns.Add("bedDetailDescription", typeof(string));
            table.Columns.Add("roomCount", typeof(int));
            table.Columns.Add("accessible", typeof(bool));
            table.Columns.Add("nonRefundable", typeof(bool));
            table.Columns.Add("annex", typeof(bool));
            table.Columns.Add("singleUse", typeof(bool));
            table.Columns.Add("sharedBed", typeof(bool));
            table.Columns.Add("averageRoomType", typeof(string));
            table.Columns.Add("averageRoomClasses", typeof(string));
            table.Columns.Add("averageRoomViews", typeof(string));
            table.Columns.Add("createDate", typeof(DateTime));

            foreach (var room in rooms)
            {
                try
                {
                    table.Rows.Add(room.PropertyName, room.GroupName, room.GroupID, room.RoomName, room.GroupConfidence,
                        room.BedDetailDescription, room.RoomCount, room.Accessible, room.NonRefundable, room.Annex,
                        room.SingleUse, room.SharedBed, room.AverageRoomType, room.AverageRoomClasses, room.AverageRoomViews,
                        room.CreateDate);
                }
                catch (Exception ex)
                {
                }
            }
            return table;
        }

        public async Task<List<DailyOptimizationReportModel>> GetDailyOptimizationReportFromDBAsync(
         int? repricerId,
         string? reportType,
         DateTime? reportDateFrom,
         DateTime? reportDateTo)
        {
            var reports = new List<DailyOptimizationReportModel>();

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new SqlCommand("usp_Get_DailyOptimizationReport", connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            // Add parameters with null handling
            command.Parameters.AddWithValue("@RepricerId", (object?)repricerId ?? DBNull.Value);
            command.Parameters.AddWithValue("@ReportType", (object?)reportType ?? DBNull.Value);
            command.Parameters.AddWithValue("@ReportDateFrom", (object?)reportDateFrom ?? DBNull.Value);
            command.Parameters.AddWithValue("@ReportDateTo", (object?)reportDateTo ?? DBNull.Value);

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                try
                {
                    var report = new DailyOptimizationReportModel
                    {
                        ReportDate = reader.GetDateTime(reader.GetOrdinal("ReportDate")),
                        RepricerId = reader.GetInt32(reader.GetOrdinal("RepricerId")),
                        ReportType = reader.GetString(reader.GetOrdinal("ReportType")),
                        TotalProfitEUR = reader.IsDBNull(reader.GetOrdinal("TotalProfit_EUR")) ? null : reader.GetDecimal(reader.GetOrdinal("TotalProfit_EUR")),
                        ReservationCountSame = reader.IsDBNull(reader.GetOrdinal("ReservationCount_Same")) ? null : reader.GetInt32(reader.GetOrdinal("ReservationCount_Same")),
                        ReservationPriceSame = reader.IsDBNull(reader.GetOrdinal("ReservationPrice_Same")) ? null : reader.GetDecimal(reader.GetOrdinal("ReservationPrice_Same")),
                        PrebookPriceSame = reader.IsDBNull(reader.GetOrdinal("PrebookPrice_Same")) ? null : reader.GetDecimal(reader.GetOrdinal("PrebookPrice_Same")),
                        ProfitSame = reader.IsDBNull(reader.GetOrdinal("Profit_Same")) ? null : reader.GetDecimal(reader.GetOrdinal("Profit_Same")),
                        ReservationCountMulti = reader.IsDBNull(reader.GetOrdinal("ReservationCount_Multi")) ? null : reader.GetInt32(reader.GetOrdinal("ReservationCount_Multi")),
                        ReservationPriceMulti = reader.IsDBNull(reader.GetOrdinal("ReservationPrice_Multi")) ? null : reader.GetDecimal(reader.GetOrdinal("ReservationPrice_Multi")),
                        PrebookPriceMulti = reader.IsDBNull(reader.GetOrdinal("PrebookPrice_Multi")) ? null : reader.GetDecimal(reader.GetOrdinal("PrebookPrice_Multi")),
                        ProfitMulti = reader.IsDBNull(reader.GetOrdinal("Profit_Multi")) ? null : reader.GetDecimal(reader.GetOrdinal("Profit_Multi")),
                        ProfitSamePercentage = reader.IsDBNull(reader.GetOrdinal("Profit_Same_Percentage")) ? null : reader.GetDecimal(reader.GetOrdinal("Profit_Same_Percentage")),
                        ProfitMultiPercentage = reader.IsDBNull(reader.GetOrdinal("Profit_Multi_Percentage")) ? null : reader.GetDecimal(reader.GetOrdinal("Profit_Multi_Percentage")),
                        CombinedProfitPercentage = reader.IsDBNull(reader.GetOrdinal("Combined_Profit_Percentage")) ? null : reader.GetDecimal(reader.GetOrdinal("Combined_Profit_Percentage")),
                        ProfitSameFormula = reader.IsDBNull(reader.GetOrdinal("Profit_Same_Formula")) ? null : reader.GetString(reader.GetOrdinal("Profit_Same_Formula")),
                        ProfitMultiFormula = reader.IsDBNull(reader.GetOrdinal("Profit_Multi_Formula")) ? null : reader.GetString(reader.GetOrdinal("Profit_Multi_Formula")),
                        CombinedProfitPercentageFormula = reader.IsDBNull(reader.GetOrdinal("Combined_Profit_Percentage_Formula")) ? null : reader.GetString(reader.GetOrdinal("Combined_Profit_Percentage_Formula")),
                        ReportDateTime = reader.GetDateTime(reader.GetOrdinal("ReportDateTime")),
                        CreatedOn = reader.GetDateTime(reader.GetOrdinal("CreatedOn")),
                        UpdatedOn = reader.IsDBNull(reader.GetOrdinal("UpdatedOn")) ? null : reader.GetDateTime(reader.GetOrdinal("UpdatedOn"))
                    };

                    reports.Add(report);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = Constant.MasterPersistence,
                        MethodName = nameof(GetDailyOptimizationReportFromDBAsync),
                    };
                    _log.Error(irixErrorEntity, ex);
                }
            }

            return reports;
        }

        /// <summary>
        /// Get additional prebook options (ranks 2-3) from ReservationReportDetailsAdditionalPrebook table
        /// Uses the same view structure as primary prebooks but from the additional prebook table
        /// </summary>
        /// <param name="reservationRequest">The reservation request parameters</param>
        /// <returns>List of additional prebook options</returns>
        public List<DashboardReportResponseRow> GetAdditionalPrebookOptions(RepricerReportRequest reservationRequest)
        {
            var watch = Stopwatch.StartNew();
            var additionalPrebooks = new List<DashboardReportResponseRow>();

            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("usp_get_AdditionalPrebookOptions", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        // Use same parameters as primary prebook query
                        command.Parameters.AddWithValue("@RepricerId", reservationRequest.RepricerId);
                        command.Parameters.AddWithValue("@ReservationId", reservationRequest.ReservationId ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@FromDate", reservationRequest.FromDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@ToDate", reservationRequest.ToDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@PageSize", reservationRequest.PageSize ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@PageNumber", reservationRequest.PageNumber ?? (object)DBNull.Value);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            var masterData = new MasterData();

                            if (reader.HasRows)
                            {
                                // Use the same data mapping logic as primary prebooks
                                additionalPrebooks = masterData.GetReservationReports(reader);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetAdditionalPrebookOptions),
                    RePricerId = reservationRequest.RepricerId
                };
                _log.Error(irixErrorEntity, ex);
                // Return empty list on error to avoid breaking the main flow
                return new List<DashboardReportResponseRow>();
            }

            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            //_log.Info($"MasterPersistence|GetAdditionalPrebookOptions|{elapsedTimeInSeconds}|{reservationRequest.RepricerId} in {watch.Elapsed}");

            return additionalPrebooks;
        }
    }
}