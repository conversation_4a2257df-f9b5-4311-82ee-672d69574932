using Autofac;
using AutoFixture;
using Irix.ServiceAdapter.Irix.Commands.Contracts;
using Irix.Entities;
using Irix.ServiceAdapters;
using Irix.ServiceAdapters.Irix.Commands.Contracts;
using Irix.ServiceAdapters.Irix.Entities;
using RePricer.Register;
using Xunit;
using Irix.Service.Contract;
using System.Text.Json;
using FluentAssertions;

namespace Irix.TestApi
{
    public class UnitTest1 : BaseTest
    {
        private readonly IGetAllBookingCmdHandler _cmdHandler;
        private readonly Fixture _fixture = new Fixture();
        private readonly IIrixAdapter _irixAdapter;
        private readonly ISearchBookingCmdHandler _searchcmdHandler;
        private readonly IPreBookingCmdHandler _prebookcmdHandler;
        private readonly IReservationService _reservationService;
        private readonly ISearchService _searchService;
        private readonly IMasterService _masterService;




        public UnitTest1()
        {
            _cmdHandler = _container.Resolve<IGetAllBookingCmdHandler>();
            _searchcmdHandler = _container.Resolve<ISearchBookingCmdHandler>();
            _prebookcmdHandler = _container.Resolve<IPreBookingCmdHandler>();
            _irixAdapter = _container.Resolve<IIrixAdapter>();
            _reservationService = _container.Resolve<IReservationService>();
            _searchService = _container.Resolve<ISearchService>();
            _masterService = _container.Resolve<IMasterService>();

        }


        [Fact]
        public  void Test1()
        {
            using (var scope = _container.BeginLifetimeScope())
            {

                var reservationRequest = new ReservationRequest
                {
                    Service = "hotel",
                    CancelPenalty = "no",
                    Page = 1
                    // Add other properties as needed
                };

                var result =  _cmdHandler.Executes(reservationRequest, MethodType.GetAllBookings, out var request, out var response,1, 3);


            }
        }
        [Fact]
        public void Test2()
        {
            using (var scope = _container.BeginLifetimeScope())
            {
                // Arrange
                //ReservationRequest reservationRequest = _fixture.Create<ReservationRequest>();
                var reservationRequest = new ReservationRequest
                {
                    Service = "hotel",
                    CancelPenalty = "no",
                    Page = 1
                    // Add other properties as needed
                };

                var result = _irixAdapter.GetBookingDetails(reservationRequest, "GetAllBookings",3);



            }
        }

        [Fact]
        public void Test3()
        {
            using (var scope = _container.BeginLifetimeScope())
            {
                var reservationRequest = new SearchRequest
                {
                    accommodation = new List<int> { 34901 },
                    checkIn = "2023-10-17",
                    checkOut = "2023-10-22",
                    occupancy = new OccupancyInfo
                    {
                        leaderNationality = 182,
                        rooms = new List<RoomInfo>
                {
                    new RoomInfo
                    {
                        adults = 1,
                        childrenAges = new List<int> { 8 }
                    }
                }
                    },
                    language = "en_GB",
                    timeout = 10,
                    sellingChannel = "B2B"
                };

                var result = _searchcmdHandler.Executes(reservationRequest, MethodType.SearchForBooking, out var request, out var response,0,3);


            }
        }

        [Fact]
        public void Test4()
        {
            using (var scope = _container.BeginLifetimeScope())
            {

                var reservationRequest = new SearchRequest
                {
                    accommodation = new List<int> { 53902 },
                    checkIn = "2023-09-02",
                    checkOut = "2023-09-05",
                    occupancy = new OccupancyInfo
                    {
                        leaderNationality = 182,
                        rooms = new List<RoomInfo>
                {
                    new RoomInfo
                    {
                        adults = 1,
                        childrenAges = new List<int> { 0 }
                    }
                }
                    },
                    language = "en_GB",
                    timeout = 10,
                    sellingChannel = "B2B"
                };

                var result = _irixAdapter.SearchForBooking(010,reservationRequest, "GetAllBookings",3);
            }
        }
        [Fact]
        public void Test7()
        {
            using (var scope = _container.BeginLifetimeScope())
            {



                _searchService._1_PrebookAndOptimize_SameSupplier_Automatic(3);


            }
        }

        /// <summary>
        /// Test multiple prebook functionality - GetAdditionalPrebookOptions method
        /// </summary>
        [Fact]
        public void Test_GetAdditionalPrebookOptions_ShouldReturnRanks2And3()
        {
            using (var scope = _container.BeginLifetimeScope())
            {
                // Arrange
                var reservationRequest = new RepricerReportRequest
                {
                    RepricerId = 99, // Use test repricer ID
                    ReservationId = null, // Get all reservations
                    ReportType = "prebook",
                    FromDate = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd"),
                    ToDate = DateTime.Now.ToString("yyyy-MM-dd"),
                    PageSize = 10,
                    PageNumber = 1
                };

                // Act
                var additionalPrebooks = _masterService.GetAdditionalPrebookOptions(reservationRequest);

                // Assert
                additionalPrebooks.Should().NotBeNull();
                // Additional prebooks should only contain ranks 2 and 3
                if (additionalPrebooks.Any())
                {
                    additionalPrebooks.All(p => p.Rank == 2 || p.Rank == 3).Should().BeTrue();
                    // Should be ordered by reservation ID and rank
                    var orderedPrebooks = additionalPrebooks.OrderBy(p => p.ReservationId).ThenBy(p => p.Rank).ToList();
                    additionalPrebooks.Should().BeEquivalentTo(orderedPrebooks);
                }
            }
        }

        /// <summary>
        /// Test multiple prebook functionality - CombinePrebookOptions method
        /// </summary>
        [Fact]
        public void Test_CombinePrebookOptions_ShouldAddToPrebookList()
        {
            using (var scope = _container.BeginLifetimeScope())
            {
                // Arrange - Create mock primary response
                var primaryResponse = new ReservationReportResponse
                {
                    Data = new RepricerReportResponse
                    {
                        ReservationReports = new List<DashboardReportResponseRow>
                        {
                            new DashboardReportResponseRow
                            {
                                ReservationId = 12345,
                                RepricerId = 99,
                                Prebook = new ReservationAndPreBookCompare
                                {
                                    Rank = 1,
                                    Price = 100.00m,
                                    Supplier = "Supplier1"
                                }
                            }
                        }
                    }
                };

                // Create mock additional prebooks
                var additionalPrebooks = new List<ReservationAndPreBookCompare>
                {
                    new ReservationAndPreBookCompare
                    {
                        ReservationId = 12345,
                        RepricerId = 99,
                        Rank = 2,
                        Price = 95.00m,
                        Supplier = "Supplier2"
                    },
                    new ReservationAndPreBookCompare
                    {
                        ReservationId = 12345,
                        RepricerId = 99,
                        Rank = 3,
                        Price = 90.00m,
                        Supplier = "Supplier3"
                    }
                };

                // Act
                var combinedResponse = _masterService.CombinePrebookOptions(primaryResponse, additionalPrebooks, "prebook");

                // Assert
                combinedResponse.Should().NotBeNull();
                combinedResponse.Data.ReservationReports.Should().HaveCount(1);

                var reservationReport = combinedResponse.Data.ReservationReports.First();

                // For "prebook" report type, Prebooks should contain all options (ranks 1, 2, 3)
                reservationReport.Prebooks.Should().NotBeNull();
                reservationReport.Prebooks.Should().HaveCount(3); // 1 primary + 2 additional

                // Check ranks
                reservationReport.Prebooks[0].Rank.Should().Be(1);
                reservationReport.Prebooks[1].Rank.Should().Be(2);
                reservationReport.Prebooks[2].Rank.Should().Be(3);
            }
        }

        /// <summary>
        /// Test end-to-end multiple prebook functionality
        /// </summary>
        [Fact]
        public void Test_MultiplePrebookEndToEnd_ShouldReturnArrayOfPrebooks()
        {
            using (var scope = _container.BeginLifetimeScope())
            {
                // Arrange
                var reservationRequest = new RepricerReportRequest
                {
                    RepricerId = 99, // Use test repricer ID
                    ReservationId = null,
                    ReportType = "prebook",
                    FromDate = DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd"),
                    ToDate = DateTime.Now.ToString("yyyy-MM-dd"),
                    PageSize = 5,
                    PageNumber = 1
                };

                // Act - Get primary prebooks
                var primaryResponse = _masterService.GetReservationReport(reservationRequest);

                if (primaryResponse?.Data?.ReservationReports?.Any() == true)
                {
                    // Get additional prebooks
                    var additionalPrebooks = _masterService.GetAdditionalPrebookOptions(reservationRequest);

                    // Combine them
                    var combinedResponse = _masterService.CombinePrebookOptions(primaryResponse, additionalPrebooks, "prebook");

                    // Assert
                    combinedResponse.Should().NotBeNull();
                    combinedResponse.Data.ReservationReports.Should().NotBeEmpty();

                    foreach (var reservationReport in combinedResponse.Data.ReservationReports)
                    {
                        // For "prebook" report type, Prebooks should contain all options
                        reservationReport.Prebooks.Should().NotBeNull();
                        reservationReport.Prebooks.Should().NotBeEmpty();

                        // Should have at least the primary prebook (rank 1)
                        reservationReport.Prebooks.Should().HaveCountGreaterOrEqualTo(1);
                        // Should have at most 3 prebooks (ranks 1, 2, 3)
                        reservationReport.Prebooks.Should().HaveCountLessOrEqualTo(3);

                        // Check that ranks are properly set
                        reservationReport.Prebooks[0].Rank.Should().Be(1);
                    }

                    // Log the result for manual verification
                    var jsonResult = JsonSerializer.Serialize(combinedResponse, new JsonSerializerOptions { WriteIndented = true });
                    System.Diagnostics.Debug.WriteLine("Multiple Prebook Result:");
                    System.Diagnostics.Debug.WriteLine(jsonResult);
                }
            }
        }

        /// <summary>
        /// Test that diversity filtering works correctly
        /// </summary>
        [Fact]
        public void Test_DiversityFiltering_ShouldReturnDifferentSuppliersAndCancellationDates()
        {
            using (var scope = _container.BeginLifetimeScope())
            {
                // Arrange
                var reservationRequest = new RepricerReportRequest
                {
                    RepricerId = 99,
                    ReservationId = null,
                    ReportType = "prebook",
                    FromDate = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd"),
                    ToDate = DateTime.Now.ToString("yyyy-MM-dd"),
                    PageSize = 20,
                    PageNumber = 1
                };

                // Act
                var additionalPrebooks = _masterService.GetAdditionalPrebookOptions(reservationRequest);

                // Assert - Check diversity filtering
                if (additionalPrebooks.Any())
                {
                    // Group by reservation ID
                    var groupedByReservation = additionalPrebooks.GroupBy(p => p.ReservationId);

                    foreach (var reservationGroup in groupedByReservation)
                    {
                        var prebooks = reservationGroup.OrderBy(p => p.Rank).ToList();

                        if (prebooks.Count > 1)
                        {
                            // Check that suppliers are different (diversity requirement)
                            var suppliers = prebooks.Select(p => p.Supplier).Distinct().ToList();
                            suppliers.Should().HaveCountGreaterThan(1, "Additional prebooks should have different suppliers");

                            // Check that cancellation dates are different (diversity requirement)
                            var cancellationDates = prebooks
                                .Select(p => DateTime.TryParse(p.CancellationPolicyStartDate, out var date) ? date.Date : (DateTime?)null)
                                .Where(d => d.HasValue)
                                .Distinct()
                                .ToList();

                            if (cancellationDates.Count > 1)
                            {
                                cancellationDates.Should().HaveCountGreaterThan(1, "Additional prebooks should have different cancellation dates");
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Test that for non-prebook report types, the old mechanism still works
        /// </summary>
        [Fact]
        public void Test_NonPrebookReportType_ShouldUseOldMechanism()
        {
            using (var scope = _container.BeginLifetimeScope())
            {
                // Arrange - Create mock primary response
                var primaryResponse = new ReservationReportResponse
                {
                    Data = new RepricerReportResponse
                    {
                        ReservationReports = new List<DashboardReportResponseRow>
                        {
                            new DashboardReportResponseRow
                            {
                                ReservationId = 12345,
                                RepricerId = 99,
                                Prebook = new ReservationAndPreBookCompare
                                {
                                    Rank = 1,
                                    Price = 100.00m,
                                    Supplier = "Supplier1"
                                }
                            }
                        }
                    }
                };

                // Create mock additional prebooks
                var additionalPrebooks = new List<ReservationAndPreBookCompare>
                {
                    new ReservationAndPreBookCompare
                    {
                        ReservationId = 12345,
                        RepricerId = 99,
                        Rank = 2,
                        Price = 95.00m,
                        Supplier = "Supplier2"
                    }
                };

                // Act - Test with "active" report type (not "prebook")
                var combinedResponse = _masterService.CombinePrebookOptions(primaryResponse, additionalPrebooks, "active");

                // Assert - For non-prebook report types, Prebooks should remain null/empty
                combinedResponse.Should().NotBeNull();
                combinedResponse.Data.ReservationReports.Should().HaveCount(1);

                var reservationReport = combinedResponse.Data.ReservationReports.First();

                // Prebook should remain as single object (old mechanism)
                reservationReport.Prebook.Should().NotBeNull();
                reservationReport.Prebook.Rank.Should().Be(1);

                // Prebooks should remain null/empty for non-prebook report types
                reservationReport.Prebooks.Should().BeNullOrEmpty();
            }
        }


    }
}
