<template>
    <div class="align-items-center border-1 border-primary d-flex gap-2">
        <span class="align-items-center bg-primary d-flex fs-3 gap-2 justify-content-between px-3 py-1">
            <i class="pi pi-briefcase fs-3"></i>
            Cron Job Time
        </span>
        <div>
            <div class="fw-500">Next: {{ nextRun }}</div>
            <div class="fw-500">Last: {{ lastRun }}</div>
        </div>
    </div>
</template>
<script>
import cronParser from 'cron-parser';
import { useRepricerStore } from '@/stores/useRepricerStore';
import moment from 'moment';
import { getUserTimezone } from '@/helpers/utils';

export default {
    name: "JobTime",
    components: {},
    data() {
        return {
            cronExpression: '',
            lastRun: '',
            nextRun: '',

        };
    },
    props: [],
    watch: {},
    computed: {
    },
    async mounted() {
        this.getPrevAndNextRunTimes();
    },
    methods: {
        async calculateRunTimes() {
            try {
                const repricerInfo = useRepricerStore();
                const timezones = await repricerInfo.GetTimeZones();

                this.cronExpression = repricerInfo.repricer.clientConfigScheduler.preBook_CronTime;
                let timezone = repricerInfo.repricer.clientConfigScheduler.timeZoneId;
                if (this.$route.params.repricerId && this.$route.params.repricerId != repricerInfo.repricer.repricerUserID) {
                    const rePricerConfig = await repricerInfo.getRePricerById(this.$route.params.repricerId);
                    this.cronExpression = rePricerConfig.clientConfigScheduler.preBook_CronTime;
                    timezone = rePricerConfig.clientConfigScheduler.timeZoneId;
                }

                const selectedTimezone = timezones.find(tz => tz.timeZoneId == timezone);
                const utc = selectedTimezone.timeZoneName.split(')')[0].replace('(', '');

                const options = {
                    currentDate: new Date(),
                    tz: utc, // Set your desired timezone here
                };
                const interval = cronParser.parseExpression(this.cronExpression, options);

                const optionsLocal = {
                    currentDate: new Date(),
                    tz: getUserTimezone(), // Set your desired timezone here
                };
                const intervalLocal = cronParser.parseExpression(this.cronExpression, optionsLocal);

                // Get the next run date and time
                this.nextRun = {
                    original: moment(interval.next().toISOString()).format('MMMM Do YYYY, h:mm:ss a'),
                    local: moment(intervalLocal.next().toISOString()).format('MMMM Do YYYY, h:mm:ss a')
                };

                // Get the last run date and time
                interval.prev(); // Move back to current date
                intervalLocal.prev(); // Move back to current date
                this.lastRun = {
                    original: moment(interval.prev().toISOString()).format('MMMM Do YYYY, h:mm:ss a'),
                    local: moment(intervalLocal.prev().toISOString()).format('MMMM Do YYYY, h:mm:ss a')
                }
            } catch (err) {
                console.error('Error parsing cron expression:', err);
            }
        },
        async getPrevAndNextRunTimes() {
            const numberOfNextRuns = 2;
            const repricerInfo = useRepricerStore();
            const options = {
                currentDate: new Date(), // Start from the current date
                tz: getUserTimezone() // Set timezone if needed (optional)
            };

            this.cronExpression = repricerInfo.repricer.clientConfigScheduler.preBook_CronTime;
            if (this.$route.params.repricerId && this.$route.params.repricerId != repricerInfo.repricer.repricerUserID) {
                const rePricerConfig = await repricerInfo.getRePricerById(this.$route.params.repricerId);
                this.cronExpression = rePricerConfig.clientConfigScheduler.preBook_CronTime;
            }

            const interval = cronParser.parseExpression(this.cronExpression, options);
            const runTimes = [];

            // Get the next run times
            for (let i = 0; i < numberOfNextRuns; i++) {
                runTimes.push(moment(interval.next().toISOString()).format('MMMM Do YYYY, h:mm:ss A'));
            }

            // Go back to the previous execution time
            interval.prev(); // Move back to current date
            const prevRun = moment(interval.prev().toISOString()).format('MMMM Do YYYY, h:mm:ss A');

            this.lastRun = prevRun;
            this.nextRun = runTimes[0];

            return {
                prevRun,
                nextRuns: runTimes
            };
        }
    }
}

</script>
