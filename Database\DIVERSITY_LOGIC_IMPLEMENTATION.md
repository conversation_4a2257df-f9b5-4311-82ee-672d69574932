# 🎯 **DIVERSITY LOGIC IMPLEMENTATION: Meaningful Prebook Differentiation**

## **📋 OVERVIEW**

This document outlines the implementation of **diversity filters** to ensure that ranks 2 and 3 prebook options are **meaningfully different** from the primary (rank 1) prebook, avoiding duplicate or near-identical options.

---

## **🔍 PROBLEM STATEMENT**

### **❌ BEFORE (Potential Issue)**
```sql
-- COULD SELECT SIMILAR OPTIONS:
Rank 1: didatravel, €25.50, 2025-12-11 (cancellation)
Rank 2: didatravel, €25.50, 2025-12-11 (cancellation)  -- SAME SUPPLIER & DATA
Rank 3: didatravel, €25.49, 2025-12-11 (cancellation)  -- MINIMAL DIFFERENCE
```

### **✅ AFTER (Desired Outcome)**
```sql
-- DIVERSE OPTIONS:
Rank 1: didatravel, €25.50, 2025-12-11 (cancellation)
Rank 2: sunhotels,  €23.20, 2025-12-12 (cancellation)  -- DIFFERENT SUPPLIER
Rank 3: booking,    €21.80, 2025-12-10 (cancellation)  -- DIFFERENT SUPPLIER + PRICE
```

---

## **🎯 DIVERSITY CRITERIA**

### **📊 KEY DIFFERENTIATORS (Priority Order)**

| **Priority** | **Field** | **Criteria** | **Threshold** |
|-------------|-----------|--------------|---------------|
| **1 (Highest)** | **Supplier** | `PrebookProviders` | Must be different |
| **2** | **Profit** | `Profit` | >€1.00 difference |
| **3** | **Cancellation Date** | `MatchedPreBookCancellationDate` | >0 days difference |
| **4** | **Price** | `PreBookPrice` | >€5.00 difference |

### **🔧 DIVERSITY FILTER LOGIC**
```sql
-- DIVERSITY FILTERS: Ensure meaningful differences from primary prebook
AND (
    t.PrebookProviders != p.PrimarySupplier                                    -- Different supplier (MOST IMPORTANT)
    OR ABS(t.Profit - p.PrimaryProfit) > 1.0                                 -- Significantly different profit (>€1)
    OR ABS(DATEDIFF(day, t.MatchedPreBookCancellationDate, p.PrimaryCancellationDate)) > 0  -- Different cancellation date
    OR ABS(t.PreBookPrice - p.PrimaryPrice) > 5.0                           -- Significantly different price (>€5)
)
```

---

## **🔧 IMPLEMENTATION APPROACH**

### **📋 MULTI-STEP SELECTION PROCESS**

#### **STEP 1: Capture Primary Prebook Characteristics**
```sql
-- Create temporary table to store rank 1 characteristics
CREATE TABLE #temp_PrimaryPrebooks_Characteristics
(
    ReservationId INT,
    RepricerId INT,
    PrimarySupplier VARCHAR(255),
    PrimaryProfit DECIMAL(18,5),
    PrimaryCancellationDate DATETIME,
    PrimaryPrice DECIMAL(18,5)
);

-- Populate with rank 1 data using same business logic
INSERT INTO #temp_PrimaryPrebooks_Characteristics
SELECT DISTINCT
    t1.ReservationId,
    t1.RepricerId,
    t1.PrebookProviders as PrimarySupplier,
    t1.Profit as PrimaryProfit,
    t1.MatchedPreBookCancellationDate as PrimaryCancellationDate,
    t1.PreBookPrice as PrimaryPrice
FROM (
    SELECT ROW_NUMBER() OVER ([SAME RANKING LOGIC]) as rn, *
    FROM #temp_ActiveTab
    WHERE [SAME FILTERS]
) t1
WHERE t1.rn = 1;  -- GET RANK 1 CHARACTERISTICS
```

#### **STEP 2: Select Diverse Additional Prebooks**
```sql
-- Select ranks 2-3 with diversity filters applied
SELECT *
into #temp_OrderedLogs
From
(
    SELECT ROW_NUMBER() OVER ([SAME RANKING LOGIC]) as rn, *
    FROM #temp_ActiveTab t
    INNER JOIN #temp_PrimaryPrebooks_Characteristics p 
        ON t.RepricerId = p.RepricerId AND t.ReservationId = p.ReservationId
    WHERE [SAME BASE FILTERS]
      -- DIVERSITY FILTERS APPLIED HERE
      AND (
          t.PrebookProviders != p.PrimarySupplier                    -- Different supplier
          OR ABS(t.Profit - p.PrimaryProfit) > 1.0                 -- Different profit
          OR ABS(DATEDIFF(day, t.MatchedPreBookCancellationDate, p.PrimaryCancellationDate)) > 0  -- Different cancellation
          OR ABS(t.PreBookPrice - p.PrimaryPrice) > 5.0            -- Different price
      )
) AS with_row_numbers
WHERE rn BETWEEN 2 AND 3;  -- SELECT DIVERSE RANKS 2 AND 3
```

---

## **📊 BUSINESS LOGIC PRESERVATION**

### **✅ MAINTAINED ELEMENTS**

1. **✅ Same Day Filter**: `CAST(CreateDate AS DATE) = @CurrentDate`
2. **✅ Optimization Exclusion**: `ISNULL(bat_NewBookingId, 0) = 0`
3. **✅ Ranking Logic**: Identical ORDER BY criteria
4. **✅ Business Rules**: All original filters preserved
5. **✅ Performance**: Efficient with proper indexing

### **🆕 ENHANCED ELEMENTS**

1. **🆕 Diversity Validation**: Multi-criteria differentiation
2. **🆕 Supplier Diversity**: Prioritizes different suppliers
3. **🆕 Value Differentiation**: Ensures meaningful profit/price differences
4. **🆕 Temporal Diversity**: Different cancellation policy dates

---

## **🎯 EXPECTED OUTCOMES**

### **📈 DIVERSITY SCENARIOS**

#### **Scenario A: High Diversity Available**
```sql
-- INPUT DATA:
Reservation 12345: 5 prebook options from 4 different suppliers

-- OUTPUT:
Rank 1: didatravel, €25.50, 2025-12-11
Rank 2: sunhotels,  €23.20, 2025-12-12  -- Different supplier + profit + date
Rank 3: booking,    €21.80, 2025-12-10  -- Different supplier + profit + date
```

#### **Scenario B: Limited Diversity**
```sql
-- INPUT DATA:
Reservation 12346: 3 prebook options, 2 from same supplier

-- OUTPUT:
Rank 1: didatravel, €25.50, 2025-12-11
Rank 2: sunhotels,  €23.20, 2025-12-12  -- Different supplier
Rank 3: didatravel, €20.00, 2025-12-11  -- Same supplier but €5.50 profit difference
```

#### **Scenario C: Insufficient Diversity**
```sql
-- INPUT DATA:
Reservation 12347: 2 prebook options, both very similar

-- OUTPUT:
Rank 1: didatravel, €25.50, 2025-12-11
Rank 2: sunhotels,  €23.20, 2025-12-12  -- Different supplier (meets criteria)
-- No Rank 3: Insufficient diverse options
```

---

## **⚡ PERFORMANCE CONSIDERATIONS**

### **🔧 OPTIMIZATION STRATEGIES**

1. **Efficient Joins**: Using INNER JOIN on indexed columns (RepricerId, ReservationId)
2. **Minimal Data**: Temporary table stores only essential characteristics
3. **Early Filtering**: Diversity filters applied during selection, not post-processing
4. **Index Usage**: Leverages existing indexes on #temp_ActiveTab

### **📊 PERFORMANCE IMPACT**

| **Operation** | **Before** | **After** | **Impact** |
|---------------|------------|-----------|------------|
| **Temp Tables** | 1 | 2 | +1 table (minimal) |
| **Data Scans** | 1 | 2 | +1 scan (same data) |
| **Filtering** | Basic | Enhanced | +Diversity logic |
| **Overall** | Fast | Fast+ | Minimal impact |

---

## **🧪 TESTING SCENARIOS**

### **Test Case 1: Supplier Diversity**
```sql
-- Verify different suppliers are selected
SELECT DISTINCT PrebookSupplier, PrebookRank 
FROM ReservationReportDetailsAdditionalPrebook 
WHERE ReservationId = 12345
-- Expected: 2-3 different suppliers
```

### **Test Case 2: Profit Diversity**
```sql
-- Verify meaningful profit differences
SELECT 
    a.Profit as AdditionalProfit,
    p.Profit as PrimaryProfit,
    ABS(a.Profit - p.Profit) as ProfitDifference
FROM ReservationReportDetailsAdditionalPrebook a
JOIN ReservationReportDetails p ON a.PrimaryPrebookId = p.PreBookId
-- Expected: ProfitDifference > 1.0 OR different suppliers
```

### **Test Case 3: Edge Cases**
```sql
-- Test insufficient diversity scenarios
SELECT COUNT(*) as AdditionalPrebooksCount
FROM ReservationReportDetailsAdditionalPrebook 
WHERE ReservationId = 12347
-- Expected: 0-2 (depending on available diversity)
```

---

## **✅ IMPLEMENTATION STATUS**

**Status**: ✅ **IMPLEMENTED AND READY**

The diversity logic has been successfully integrated into `usp_upd_reservationreport_AdditionalPrebook` stored procedure with:

1. **✅ Multi-step selection process**
2. **✅ Comprehensive diversity filters**
3. **✅ Business logic preservation**
4. **✅ Performance optimization**
5. **✅ Edge case handling**

**Next Steps**: Deploy and test with real data to validate diversity effectiveness.
