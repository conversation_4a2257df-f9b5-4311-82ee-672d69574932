﻿using Irix.Entities;

namespace Irix.Service.Contract
{
    public interface IMasterService
    {
        Task<decimal> GetConversionFactorAsync(string baseCurrencyCode, string currencyCode);

        void HandleRepricerId(UserMapping usersData, PrebookRequest prebookRequest);

        string GenerateCacheKey(string prefix, PrebookRequest prebookRequest);

        //PreBookResult GetOrCalculatePrebookResult(string cacheKey, PrebookRequest prebookRequest, Func<PrebookRequest, PreBookResult> getResultFunc);
        void PopulateActionsTaken(PreBookResult result, PrebookRequest prebookRequest);

        void SaveReportDatatoRedis(int RePricerId);

        decimal getrealizedgain(int repricerid);

        void PopulateMaxPrice(PreBookResult result, PrebookRequest prebookRequest, string procedurename);

        ReservationReportResponse GetReservationReport(RepricerReportRequest reservationRequest);

        bool PopulateActionsTaken(List<DashboardReportResponseRow> result, PrebookRequest prebookRequest, string reportKey = null, RepricerReportResponse repricerReportResponse = null);

        void PopulateMaxPrice(List<DashboardReportResponseRow> result, PrebookRequest prebookRequest, string procedurename);

        void RefreshCache(int RepricerId);

        void RefreshCacheTrigger(int RepricerId, bool isCacheRefresh = false);

        void InsertReportinTable(int RepricerId);

        RepricerReportResponse GetInvoiceData(CommonReportRequest reservationRequest, RePricerDetail rePricerDetail = null);

        ReservationReportResponse CalculatedReservationReport(string currency, decimal factor, ReservationReportResponse reservationReportResponse, decimal factorOriginal);

        ReservationReportCalculation CalculatedReservationReportSummary(string currency, decimal factor, ReservationReportCalculation reservationReportResponse, decimal factorOriginal);

        RepricerInvoiceResponse GetRepricerInvoiceResponse(string currency, decimal factor, RepricerInvoiceResponse reservationinvoiceresponse, decimal factorOriginal);

        ReservationReportCalculation AggregateResults(ReservationReportCalculation report, ReservationReportCalculation newReport);

        ReservationReportCalculation SuperAdminReservationReportSummary(DashboardSummaryRequest reservationReportRequest);

        List<ReservationReportCalculation> SuperAdminReservationReport_SummaryNew(DashboardSummaryRequest reservationReportRequest);

        Task<GiataMappingRoomDetails> GetGiataRoomDetailsAsync(string propertyName, string roomName, int repricerId, string reservationId, bool iscacherefresh = false, string providers = null);

        void MultiSupplierPreBooklog(MultiSupplierlog multiSupplierlog);

        Task<List<BoardMapping>> GetAllBoardMappingsAsync(bool isCacheRefresh = false);

        Task<List<GiataMappingRoomDetails>> GetAllRoomDetailsAsync(bool isCacheRefresh = false);

        Task<bool> AddGiataRoomDetailsToCachedList(
             GiataMappingRoomDetails giataMappingRoomDetails);

        Task<GiataMappingRoomDetails> GetGiataRoomDetailsFromCachedList(
        string propertyName,
        string roomName,
        int repricerId,
        string reservationId,
        bool isCacheRefresh = false,
        string providers = null);

        string GetBoardGroupName(string boardName, bool isCacheRefresh = false);

        Task<RepricerReportResponse> GetReservationReportAndUpdateAction(RepricerReportRequest reservationRequest);

        Task<List<string>> DeleteMemoryCacheKeysAsync(string pattern);

        Task<List<string>> DeleteMemoryCacheKeyByRepricerId(int RepricerId = 0);

        Task<List<GiataDbResp>> InsertGiataRoomsBulkAsync(List<BulkGiataRoom> rooms);

        Task<List<DailyOptimizationReportModel>> GetDailyOptimizationReportAsync(
        int? repricerId,
        string? reportType,
        DateTime? reportDateFrom,
        DateTime? reportDateTo,
        bool isCacheRefresh = false);

        void RefreshDbAndCachedReport(int repricerId, int reservationId, bool isSameSupplier);

        /// <summary>
        /// Get additional prebook options (ranks 2-3) for multiple prebook display
        /// Returns ReservationAndPreBookCompare objects that can be added to the prebook list
        /// </summary>
        /// <param name="reservationRequest">The reservation request parameters</param>
        /// <returns>List of additional prebook options as ReservationAndPreBookCompare</returns>
        List<ReservationAndPreBookCompare> GetAdditionalPrebookOptions(RepricerReportRequest reservationRequest);

        /// <summary>
        /// Combine primary prebook response with additional prebook options
        /// For "prebook" report type, populates Prebooks with all options (ranks 1, 2, 3)
        /// For other report types, uses existing mechanism
        /// </summary>
        /// <param name="primaryResponse">Primary reservation report response</param>
        /// <param name="additionalPrebooks">Additional prebook options</param>
        /// <param name="reportType">Report type to determine behavior (case insensitive)</param>
        /// <returns>Combined response with multiple prebook options</returns>
        ReservationReportResponse CombinePrebookOptions(ReservationReportResponse primaryResponse, List<ReservationAndPreBookCompare> additionalPrebooks, string reportType = null);
    }
}