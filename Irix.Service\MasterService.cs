﻿using Azure.Core;
using Irix.Entities;
using Irix.Persistence.Contract;
using Irix.Service.Contract;
using Logger.Contract;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Repricer.Cache;
using Repricer.Util;
using RePricer.Util;
using System.Collections.Concurrent;
using System.Data;
using System.Globalization;
using constant = RePricer.Constants.ServiceConstants;

using Constant = RePricer.Constants.ServiceConstants;

using Constants = RePricer.Constants.UserControllerConstant;

namespace Irix.Service
{
    public class MasterService : IMasterService
    {
        private readonly IMasterPersistence _masterPersistence;
        private readonly ILogger _log;
        private readonly IClientPersistance _clientPersistence;
        private readonly IReservationPersistence _reservationPersistence;

        private readonly IGiataService _giataService;
        private string _className;
        private readonly IMemoryCache _memoryCache;
        private readonly object _lock_GetAllRoomDetailsAsync = new object();
        private readonly object _lock_GetAllBoardMappingsAsync = new object();
        private readonly object _lock_SA_RefreshCacheData = new object();
        private bool _isRunning_SA_RefreshCacheData = false;
        private readonly TimeSpan _memoryCacheTimeSpan = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _redisCacheTimeSpan = TimeSpan.FromDays(5);

        public MasterService
        (
            IMasterPersistence masterPersistence
            , ILogger log, IClientPersistance clientPersistence
            , IReservationPersistence reservationPersistence
            , IMemoryCache memoryCache
            , IGiataService giataService
        )
        {
            _masterPersistence = masterPersistence;
            _log = log;
            _clientPersistence = clientPersistence;
            _reservationPersistence = reservationPersistence;
            _className = nameof(MasterService);
            _memoryCache = memoryCache;
            _giataService = giataService;

            _redisCacheTimeSpan = TimeSpan.FromDays(5);
            _memoryCacheTimeSpan = TimeSpan.FromMinutes(5);
        }

        public async Task<decimal> GetConversionFactorAsync(string baseCurrencyCode, string currencyCode)
        {
            decimal conversionFactorWithBuffer = 1;
            try
            {
                if ((baseCurrencyCode + "").Trim().ToLowerInvariant().Equals(currencyCode.Trim().ToLowerInvariant()))
                    return conversionFactorWithBuffer;

                var exchangeRateData = _masterPersistence.LoadExchangeRates().AsEnumerable()
                                        .FirstOrDefault(x => x.FromCurrencyCode.ToLower().Equals((baseCurrencyCode + "").ToLowerInvariant())
                                             && x.ToCurrencyCode.ToLower().Equals(currencyCode.ToLowerInvariant()));

                if (exchangeRateData != null)
                {
                    conversionFactorWithBuffer = exchangeRateData.ExchangeRate;

                    if ((
                            currencyCode.ToUpperInvariant().Equals("NZD")
                        || currencyCode.ToUpperInvariant().Equals("SGD")
                        || currencyCode.ToUpperInvariant().Equals("HKD")
                        || currencyCode.ToUpperInvariant().Equals("MYR")
                        || currencyCode.ToUpperInvariant().Equals("BRL")
                        || currencyCode.ToUpperInvariant().Equals("INR"))
                        && ConfigurationManagerHelper.GetValuefromAppSettings(Constant.ExchangeRateBuffer) != null)
                    {
                        if (int.TryParse(ConfigurationManagerHelper.GetValuefromAppSettings(Constant.ExchangeRateBuffer), out var exchangeRateBuffer))
                        {
                            var factor = 100 + exchangeRateBuffer;
                            conversionFactorWithBuffer = conversionFactorWithBuffer * factor / 100;
                            return conversionFactorWithBuffer;
                        }
                    }
                }
                return await Task.FromResult(conversionFactorWithBuffer);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = "MasterService",
                    MethodName = "GetConversionFactor"
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public void HandleRepricerId(UserMapping usersData, PrebookRequest prebookRequest)
        {
            if (usersData.Roles.Any(role => role != Constants.SuperAdmin))
            {
                if (prebookRequest.RepricerId != 0)
                {
                    if (prebookRequest.RepricerId != usersData.RepricerUserId)
                    {
                        throw new Exception(Constants.AdminError);
                    }
                }
                else
                {
                    prebookRequest.RepricerId = usersData.RepricerUserId;
                }
            }
        }

        public void SaveReportDatatoRedis(int RePricerId)
        {
            GetResult(_masterPersistence.GetPreBookResult, RePricerId, "GetPrebook");
            GetResult(_masterPersistence.GetRecommendationEdgeCaseCP, RePricerId, "GetRecommendationEdgeCaseCP");
            GetResult(_masterPersistence.GetRecommendationEdgeCasePR, RePricerId, "GetRecommendationEdgeCasePR");
            DeleteMaxPriceData(RePricerId);
        }

        public void DeleteMaxPriceData(int RePricerId)
        {
            var cachekeymaxprofit = $"{Constants.PopulateMaxPrice}_{RePricerId}";
            if (RedisCacheHelper.KeyExists(cachekeymaxprofit))
            {
                RedisCacheHelper.KeyDelete(cachekeymaxprofit);
            }
            var cachekeymaxprofitCP = $"{Constants.PopulateMaxPriceCP}_{RePricerId}";
            if (RedisCacheHelper.KeyExists(cachekeymaxprofitCP))
            {
                RedisCacheHelper.KeyDelete(cachekeymaxprofitCP);
            }
            var cachekeymaxprofitPR = $"{Constants.PopulateMaxPricePR}_{RePricerId}";
            if (RedisCacheHelper.KeyExists(cachekeymaxprofitPR))
            {
                RedisCacheHelper.KeyDelete(cachekeymaxprofitPR);
            }
        }

        public void GetResult(Func<PrebookRequest, PreBookResult> getResultFunc, int RePricerId, string prefix)
        {
            string cacheKey = $"{prefix}_{RePricerId}";
            var repricerdetail = _clientPersistence.LoadRePricerDetail(RePricerId).GetAwaiter().GetResult();

            PrebookRequest prerequest = new PrebookRequest
            {
                RepricerId = RePricerId,
                ReservationId = null,
                FromDate = null,
                ToDate = null,
                PageSize = 0,
                PageNumber = 0
            };

            PreBookResult result;
            if (RedisCacheHelper.KeyExists(cacheKey))
            {
                RedisCacheHelper.KeyDelete(cacheKey);
                result = getResultFunc(prerequest);
                result.NotifiedPreBook.ForEach(item => item.ClientConfig_TravelDaysMaxSearchInDays = repricerdetail.ExtraClientDetail.TravelDaysMaxSearchInDays);
                result.NotifiedPreBook.ForEach(item => item.ClientConfig_TravelDaysMinSearchInDays = repricerdetail.ExtraClientDetail.TravelDaysMinSearchInDays);
                result.NotifiedPreBook.ForEach(item => item.ClientConfig_MaxNumberOfTimesOptimization = repricerdetail.ExtraClientDetail.MaxNumberOfTimesOptimization);
                result.NotifiedPreBook.ForEach(item => item.ClientConfig_DaysDifferenceInPreBookCreation = repricerdetail.ExtraClientDetail.ClientConfig_DaysDifferenceInPreBookCreation);
                result.NotifiedPreBook.ForEach(item => item.ClientConfig_PriceDifferenceValue = repricerdetail.ExtraClientDetail.PriceDifferenceValue);
                result.NotifiedPreBook.ForEach(item => item.ClientConfig_MarginThreshold_Percentage = repricerdetail.ExtraClientDetail.PriceDifferencePercentage);
                result.NotifiedPreBook.ForEach(item => item.ClientConfig_MarginThreshold_PercentageUsed = repricerdetail.ExtraClientDetail.IsUsePercentage);
                result.NotifiedPreBook.ForEach(item => item.ClientConfig_ReportEmailToSend = repricerdetail.ExtraClientDetail.ReportEmailToSend);

                result.NotifiedPreBook.ForEach(item => item.ClientConfig_DaysLimitCancellationPolicyEdgeCase = repricerdetail.ExtraClientDetail.DaysLimitCancellationPolicyEdgeCase);
                result.NotifiedPreBook.ForEach(item => item.ClientConfig_IsUseDaysLimitCancellationPolicyEdgeCase = repricerdetail.ExtraClientDetail.IsUseDaysLimitCancellationPolicyEdgeCase);
                result.NotifiedPreBook.ForEach(item => item.ClientConfig_IsCreatePrebookForPriceEdgeCase = repricerdetail.ExtraClientDetail.IsCreatePrebookForPriceEdgeCase);

                if (result != null)
                {
                    RedisCacheHelper.Set(cacheKey, result, TimeSpan.FromHours(6));
                }
            }
            else
            {
                result = getResultFunc(prerequest);
                if (result != null)
                {
                    RedisCacheHelper.Set(cacheKey, result, TimeSpan.FromHours(6));
                }
            }
        }

        public string GenerateCacheKey(string prefix, PrebookRequest prebookRequest)
        {
            return $"{prefix}_{prebookRequest.RepricerId}";
        }

        //public PreBookResult GetOrCalculatePrebookResult(string cacheKey, PrebookRequest prebookRequest, Func<PrebookRequest, PreBookResult> getResultFunc)
        //{
        //	var result = new PreBookResult();
        //	var repricerdetail = (_clientPersistence.LoadRePricerDetail(prebookRequest.RepricerId)).GetAwaiter().GetResult();

        //	if (RedisCacheHelper.KeyExists(cacheKey))
        //	{
        //		result = RedisCacheHelper.Get<PreBookResult>(cacheKey);
        //	}
        //	else
        //	{
        //		PrebookRequest prerequest = new PrebookRequest
        //		{
        //			RepricerId = prebookRequest.RepricerId,
        //			ReservationId = null,
        //			FromDate = null,
        //			ToDate = null,
        //			PageSize = 0,
        //			PageNumber = 0
        //		};

        //		result = getResultFunc(prerequest);
        //		result.NotifiedPreBook.ForEach(item => item.ClientConfig_TravelDaysMaxSearchInDays = repricerdetail.ExtraClientDetail.TravelDaysMaxSearchInDays);
        //		result.NotifiedPreBook.ForEach(item => item.ClientConfig_TravelDaysMinSearchInDays = repricerdetail.ExtraClientDetail.TravelDaysMinSearchInDays);
        //		result.NotifiedPreBook.ForEach(item => item.ClientConfig_MaxNumberOfTimesOptimization = repricerdetail.ExtraClientDetail.MaxNumberOfTimesOptimization);
        //		result.NotifiedPreBook.ForEach(item => item.ClientConfig_DaysDifferenceInPreBookCreation = repricerdetail.ExtraClientDetail.ClientConfig_DaysDifferenceInPreBookCreation);
        //		result.NotifiedPreBook.ForEach(item => item.ClientConfig_PriceDifferenceValue = repricerdetail.ExtraClientDetail.PriceDifferenceValue);
        //		result.NotifiedPreBook.ForEach(item => item.ClientConfig_MarginThreshold_Percentage = repricerdetail.ExtraClientDetail.PriceDifferencePercentage);
        //		result.NotifiedPreBook.ForEach(item => item.ClientConfig_MarginThreshold_PercentageUsed = repricerdetail.ExtraClientDetail.IsUsePercentage);
        //		result.NotifiedPreBook.ForEach(item => item.ClientConfig_ReportEmailToSend = repricerdetail.ExtraClientDetail.ReportEmailToSend);

        //		result.NotifiedPreBook.ForEach(item => item.ClientConfig_DaysLimitCancellationPolicyEdgeCase = repricerdetail.ExtraClientDetail.DaysLimitCancellationPolicyEdgeCase);
        //		result.NotifiedPreBook.ForEach(item => item.ClientConfig_IsUseDaysLimitCancellationPolicyEdgeCase = repricerdetail.ExtraClientDetail.IsUseDaysLimitCancellationPolicyEdgeCase);
        //		result.NotifiedPreBook.ForEach(item => item.ClientConfig_IsCreatePrebookForPriceEdgeCase = repricerdetail.ExtraClientDetail.IsCreatePrebookForPriceEdgeCase);

        //		RedisCacheHelper.Set(cacheKey, result, TimeSpan.FromHours(6));
        //	}
        //	result = FilterByDateRange(result, prebookRequest);
        //	return result;
        //}

        public void PopulateActionsTaken(PreBookResult result, PrebookRequest prebookRequest)
        {
            var actionbookingdata = _masterPersistence.GetBookingActionsTaken(prebookRequest);
            //if (prebookRequest != null)
            //{
            //    RefreshCacheTrigger(prebookRequest.RepricerId, prebookRequest.IsCached);
            //}
            if (actionbookingdata != null)
            {
                foreach (var notifiedPreBookResult in result?.NotifiedPreBook)
                {
                    var correspondingActions = actionbookingdata?
                        .Where(a => a.repricerId == notifiedPreBookResult.RePricerId && a.reservationId == notifiedPreBookResult.ReservationId)
                        ?.ToList();
                    if (correspondingActions != null && correspondingActions.Count != 0)
                    {
                        notifiedPreBookResult.actionsTakens = correspondingActions;
                    }
                }
            }
        }

        /// <summary>
        ///           Update action for dashboard items
        /// </summary>
        /// <param name="result">RepricerReportResponse.ReservationReports</param>
        /// <param name="prebookRequest"></param>
        /// <param name="currentKey">RepricerReportResponse cache key</param>
        /// <param name="reservationReports">reservationReports</param>
        /// <returns></returns>
        public bool PopulateActionsTaken(List<DashboardReportResponseRow> result, PrebookRequest prebookRequest, string currentKey = null, RepricerReportResponse reservationReports = null)
        {
            bool isUpdatedFromDB = false;
            bool isAnyActionUpdated = false;
            if (result?.Any() == true)
            {
                var actionbookingdata = _masterPersistence.GetBookingActionsTaken(prebookRequest);

                /*
                result.FirstOrDefault(a => a.ReservationId == 3250).ActionsTakens = null;
                actionbookingdata = actionbookingdata?.Where(a => a.reservationId != 1840).ToList();
                //*/

                var isAnyActionNotUpdated = result
                                                .Any(x =>
                                                    (
                                                    x?.ReportType?.ToLower() == "optimized"
                                                    || x?.ActionId == 1
                                                    //|| x?.ReportType?.ToLower() == "prebook"
                                                    )
                                                    &&
                                                        (
                                                            x?.ActionsTakens == null
                                                            || (x?.ActionsTakens?.Count ?? 0) == 0
                                                            || x?.ActionsTakens?.Any(y => y.ActionId != 1) == true
                                                        )
                                                        &&
                                                        !(
                                                            actionbookingdata
                                                            ?.Any(a => a.reservationId == x?.ReservationId
                                                            && a.repricerId == x.RepricerId
                                                            && a.ActionId == 1
                                                            )
                                                            == true
                                                        )

                                                );

                if (isAnyActionNotUpdated)
                {
                    // It will trigger reload from db in _masterPersistence.GetBookingActionsTaken
                    if (prebookRequest.IsCached != false)
                    {
                        prebookRequest.IsCached = false;
                        actionbookingdata = _masterPersistence.GetBookingActionsTaken(prebookRequest);
                        isUpdatedFromDB = true;
                    }
                }

                if (actionbookingdata != null)
                {
                    foreach (var notifiedPreBookResult in result)
                    {
                        try
                        {
                            var correspondingActions = actionbookingdata?
                                                .Where(a => a.repricerId == notifiedPreBookResult.RepricerId && a.reservationId == notifiedPreBookResult.ReservationId)
                                                ?.ToList();
                            if (correspondingActions != null && correspondingActions.Count != 0)
                            {
                                notifiedPreBookResult.ActionsTakens = correspondingActions;
                                isAnyActionUpdated = true;
                            }
                        }
                        catch (Exception ex)
                        {
                        }
                    }

                    if (isUpdatedFromDB && isAnyActionUpdated
                        && !string.IsNullOrEmpty(currentKey) && reservationReports != null && reservationReports?.ReservationReports?.Count > 1
                        && string.IsNullOrEmpty(prebookRequest.ReservationId)
                    )
                    {
                        _memoryCache.Set(currentKey, reservationReports, _memoryCacheTimeSpan);
                        RedisCacheHelper.Set(currentKey, reservationReports, _redisCacheTimeSpan);
                    }
                }
            }
            return isUpdatedFromDB;
        }

        public void PopulateMaxPrice(List<DashboardReportResponseRow> result, PrebookRequest prebookRequest, string procedurename)
        {
            var maxProfit = _masterPersistence.GetMaxProfitData(prebookRequest.RepricerId, procedurename);

            if (maxProfit != null)
            {
                foreach (var notifiedPreBookResult in result)
                {
                    var correspondingActions = maxProfit
                        ?.Where(a => a.RepricerId == notifiedPreBookResult.RepricerId && a.ReservationId == notifiedPreBookResult.ReservationId)
                        ?.ToList();

                    if (correspondingActions != null)
                    {
                        if (correspondingActions?.FirstOrDefault()?.OptimizationProfit < notifiedPreBookResult.OptimizationProfit)
                        {
                            correspondingActions.First().OptimizationProfit = notifiedPreBookResult.OptimizationProfit;
                            correspondingActions.First().PreBookPrice = notifiedPreBookResult?.Prebook?.FirstOrDefault()?.Price ?? 0.0M;
                            correspondingActions.First().ReservationPrice = notifiedPreBookResult?.Reservation?.Price ?? 0.0M;
                            correspondingActions.First().CreatedDate = notifiedPreBookResult.CreatedDate;
                            correspondingActions.First().ReservationId = notifiedPreBookResult.ReservationId;
                            correspondingActions.First().RepricerId = notifiedPreBookResult.RepricerId;
                        }
                    }
                }
            }
        }

        public void PopulateMaxPrice(PreBookResult result, PrebookRequest prebookRequest, string procedurename)
        {
            var maxProfit = _masterPersistence.GetMaxProfitData(prebookRequest.RepricerId, procedurename);

            if (maxProfit != null)
            {
                foreach (var notifiedPreBookResult in result?.NotifiedPreBook)
                {
                    var correspondingActions = maxProfit?
                        .Where(a => a.RepricerId == notifiedPreBookResult.RePricerId && a.ReservationId == notifiedPreBookResult.ReservationId)
                        ?.ToList();
                    if (correspondingActions != null && correspondingActions.Count != 0)
                    {
                        notifiedPreBookResult.maxProfit = correspondingActions?.OrderByDescending(x => x.CreatedDate)?.FirstOrDefault();
                    }
                }
            }
        }

        public decimal getrealizedgain(int repricerid)
        {
            var realizedgain = _masterPersistence.GetRealizedGain(repricerid);
            return realizedgain;
        }

        public PreBookResult FilterByDateRange(PreBookResult preBookResult, PrebookRequest prebookRequest)
        {
            try
            {
                var filteredPreBook = preBookResult.NotifiedPreBook;
                if (preBookResult.NotifiedPreBook != null)
                {
                    if (prebookRequest.FromDate != null)
                    {
                        var fromDateValue = DateTime.Parse(prebookRequest.FromDate);
                        DateTime toDateValue;
                        if (prebookRequest.ToDate != null)
                        {
                            toDateValue = DateTime.Parse(prebookRequest.ToDate);
                        }
                        else
                        {
                            toDateValue = DateTime.UtcNow.Date;
                        }

                        filteredPreBook = preBookResult.NotifiedPreBook
                            .Where(item => DateTime.Parse(item.CreatedDate) >= fromDateValue && DateTime.Parse(item.CreatedDate) <= toDateValue)
                            .ToList();
                    }

                    // Filter by ReservationId if provided
                    if (!string.IsNullOrWhiteSpace(prebookRequest.ReservationId))
                    {
                        var reservationIds = prebookRequest.ReservationId.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                                                         .Select(id => int.Parse(id.Trim()))
                                                                         .ToList();

                        filteredPreBook = filteredPreBook
                            .Where(item => reservationIds.Contains(item.ReservationId))
                            .ToList();
                    }

                    // Apply pagination
                    if (prebookRequest.PageSize > 0 && prebookRequest.PageNumber > 0)
                    {
                        int pageSize = prebookRequest.PageSize.Value;
                        int pageNumber = prebookRequest.PageNumber.Value;

                        filteredPreBook = filteredPreBook
                            .Skip((pageNumber - 1) * pageSize)
                            .Take(pageSize)
                            .ToList();
                    }
                }

                preBookResult.NotifiedPreBook = filteredPreBook;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(PreBookResult)
                };
                _log.Error(irixErrorEntity, ex);
            }
            return preBookResult;
        }

        /// <summary>
        /// Get Reservation returnResult and update other master and summary
        /// </summary>
        /// <param name="reservationRequest"></param>
        /// <returns></returns>
        public ReservationReportResponse GetReservationReport(RepricerReportRequest reservationRequest)
        {
            var result = new RepricerReportResponse();
            var repricerdetail = _clientPersistence.LoadRePricerDetail(reservationRequest.RepricerId, null, reservationRequest.IsCached == false).GetAwaiter().GetResult();

            result = GetReservationReportAndUpdateAction(reservationRequest)?.GetAwaiter().GetResult();
            if (result?.ReservationReports != null)
            {
                result.ReservationReports.ForEach(item => item.ClientConfigiurationUsed = new ClientConfig
                {
                    DaysDifferenceInPreBookCreation = repricerdetail.ExtraClientDetail.ClientConfig_DaysDifferenceInPreBookCreation,
                    DaysLimitCancellationPolicyEdgeCase = repricerdetail.ExtraClientDetail.DaysLimitCancellationPolicyEdgeCase,
                    IsCreatePrebookForPriceEdgeCase = repricerdetail.ExtraClientDetail.IsCreatePrebookForPriceEdgeCase,
                    IsUseDaysLimitCancellationPolicyEdgeCase = repricerdetail.ExtraClientDetail.IsUseDaysLimitCancellationPolicyEdgeCase,
                    MarginThresholdPercentage = repricerdetail.ExtraClientDetail.PriceDifferencePercentage,
                    MarginThresholdIsPercentageUsed = repricerdetail.ExtraClientDetail.IsUsePercentage,
                    MarginThresholdValue = repricerdetail.ExtraClientDetail.PriceDifferenceValue,
                    MaxNumberOfTimesOptimization = repricerdetail.ExtraClientDetail.MaxNumberOfTimesOptimization,
                    TravelDaysMaxSearchInDays = repricerdetail.ExtraClientDetail.TravelDaysMaxSearchInDays,
                    TravelDaysMinSearchInDays = repricerdetail.ExtraClientDetail.TravelDaysMinSearchInDays
                });
            }

            if (reservationRequest.IsCached == false)
            {
                var refreshTask = Task.Run(async () =>
                {
                    var reservationSAReportRequest = new DashboardSummaryRequest
                    {
                        RepricerId = 0,
                        isCached = false,
                    };

                    _masterPersistence.ReservationReportSummary(reservationSAReportRequest);

                    await GetDailyOptimizationReportAsync
                                        (
                                           repricerId: 0,
                                           reportType: null,
                                           reportDateFrom: null,
                                           reportDateTo: null,
                                           isCacheRefresh: true
                                        );
                    SuperAdminReservationReport_SummaryNew(reservationSAReportRequest);
                });
            }

            if (result?.ReservationReports != null && result?.Action != null)
            {
                var result2 = FilterDateRangeReport(result?.ReservationReports, reservationRequest, result?.Action);
                return result2;
            }
            else
            {
                return null;
            }
        }

        public ReservationReportResponse FilterDateRangeReport(List<DashboardReportResponseRow> preBookResult, RepricerReportRequest prebookRequest, List<ActionInfo> action)
        {
            var filteredPreBook = preBookResult;

            var totalRowsBreforeFilteration = filteredPreBook?.Count ?? 0;
            var totalRows = filteredPreBook?.Count ?? 0;
            var pageNumber = prebookRequest?.PageNumber ?? 1;
            var pageSize = prebookRequest?.PageSize ?? 100;

            var originalPreBookToDateBKUP = prebookRequest.PreBookToDate;
            if (prebookRequest.PreBookToDate != null)
            {
                prebookRequest.PreBookToDate = DateTime.Parse(prebookRequest?.PreBookToDate).AddDays(1).ToString(Constant.datetype);
            }
            var reservationReportResponse = default(ReservationReportResponse);

            var currentKey = $"FilterDateRangeReport_{prebookRequest.RepricerId}_{prebookRequest.PageSize}_{prebookRequest.PageNumber}_{prebookRequest.IsReservationActionTaken}_{prebookRequest.ReportType}";

            try
            {
                //if (RedisCacheHelper.KeyExists(currentKey))
                //{
                //	reservationReportResponse = RedisCacheHelper.Get<ReservationReportResponse>(currentKey);
                //}
                if (reservationReportResponse == null)
                {
                    var reportType = prebookRequest?.ReportType;
                    var reservationReportRequest = new DashboardSummaryRequest
                    {
                        RepricerId = prebookRequest.RepricerId,
                        isCached = prebookRequest.IsCached ?? true
                    };
                    var overallSummary = _masterPersistence.ReservationReportSummary(reservationReportRequest);

                    if (preBookResult != null)
                    {
                        if (!string.IsNullOrWhiteSpace(reportType?.ToString()))
                        {
                            filteredPreBook = filteredPreBook
                                ?.Where(item => item.ReportType == reportType)
                                ?.ToList();

                            totalRows = overallSummary?.SummarizedView?.FirstOrDefault(
                                            item => item.ReportType == reportType
                                        )
                                        ?.ReservationsCount
                                        ?? filteredPreBook?.Count
                                        ?? 0;
                        }

                        if (prebookRequest?.PreBookFromDate != null)
                        {
                            var fromDateValue = DateTime.Parse(prebookRequest.PreBookFromDate).Date;
                            DateTime toDateValue;
                            if (prebookRequest.PreBookToDate != null)
                            {
                                toDateValue = DateTime.Parse(prebookRequest.PreBookToDate).Date;
                            }
                            else
                            {
                                toDateValue = DateTime.UtcNow;
                            }

                            filteredPreBook = filteredPreBook
                                ?.Where(item => DateTime.Parse(item?.CreatedDate) >= fromDateValue && DateTime.Parse(item.CreatedDate) <= toDateValue)
                                ?.ToList();
                            totalRows = filteredPreBook?.Count ?? 0; //update if Filter applied
                        }

                        if (prebookRequest?.CheckInFromDate != null)
                        {
                            var fromDateValue = DateTime.Parse(prebookRequest.CheckInFromDate);
                            DateTime toDateValue;
                            if (prebookRequest.CheckInToDate != null)
                            {
                                toDateValue = DateTime.Parse(prebookRequest.CheckInToDate);
                            }
                            else
                            {
                                toDateValue = DateTime.UtcNow.Date;
                            }

                            filteredPreBook = filteredPreBook
                                ?.Where(item => DateTime.Parse(item?.Reservation.CheckIn) >= fromDateValue && DateTime.Parse(item.Reservation.CheckIn) <= toDateValue)
                                ?.ToList();
                            totalRows = filteredPreBook?.Count ?? 0; //update if Filter applied
                        }

                        if (prebookRequest?.BookingFromDate != null)
                        {
                            var fromDateValue = DateTime.Parse(prebookRequest.BookingFromDate);
                            DateTime toDateValue;
                            if (prebookRequest.BookingFromDate != null)
                            {
                                toDateValue = DateTime.Parse(prebookRequest.BookingToDate);
                            }
                            else
                            {
                                toDateValue = DateTime.UtcNow.Date;
                            }

                            filteredPreBook = filteredPreBook
                                ?.Where(item => DateTime.Parse(item?.BookingDate) >= fromDateValue && DateTime.Parse(item.Reservation.CheckIn) <= toDateValue)
                                ?.ToList();
                            totalRows = filteredPreBook?.Count ?? 0; //update if Filter applied
                        }

                        // Filter by ReservationId if provided
                        if (!string.IsNullOrWhiteSpace(prebookRequest?.ReservationId))
                        {
                            var reservationIds = prebookRequest.ReservationId.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                                                             .Select(id => int.Parse(id.Trim()))
                                                                             .ToList();

                            filteredPreBook = filteredPreBook
                                ?.Where(item => reservationIds.Contains(item.ReservationId))
                                ?.ToList();
                            totalRows = filteredPreBook?.Count ?? 0; //update if Filter applied
                        }

                        if (!string.IsNullOrWhiteSpace(prebookRequest?.Suppliers))
                        {
                            filteredPreBook = filteredPreBook
                                ?.Where(item => prebookRequest.Suppliers.Contains(item.Reservation.Supplier))
                                ?.ToList();
                            totalRows = filteredPreBook?.Count ?? 0; //update if Filter applied
                        }

                        if (!string.IsNullOrWhiteSpace(prebookRequest?.ReservationStatus))
                        {
                            filteredPreBook = filteredPreBook
                                ?.Where(item => prebookRequest.ReservationStatus.Contains(item.Reservation.Status))
                                ?.ToList();
                            totalRows = filteredPreBook?.Count ?? 0; //update if Filter applied
                        }

                        if (prebookRequest?.IsReservationActionTaken != null)
                        {
                            filteredPreBook = filteredPreBook
                                ?.Where(item => item.NewReservationId > 0
                                )
                                ?.ToList();
                            totalRows = filteredPreBook?.Count ?? 0; //update if Filter applied
                        }

                        // Apply pagination

                        var totalPages = System.Convert.ToInt32(Math.Ceiling(totalRows * 1.0M / (pageSize * 1.0M)));
                        totalPages = totalRows > 0 && totalPages < 1 ? 1 : totalPages;

                        reservationReportResponse = new ReservationReportResponse
                        {
                            RequestBody = prebookRequest,

                            PagesSummary = new PagesSummary
                            {
                                totalRows = totalRows,
                                totalPages = totalPages,
                                pageNumber = pageNumber,
                                pageSize = pageSize,
                            },
                            OverallSummary = overallSummary,
                            Data = filteredPreBook,
                            Action = action
                        };
                        //RedisCacheHelper.Set(currentKey, reservationReportResponse, TimeSpan.FromHours(6));

                        if (prebookRequest?.PageSize > 0 && prebookRequest?.PageNumber > 0)
                        {
                            reservationReportResponse.Data = filteredPreBook
                                ?.Skip((pageNumber - 1) * pageSize)
                                ?.Take(pageSize)
                                ?.ToList();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(PreBookResult)
                };
                _log.Error(irixErrorEntity, ex);
            }
            prebookRequest.PreBookToDate = originalPreBookToDateBKUP;

            return reservationReportResponse;
        }

        private void LogInfo(int RepricerId, string className, string methodName, string message, DateTime startTime)
        {
            var elapsedTime = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2");
            var irixInfoEntity = new IrixErrorEntity
            {
                ClassName = _className,
                MethodName = methodName,
                RePricerId = RepricerId,
                Params = $"RepricerId : {RepricerId}, TimeInMinutes: {elapsedTime}, Message: {message}",
                ElapsedTime = elapsedTime
            };
            _log.Info(message, irixInfoEntity, true);
        }

        public async void RefreshCache(int RepricerId)
        {
            var currentDate = DateTime.UtcNow;
            var methodName = nameof(RefreshCache);
            var cacheKey = $"Repricer_{RepricerId}_RefreshCache_IsRunning";
            var taskList = new ConcurrentBag<Task>();
            try
            {
                var isRunning = RedisCacheHelper.Get<bool>(cacheKey);
                var cacheKeyActionTakenList = $"RepricerId_{RepricerId}_actionTakenList_LoadFromDatabaseWithRetry";
                RedisCacheHelper.DeleteKeysContaining(cacheKeyActionTakenList);

                #region populate Booking Actions

                try
                {
                    PrebookRequest prebookRequest = new PrebookRequest();
                    prebookRequest.RepricerId = RepricerId;
                    prebookRequest.IsCached = false;
                    var startTimeAction = DateTime.UtcNow;

                    #region Update cache

                    try
                    {
                        _masterPersistence.GetBookingActionsTaken(prebookRequest);
                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = _className,
                            MethodName = nameof(RefreshCache),
                            RePricerId = RepricerId,
                            Params = $"RepricerId: {RepricerId},_masterPersistence.GetBookingActionsTaken"
                        };
                        _log.Error(irixErrorEntity, ex);
                    }

                    #endregion Update cache

                    LogInfo(RepricerId, _className, methodName, $"GetBookingActionsTaken", startTimeAction);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        Params = $"RepricerId: {RepricerId},_masterPersistence.GetBookingActionsTaken"
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                #endregion populate Booking Actions

                if (isRunning)
                {
                    try
                    {
                        #region Mandatory refresh on booking action taken

                        #region Delete memory cache SuperAdmin

                        try
                        {
                            var startTimeDeleteMemoryCache = DateTime.UtcNow;
                            int SuperAdminRepricerId = 0;

                            var superAdminMemoryCache = DeleteMemoryCacheKeyByRepricerId(SuperAdminRepricerId)?.GetAwaiter().GetResult();

                            var memoryCacheDeleteRepricer = DeleteMemoryCacheKeyByRepricerId(RepricerId).GetAwaiter().GetResult();
                            LogInfo(RepricerId, _className, methodName, "GetBookingActionsTaken", startTimeDeleteMemoryCache);

                            var logInfoEntry = new
                            {
                                Method = "MemoryCacheDelete",
                                superAdminMemoryCache,
                                memoryCacheDeleteRepricer
                            };

                            var msg = $"{SerializeDeSerializeHelper.Serialize(logInfoEntry)}";
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = "RefreshCache",
                                Params = msg
                            };
                            _log.Info(msg, irixErrorEntity, true);
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = nameof(RefreshCache),
                                RePricerId = RepricerId,
                                Params = $"RepricerId: {RepricerId},MemoryCache"
                            };
                            _log.Error(irixErrorEntity, ex);
                        }

                        #endregion Delete memory cache SuperAdmin

                        var viewReuestCommon = new RepricerReportRequest
                        {
                            RepricerId = RepricerId,
                            PageNumber = 1,
                            PageSize = 20,
                            ReportType = "Prebook",
                            IsCached = false,
                        };
                        GetReservationReport(viewReuestCommon);

                        var reservationReportRequest = new DashboardSummaryRequest
                        {
                            RepricerId = RepricerId,
                            preBookFromDate = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).AddDays(-1).ToString("yyyy-MM-dd"),
                            preBookToDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                            isCached = false,
                        };
                        _masterPersistence.ReservationReportSummary(reservationReportRequest, false);

                        Console.WriteLine($"{cacheKey}_{isRunning}");

                        #endregion Mandatory refresh on booking action taken

                        #region GetDailyOptimizationReportAsync

                        var reports = GetDailyOptimizationReportAsync
                                        (
                                           repricerId: 0,
                                           reportType: null,
                                           reportDateFrom: null,
                                           reportDateTo: null,
                                           isCacheRefresh: true
                                        )?.GetAwaiter().GetResult();

                        #endregion GetDailyOptimizationReportAsync

                        #region SuperAdmin Refresh cache

                        try
                        {
                            var currentSADate = DateTime.UtcNow;
                            var timeDelay = 2000;

                            var preBookFromDate = new DateTime(currentDate.Year, currentSADate.Month, 1).ToString("yyyy-MM-dd");
                            var preBookToDate = new DateTime(currentDate.Year, currentSADate.Month, DateTime.DaysInMonth(currentDate.Year, currentDate.Month)).ToString("yyyy-MM-dd");

                            var reservationSAReportRequest = new DashboardSummaryRequest
                            {
                                RepricerId = 0,
                                preBookFromDate = preBookFromDate,
                                preBookToDate = preBookToDate,
                                isCached = false,
                            };

                            //SuperAdminReservationReport_SummaryNew(reservationSAReportRequest);
                            //await Task.Delay(timeDelay);
                            //SuperAdminReservationReport_SummaryNew(reservationSAReportRequest);

                            // await Task.Delay(timeDelay);
                            reservationSAReportRequest = new DashboardSummaryRequest
                            {
                                RepricerId = 0,
                                isCached = false,
                            };

                            SuperAdminReservationReport_SummaryNew(reservationSAReportRequest);
                            //await Task.Delay(timeDelay);
                            //SuperAdminReservationReport_SummaryNew(reservationSAReportRequest);
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = nameof(RefreshCache),
                                RePricerId = RepricerId,
                                Params = $"RepricerId: {RepricerId},{nameof(SuperAdminReservationReport_SummaryNew)}"
                            };
                            _log.Error(irixErrorEntity, ex);
                        }

                        #endregion SuperAdmin Refresh cache
                    }
                    catch
                    {
                    }
                    return;
                }
                RedisCacheHelper.Set(cacheKey, true, TimeSpan.FromMinutes(5));

                var startTimeFull = DateTime.UtcNow;
                var startTime = DateTime.UtcNow;
                _log.InfoV1(RepricerId, _className, "UPDATE_RRD_AND_CACHE", $"{_className}{nameof(RefreshCache)}", startTime, true, "start");
                var repricerDetail = _clientPersistence.LoadRePricerDetail(RepricerId)?.GetAwaiter().GetResult();

                #region InvoiceRefresh

                try
                {
                    var currency = repricerDetail?.ExtraClientDetail?.Currency ?? "EUR";
                    var factor = ExchangeRateFactor(RepricerId, "EUR", currency);
                    var factorOriginal = ExchangeRateFactor(RepricerId, currency, "EUR");
                    var reservationRequestReportsForCurrentYear = GenerateMonthlyReportsForYear(RepricerId);

                    foreach (var reservationRequestReport in reservationRequestReportsForCurrentYear)
                    {
                        try
                        {
                            var reservationReportResponse = GetInvoiceData(reservationRequestReport, repricerDetail);
                            RepricerInvoiceResponse result = new RepricerInvoiceResponse
                            {
                                ReservationReports = reservationReportResponse?.ReservationReports ?? new List<DashboardReportResponseRow>(),
                            };

                            if (currency != "EUR" && result.ReservationReports != null && result.ReservationReports.Count > 0)
                            {
                                try
                                {
                                    var cachedDataCopy = SerializeDeSerializeHelper.DeSerialize<RepricerInvoiceResponse>(SerializeDeSerializeHelper.Serialize(result));
                                    result = GetRepricerInvoiceResponse(currency, factor, result, factorOriginal);
                                }
                                catch (Exception ex)
                                {
                                    var irixErrorEntity = new IrixErrorEntity
                                    {
                                        ClassName = _className,
                                        MethodName = nameof(RefreshCache),
                                        Params = $"RepricerId: {RepricerId},_masterService.GetInvoceData"
                                    };
                                    _log.Error(irixErrorEntity, ex);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            var logEntry = new
                            {
                                RepricerId,
                                Method = "_masterService.GetInvoceData",
                                reservationRequestReport
                            };

                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = nameof(RefreshCache),
                                Params = SerializeDeSerializeHelper.Serialize(logEntry)
                            };
                            _log.Error(irixErrorEntity, ex);
                        }
                    }
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        Params = $"RepricerId: {RepricerId},_masterService.GetInvoceData"
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                #endregion InvoiceRefresh

                #region Delete memory cache SuperAdmin

                try
                {
                    int SuperAdminRepricerId = 0;

                    var superAdminMemoryCache = DeleteMemoryCacheKeyByRepricerId(SuperAdminRepricerId)?.GetAwaiter().GetResult();

                    var memoryCacheDeleteRepricer = DeleteMemoryCacheKeyByRepricerId(RepricerId).GetAwaiter().GetResult();
                    LogInfo(RepricerId, _className, methodName, "GetBookingActionsTaken", startTime);

                    var logInfoEntry = new
                    {
                        Method = "MemoryCacheDelete",
                        superAdminMemoryCache,
                        memoryCacheDeleteRepricer
                    };

                    var msg = $"{SerializeDeSerializeHelper.Serialize(logInfoEntry)}";
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = "RefreshCache",
                        Params = msg
                    };
                    _log.Info(msg, irixErrorEntity, true);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                        Params = $"RepricerId: {RepricerId},MemoryCache"
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                #endregion Delete memory cache SuperAdmin

                #region DeleteCacheUsingEndpoint

                try
                {
                    var task = Task.Run(() =>
                    {
                        RepricerService.DeleteCacheUsingEndpoint(RepricerId);
                    });
                    taskList.Add(task);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                        Params = $"RepricerId: {RepricerId},MemoryCache-PROD-DeleteCacheUsingEndpoint"
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                #endregion DeleteCacheUsingEndpoint

                #region ALL_Cache_Refresh

                try
                {
                    startTime = DateTime.UtcNow;
                    _clientPersistence.LoadRePricerDetail(RepricerId, null, true)?.GetAwaiter().GetResult();
                    LogInfo(RepricerId, _className, methodName, "LoadRePricerDetail", startTime);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                        Params = $"RepricerId: {RepricerId},LoadRePricerDetail"
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                try
                {
                    startTime = DateTime.UtcNow;

                    _clientPersistence.LoadRePricerEmailConfig(RepricerId, true)?.GetAwaiter().GetResult();

                    LogInfo(RepricerId, _className, methodName, "LoadRePricerEmailConfig", startTime);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                        Params = $"RepricerId: {RepricerId},LoadRePricerEmailConfig"
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                try
                {
                    var prebookRequest = new PrebookRequest
                    {
                        RepricerId = RepricerId,
                        IsCached = false
                    };
                    startTime = DateTime.UtcNow;
                    _masterPersistence.GetBookingActionsTaken(prebookRequest);
                    LogInfo(RepricerId, _className, methodName, "GetBookingActionsTaken", startTime);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                        Params = $"RepricerId: {RepricerId},GetBookingActionsTaken"
                    };
                    _log.Error(irixErrorEntity, ex);
                }
                try
                {
                    var datewisecount = new DateWiseRequest
                    {
                        RepricerId = RepricerId,
                        FromDate = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).ToString("yyyy-MM-dd"),
                        ToDate = DateTime.UtcNow.ToString("yyyy-MM-dd")
                    };
                    startTime = DateTime.UtcNow;
                    _masterPersistence.DateWiseCount(datewisecount, false);
                    LogInfo(RepricerId, _className, methodName, "DateWiseCount", startTime);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                        Params = $"RepricerId: {RepricerId},DateWiseCount"
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                try
                {
                    var datewisecount1 = new DateWiseRequest
                    {
                        RepricerId = RepricerId,
                        FromDate = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).AddDays(-1).ToString("yyyy-MM-dd"),
                        ToDate = DateTime.UtcNow.ToString("yyyy-MM-dd")
                    };
                    startTime = DateTime.UtcNow;
                    _masterPersistence.DateWiseCount(datewisecount1, false);
                    LogInfo(RepricerId, _className, methodName, "DateWiseCount-datewisecount1", startTime);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                        Params = $"RepricerId: {RepricerId},DateWiseCount-datewisecount1"
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                try
                {
                    startTime = DateTime.UtcNow;
                    //RedisCacheHelper.DeleteKeysContaining($"{UserConstant.ReservationReportSummary}_{RepricerId}_");
                    RedisCacheHelper.DeleteKeysContaining($"Repricer_{RepricerId}_");
                    RedisCacheHelper.DeleteKeysContaining($"GetMultiSupplierReservationReport");

                    var actionKey = $"RepricerId_{RepricerId}_actionTakenList_LoadFromDatabaseWithRetry";
                    _memoryCache.Remove(actionKey);

                    actionKey = $"GetPreBookCriteriaDBAll_{RepricerId}_";
                    _memoryCache.Remove(actionKey);
                    RedisCacheHelper.DeleteKeysContaining(actionKey);

                    LogInfo(RepricerId, _className, methodName, "DRedisCacheHelper.DeleteKeysContaining", startTime);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                        Params = $"RepricerId: {RepricerId},RedisCacheHelper.DeleteKeysContaining(Repricer_{RepricerId})"
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                #region GetReservationReports

                try
                {
                    var viewReuestCommon = new RepricerReportRequest
                    {
                        RepricerId = RepricerId,
                        PageNumber = 1,
                        PageSize = 20,
                        IsCached = false,
                    };
                    startTime = DateTime.UtcNow;
                    GetReservationReportAndUpdateAction(viewReuestCommon)?.GetAwaiter().GetResult();
                    LogInfo(RepricerId, _className, methodName, "GetReservationReports-viewReuestCommon", startTime);

                    List<ViewReportType> reports = new List<ViewReportType>();
                    reports.Add(ViewReportType.Prebook);
                    reports.Add(ViewReportType.CancellationEdgeCase);
                    reports.Add(ViewReportType.PriceEdgeCase);
                    reports.Add(ViewReportType.NoOrLessGainButBetterCancellation);
                    reports.Add(ViewReportType.CancellationAmountCase);
                    reports.Add(ViewReportType.CancellationChargesApplicable);
                    reports.Add(ViewReportType.UsingRoomMapping);
                    reports.Add(ViewReportType.UsingRoomMapping1);
                    reports.Add(ViewReportType.UsingRoomMapping1);

                    foreach (ViewReportType reportType in reports)
                    {
                        // Generate the request based on the current enum type
                        try
                        {
                            startTime = DateTime.UtcNow;
                            var request = GetRepricerReportRequest(reportType, RepricerId);
                            // Call the async method and add the task to the list
                            var result = GetReservationReport(request);

                            LogInfo(RepricerId, _className, methodName, $"GetReservationReport-{reportType.ToString()}", startTime);
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = nameof(RefreshCache),
                                RePricerId = RepricerId,
                                Params = $"RepricerId: {RepricerId},GetRepricerReportRequest({reportType.ToString()},{RepricerId})",
                            };
                            _log.Error(irixErrorEntity, ex);
                        }
                    }
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                try
                {
                    startTime = DateTime.UtcNow;
                    var repricerreport4 = new RepricerReportRequest
                    {
                        RepricerId = RepricerId,
                        PreBookFromDate = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).ToString("yyyy-MM-dd"),
                        PreBookToDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                        IsCached = true
                    };

                    GetReservationReport(repricerreport4);
                    LogInfo(RepricerId, _className, methodName, $"GetReservationReport-repricerreport4", startTime);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                        Params = $"RepricerId: {RepricerId},GetRepricerReportRequest(repricerreport4)",
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                try
                {
                    startTime = DateTime.UtcNow;
                    var repricerreport5 = new RepricerReportRequest
                    {
                        IsReservationActionTaken = true,
                        RepricerId = 1,
                        PageNumber = 1,
                        PageSize = 20,
                        IsCached = true,
                    };
                    GetReservationReport(repricerreport5);
                    LogInfo(RepricerId, _className, methodName, $"GetReservationReport-repricerreport6", startTime);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                        Params = $"RepricerId: {RepricerId},GetRepricerReportRequest(repricerreport5)",
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                #endregion GetReservationReports

                #region ReservationReportSummary

                try
                {
                    startTime = DateTime.UtcNow;
                    var reservationReportRequest = new DashboardSummaryRequest
                    {
                        RepricerId = RepricerId,
                        //preBookFromDate = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).AddDays(-1).ToString("yyyy-MM-dd"),
                        //preBookToDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                        isCached = false,
                    };
                    _masterPersistence.ReservationReportSummary(reservationReportRequest, false);
                    LogInfo(RepricerId, _className, methodName, $"ReservationReportSummary-Request1", startTime);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                //try
                //{
                //    startTime = DateTime.UtcNow;
                //    var reservationReportRequest_year = new DashboardSummaryRequest
                //    {
                //        RepricerId = RepricerId,
                //        preBookFromDate = currentDate.AddYears(-1).AddDays(1).ToString("yyyy-MM-dd"),
                //        preBookToDate = currentDate.ToString("yyyy-MM-dd"), // Current date
                //        isCached = false,
                //    };

                //    _masterPersistence.ReservationReportSummary(reservationReportRequest_year, false);
                //    LogInfo(RepricerId, _className, methodName, $"ReservationReportSummary-reservationReportRequest_year", startTime);
                //}
                //catch (Exception ex)
                //{
                //    var irixErrorEntity = new IrixErrorEntity
                //    {
                //        ClassName = _className,
                //        MethodName = nameof(RefreshCache),
                //        repricerId = RepricerId,
                //    };
                //    _log.Error(irixErrorEntity, ex);
                //}

                //try
                //{
                //    startTime = DateTime.UtcNow;
                //    var reservationReportRequest1 = new DashboardSummaryRequest
                //    {
                //        RepricerId = RepricerId,
                //    };
                //    _masterPersistence.ReservationReportSummary(reservationReportRequest1, false);
                //    LogInfo(RepricerId, _className, methodName, $"ReservationReportSummary-OnlyRepricerID", startTime);
                //}
                //catch (Exception ex)
                //{
                //    var irixErrorEntity = new IrixErrorEntity
                //    {
                //        ClassName = _className,
                //        MethodName = nameof(RefreshCache),
                //        repricerId = RepricerId,
                //    };
                //    _log.Error(irixErrorEntity, ex);
                //}

                #endregion ReservationReportSummary

                try
                {
                    startTime = DateTime.UtcNow;
                    var reservationReportRequest_sup = new DashboardSummaryRequest
                    {
                        RepricerId = 0,
                        //preBookFromDate = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).AddDays(-1).ToString("yyyy-MM-dd"),
                        //preBookToDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                        isCached = false
                    };
                    SuperAdminReservationReportSummary(reservationReportRequest_sup);
                    LogInfo(RepricerId, _className, methodName, $"SuperAdminReservationReportSummary-reservationReportRequest_sup", startTime);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                        Params = $"RepricerId: {RepricerId}, SuperAdminReservationReportSummary-reservationReportRequest_sup"
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                //try
                //{
                //    startTime = DateTime.UtcNow;
                //    var reservationReportRequest_year_sup = new DashboardSummaryRequest
                //    {
                //        RepricerId = 0,
                //        preBookFromDate = currentDate.AddYears(-1).AddDays(1).ToString("yyyy-MM-dd"),
                //        preBookToDate = currentDate.ToString("yyyy-MM-dd"), // Current date
                //        isCached = false
                //    };

                //    SuperAdminReservationReportSummary(reservationReportRequest_year_sup);
                //    LogInfo(RepricerId, _className, methodName, $"SuperAdminReservationReportSummary-reservationReportRequest_year_sup", startTime);
                //}
                //catch (Exception ex)
                //{
                //    var irixErrorEntity = new IrixErrorEntity
                //    {
                //        ClassName = _className,
                //        MethodName = nameof(RefreshCache),
                //        repricerId = RepricerId,
                //    };
                //    _log.Error(irixErrorEntity, ex);
                //}

                try
                {
                    startTime = DateTime.UtcNow;
                    _reservationPersistence.GetSupplierCount(0, true);
                    _reservationPersistence.GetSupplierCount(RepricerId, true);
                    LogInfo(RepricerId, _className, methodName, $"GetSupplierCount", startTime);

                    var task_GetMultiSupplierPrebookSummary = Task.Run(() =>
                    {
                        var startTime = DateTime.UtcNow;
                        _reservationPersistence.GetMultiSupplierPrebookSummary(RepricerId, null, null, true);
                        _reservationPersistence.GetMultiSupplierPrebookSummary(0, null, null, true);
                        LogInfo(RepricerId, _className, methodName, $"GetMultiSupplierPrebookSummary", startTime);
                    });

                    startTime = DateTime.UtcNow;
                    var reports = GetDailyOptimizationReportAsync
                    (
                       repricerId: 0,
                       reportType: null,
                       reportDateFrom: null,
                       reportDateTo: null,
                       isCacheRefresh: true
                    )?.GetAwaiter().GetResult();

                    LogInfo(RepricerId, _className, methodName, $"GetDailyOptimizationReportAsync", startTime);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                #endregion ALL_Cache_Refresh

                #region SuperAdmin Refresh cache

                try
                {
                    var currentSADate = DateTime.UtcNow;
                    var timeDelay = 2000;

                    var preBookFromDate = new DateTime(currentDate.Year, currentSADate.Month, 1).ToString("yyyy-MM-dd");
                    var preBookToDate = new DateTime(currentDate.Year, currentSADate.Month, DateTime.DaysInMonth(currentDate.Year, currentDate.Month)).ToString("yyyy-MM-dd");

                    var reservationSAReportRequest = new DashboardSummaryRequest
                    {
                        RepricerId = 0,
                        //preBookFromDate = preBookFromDate,
                        //preBookToDate = preBookToDate,
                        isCached = false,
                    };

                    //SuperAdminReservationReport_SummaryNew(reservationSAReportRequest);
                    //await Task.Delay(timeDelay);
                    //SuperAdminReservationReport_SummaryNew(reservationSAReportRequest);

                    //await Task.Delay(timeDelay);
                    //reservationSAReportRequest = new DashboardSummaryRequest
                    //{
                    //    RepricerId = 0,
                    //    isCached = false,
                    //};

                    //SuperAdminReservationReport_SummaryNew(reservationSAReportRequest);
                    //await Task.Delay(timeDelay);
                    SuperAdminReservationReport_SummaryNew(reservationSAReportRequest);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(RefreshCache),
                        RePricerId = RepricerId,
                        Params = $"RepricerId: {RepricerId},{nameof(SuperAdminReservationReport_SummaryNew)}"
                    };
                    _log.Error(irixErrorEntity, ex);
                }

                #endregion SuperAdmin Refresh cache

                _log.InfoV1(RepricerId, _className, "UPDATE_RRD_AND_CACHE", $"{nameof(RefreshCache)}", startTimeFull, true, "end");
            }
            finally
            {
                if (taskList.Any())
                {
                    Task.WhenAll(taskList)?.GetAwaiter().GetResult();
                }
                RedisCacheHelper.Remove(cacheKey);
            }
        }

        // Method to generate reservation returnResult for every month of the given year
        private List<CommonReportRequest> GenerateMonthlyReportsForYear(int repricerId)
        {
            var year = DateTime.UtcNow.Year;
            var monthlyReports = new List<CommonReportRequest>();

            for (int month = 1; month <= 12; month++)
            {
                DateTime fromDate = new DateTime(year, month, 1);

                DateTime toDate = fromDate.AddMonths(1);

                var report = new CommonReportRequest
                {
                    RepricerId = repricerId,
                    IsCached = false,
                    FromDate = fromDate.ToString("yyyy-MM-dd"),
                    ToDate = toDate.ToString("yyyy-MM-dd")
                };

                monthlyReports.Add(report);
            }

            return monthlyReports;
        }

        private RepricerReportRequest GetRepricerReportRequest(ViewReportType viewReportType, int RepricerId)
        {
            return new RepricerReportRequest
            {
                RepricerId = RepricerId,
                PageNumber = 1,
                PageSize = 20,
                IsCached = true,
                ReportType = viewReportType.ToString()
            };
        }

        public void RefreshCacheTrigger(int RepricerId, bool isCacheRefresh = false)
        {
            if (isCacheRefresh && RepricerId > 0)
            {
                Task.Run(() =>
                {
                    try
                    {
                        RefreshCache(RepricerId);
                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = _className,
                            MethodName = nameof(RefreshCacheTrigger)
                        };
                        _log.Error(irixErrorEntity, ex);
                    }
                });
            }
        }

        private DateTime ParseDate(string dateString)
        {
            DateTime parsedDate;

            if (DateTime.TryParseExact(dateString, Constant.datetype, CultureInfo.InvariantCulture, DateTimeStyles.None, out parsedDate))
            {
                return parsedDate;
            }
            else
            {
                throw new ArgumentException("Invalid date format", nameof(dateString));
            }
        }

        private List<T> GetPageFromList<T>(List<T> list, int? pageVar, int? pageSizeVar)
        {
            int page = pageVar ?? 1;
            int pageSize = pageSizeVar ?? 100;
            return list.Skip(page * pageSize).Take(pageSize).ToList();
        }

        public void InsertReportinTable(int RepricerId)
        {
            try
            {
                _reservationPersistence.InsertReportinTable(RepricerId);
                RedisCacheHelper.RemoveAllKeys();
                //RedisCacheHelper.DeleteKeysContaining($"{UserConstant.FilterDateRangeReport}_{RepricerId}");
                //RedisCacheHelper.DeleteKeysContaining($"{UserConstant.DateWiseCount}_{RepricerId}");
                //RedisCacheHelper.DeleteKeysContaining($"{UserConstant.ReservationReportSummary}_{RepricerId}");
                //RedisCacheHelper.DeleteKeysContaining($"{UserConstant.GetReservationReportAndUpdateAction}_{RepricerId}");
            }
            catch (Exception ex)
            {
            }
        }

        public RepricerReportResponse GetInvoiceData(CommonReportRequest reservationRequest, RePricerDetail repricerdetail = null)
        {
            var result = new RepricerReportResponse();
            try
            {
                if (repricerdetail == null)
                {
                    repricerdetail = _clientPersistence.LoadRePricerDetail(reservationRequest.RepricerId, null, reservationRequest.IsCached == false).GetAwaiter().GetResult();
                }

                result = _masterPersistence.GetInvoiceData(reservationRequest);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetInvoiceData)
                };
                _log.Error(irixErrorEntity, ex);
            }

            return result;
        }

        public ReservationReportResponse CalculatedReservationReport(string currency, decimal factor, ReservationReportResponse reservationReportResponse, decimal factorOriginal)
        {
            var preBookFactor = factor;
            var reservationFactor = factor;
            var gainFactor = factor;
            reservationReportResponse.OverallSummary.LifetimeOptimisedReservationPrice = RoundToDecimalPlaces(reservationReportResponse.OverallSummary.LifetimeOptimisedReservationPrice * factor);
            reservationReportResponse.OverallSummary.LifetimeOptimisedPrebookPrice = RoundToDecimalPlaces(reservationReportResponse.OverallSummary.LifetimeOptimisedPrebookPrice * factor);
            reservationReportResponse.OverallSummary.LifetimePossibleOptimisationProfit = RoundToDecimalPlaces(reservationReportResponse.OverallSummary.LifetimePossibleOptimisationProfit * factor);
            reservationReportResponse.OverallSummary.RealizedGain = RoundToDecimalPlaces(reservationReportResponse.OverallSummary.RealizedGain * factor);
            reservationReportResponse.OverallSummary.MissedGain = RoundToDecimalPlaces(reservationReportResponse.OverallSummary.MissedGain * factor);
            reservationReportResponse.OverallSummary.OpenGain = RoundToDecimalPlaces(reservationReportResponse.OverallSummary.OpenGain * factor);

            if (reservationReportResponse.OverallSummary.SummarizedView != null)
            {
                foreach (var summarizedview in reservationReportResponse.OverallSummary.SummarizedView)
                {
                    summarizedview.Currency = currency;
                    summarizedview.MaxProfit = RoundToDecimalPlaces(summarizedview.MaxProfit * factor ?? 0.0m);
                    summarizedview.PrebookPrice = RoundToDecimalPlaces(summarizedview.PrebookPrice * factor ?? 0.0m);
                    summarizedview.Profit = RoundToDecimalPlaces(summarizedview.Profit * factor ?? 0.0m);
                    summarizedview.RealizedGain = RoundToDecimalPlaces(summarizedview.RealizedGain * factor ?? 0.0m);
                    summarizedview.ReservationPrice = RoundToDecimalPlaces(summarizedview.ReservationPrice * factor ?? 0.0m);
                }
            }

            if (reservationReportResponse?.Data != null)
            {
                foreach (var item in reservationReportResponse.Data)
                {
                    var bookingAction = item?.ActionsTakens?.OrderByDescending(x => x.createdOn)?.FirstOrDefault(x => x.ActionId == 1) ?? item?.ActionsTakens?.FirstOrDefault();
                    PricesOptimizationBooking actionPrices = null;
                    OptimizationBookingResponse optimizationBookingResponse = null;

                    //if ((item?.Reservation?.Currency ?? item?.Currency) != item?.CustomerCurrency
                    //    && !string.IsNullOrEmpty(item?.CustomerCurrency)
                    //)
                    //{
                    //    var itemCurrency = (item?.Reservation?.Currency ?? item?.Currency ?? "EUR");
                    //    currency = item?.CustomerCurrency ?? currency;
                    //    factor = ExchangeRateFactor(item.RepricerId, itemCurrency, currency);
                    //    item.OptimizationProfit = item.CustomerAmount;
                    //}
                    //else
                    //{
                    //    item.OptimizationProfit = RoundToDecimalPlaces(item.OptimizationProfit * factor);
                    //}

                    item.ProfitAfterCancellation = RoundToDecimalPlaces(item.ProfitAfterCancellation * factor);
                    item.MatchedCancellationPolicyGain = RoundToDecimalPlaces(item.MatchedCancellationPolicyGain * factor);
                    item.Reservation.Price = RoundToDecimalPlaces(item.Reservation.Price * factor);
                    item.Prebook.FirstOrDefault().Price = RoundToDecimalPlaces(item.Prebook.FirstOrDefault().Price * factor);
                    item.Reservation.CancellationCharge = RoundToDecimalPlaces(item.Reservation.CancellationCharge * factor);
                    item.Prebook.FirstOrDefault().CancellationCharge = RoundToDecimalPlaces(item.Prebook.FirstOrDefault().CancellationCharge * factor);
                    item.Currency = currency;
                    item.Reservation.CancellationCurrency = currency;
                    item.Prebook.FirstOrDefault().CancellationCurrency = currency;

                    //item.CurrencyFactorToEur = factorOriginal;

                    //if (item.ActionsTakens != null)
                    //{
                    //    foreach (var actions in item.ActionsTakens)
                    //    {
                    //        try
                    //        {
                    //            actions.RealizedGain = RoundToDecimalPlaces(actions.RealizedGain * factor);

                    //            if
                    //            (
                    //                optimizationBookingResponse != null
                    //                && currency == optimizationBookingResponse?.OldReservation?.Price?.Supplier?.Currency
                    //                && optimizationBookingResponse?.OldReservation?.Price?.Supplier != null
                    //                && optimizationBookingResponse?.NewReservation?.Price?.Supplier != null
                    //            )
                    //            {
                    //                item.Reservation.Price = optimizationBookingResponse.OldReservation.Price.Supplier.Value;
                    //                item.Reservation.Currency = optimizationBookingResponse.OldReservation.Price.Supplier.Currency;
                    //            }
                    //            else
                    //            {
                    //                var originalReservationCurrency = optimizationBookingResponse?.OldReservation?.Price?.Supplier?.Currency;
                    //                var originalReservationToCustomerCurrencyFactor = item.CustomerCurrencyFactor;
                    //                if
                    //                (
                    //                    item?.CustomerCurrencyFactor != null
                    //                    && !string.IsNullOrEmpty(originalReservationCurrency)
                    //                    && !string.IsNullOrEmpty(item?.CustomerCurrency)
                    //                    && originalReservationCurrency != item?.CustomerCurrency
                    //                )
                    //                {
                    //                    if (originalReservationCurrency != currency)
                    //                    {
                    //                        var prebookfactor = ExchangeRateFactor(item.RepricerId, originalReservationCurrency, currency);
                    //                        item.Reservation.Price = optimizationBookingResponse.OldReservation.Price.Supplier.Value * prebookfactor;
                    //                        item.Reservation.Currency = currency;
                    //                    }
                    //                    else if (originalReservationCurrency != item.CustomerCurrency)
                    //                    {
                    //                        item.Reservation.Price = optimizationBookingResponse.OldReservation.Price.Supplier.Value * item.CustomerCurrencyFactor;
                    //                        item.Reservation.Currency = item.CustomerCurrency;
                    //                    }
                    //                }
                    //            }

                    //            if
                    //            (
                    //                optimizationBookingResponse != null
                    //                && currency == optimizationBookingResponse?.NewReservation?.Price?.Supplier?.Currency
                    //                && optimizationBookingResponse?.OldReservation?.Price?.Supplier != null
                    //                && optimizationBookingResponse?.NewReservation?.Price?.Supplier != null
                    //            )
                    //            {
                    //                item.Prebook.Price = optimizationBookingResponse.NewReservation.Price.Supplier.Value;
                    //                item.Prebook.Currency = optimizationBookingResponse.NewReservation.Price.Supplier.Currency;
                    //            }
                    //            else
                    //            {
                    //                var originalReservationCurrency = optimizationBookingResponse?.NewReservation?.Price?.Supplier?.Currency;
                    //                var originalReservationToCustomerCurrencyFactor = item.CustomerCurrencyFactor;
                    //                if
                    //                (
                    //                    item?.CustomerCurrencyFactor != null
                    //                    && !string.IsNullOrEmpty(originalReservationCurrency)
                    //                    && !string.IsNullOrEmpty(item?.CustomerCurrency)
                    //                    && originalReservationCurrency != item?.CustomerCurrency
                    //                )
                    //                {
                    //                    if (originalReservationCurrency != currency)
                    //                    {
                    //                        var prebookfactor = ExchangeRateFactor(item.RepricerId, originalReservationCurrency, currency);
                    //                        item.Prebook.Price = optimizationBookingResponse.NewReservation.Price.Supplier.Value * prebookfactor;
                    //                        item.Prebook.Currency = currency;
                    //                    }
                    //                    else if (originalReservationCurrency != item.CustomerCurrency)
                    //                    {
                    //                        item.Prebook.Price = optimizationBookingResponse.NewReservation.Price.Supplier.Value * item.CustomerCurrencyFactor;
                    //                        item.Prebook.Currency = item.CustomerCurrency;
                    //                    }
                    //                }
                    //            }
                    //        }
                    //        catch (Exception ex)
                    //        {
                    //            Console.WriteLine(ex.ToString());
                    //        }
                    //    }
                    //}

                    try
                    {
                        try
                        {
                            if (!string.IsNullOrEmpty(bookingAction?.ResponseBody))
                            {
                                optimizationBookingResponse = (SerializeDeSerializeHelper.DeSerialize<OptimizationBookingResponse>(bookingAction?.ResponseBody));
                                actionPrices = optimizationBookingResponse?.Optimization?.Prices;

                                if (actionPrices?.OldBooking != null)
                                {
                                    item.Reservation.Currency = actionPrices.OldBooking.Currency;
                                    if (actionPrices.OldBooking.Currency == currency)
                                    {
                                        reservationFactor = 1;
                                        item.Reservation.Price = actionPrices.OldBooking.Value;
                                    }
                                    else
                                    {
                                        reservationFactor = ExchangeRateFactor(item.RepricerId, actionPrices.OldBooking.Currency, currency, item.BookingDate);
                                        item.Reservation.Price = actionPrices.OldBooking.Value * reservationFactor;
                                        item.Reservation.Currency = actionPrices.OldBooking.Currency;
                                    }

                                    item.Prebook.FirstOrDefault().Currency = currency;
                                    if (actionPrices.NewBooking.Currency == currency)
                                    {
                                        reservationFactor = 1;
                                        item.Prebook.FirstOrDefault().Price = actionPrices.NewBooking.Value;
                                    }
                                    else
                                    {
                                        if (bookingAction?.IsUseCustomerCurrencyFactor == true)
                                        {
                                            preBookFactor = item.CustomerCurrencyFactor;
                                        }
                                        else
                                        {
                                            preBookFactor = ExchangeRateFactor(item.RepricerId, actionPrices.NewBooking.Currency, currency, bookingAction.createdOn.ToString(constant.datetype));
                                        }
                                        item.Prebook.FirstOrDefault().Price = actionPrices.NewBooking.Value * preBookFactor;

                                    }

                                    item.CustomerCurrency = currency;
                                    if (actionPrices.Gain.Currency == currency)
                                    {
                                        reservationFactor = 1;
                                        item.ProfitAfterCancellation = item.OptimizationProfit = item.CustomerAmount = item.OptimizationProfit = actionPrices.Gain.Value;
                                    }
                                    else
                                    {
                                        gainFactor = ExchangeRateFactor(item.RepricerId, actionPrices.Gain.Currency, currency, bookingAction.createdOn.ToString(constant.datetype));
                                        item.ProfitAfterCancellation = item.OptimizationProfit = item.CustomerAmount = item.OptimizationProfit = actionPrices.Gain.Value * gainFactor;
                                    }


                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(ex.ToString());
                        }

                        if (item.Reservation?.CancellationPoliciesBySource != null)
                        {
                            foreach (var cp in item.Reservation?.CancellationPoliciesBySource)
                            {
                                var reservationCPFactor = 0.0m;
                                if (currency == cp.Currency && currency == item.CustomerCurrency)
                                {
                                    reservationCPFactor = item.CustomerCurrencyFactor;
                                    currency = item.CustomerCurrency;
                                    item.Reservation.CancellationCharge = cp.CancellationCharge = RoundToDecimalPlaces(cp.CancellationCharge, 2);
                                }
                                else
                                {
                                    if (cp?.CancellationPolicyType?.ToLower() == "limit")
                                    {
                                        reservationCPFactor = 1;
                                        cp.Currency = currency;
                                        cp.CancellationCharge = item.Reservation.Price;
                                    }
                                    else
                                    {
                                        reservationCPFactor = ExchangeRateFactor(item.RepricerId, cp.Currency, currency, item.BookingDate);
                                        cp.Currency = currency;
                                        item.Reservation.CancellationCharge = cp.CancellationCharge = RoundToDecimalPlaces(cp.CancellationCharge * reservationCPFactor, 2);
                                    }
                                }
                            }
                        }

                        if (item.Prebook?.FirstOrDefault()?.CancellationPoliciesBySource != null)
                        {
                            foreach (var cp in item.Prebook?.FirstOrDefault()?.CancellationPoliciesBySource)
                            {
                                var prebookCPFactor = 0.0m;
                                if (currency == cp.Currency && currency == item.CustomerCurrency)
                                {
                                    prebookCPFactor = item.CustomerCurrencyFactor;
                                    currency = item.CustomerCurrency;
                                    item.Prebook.FirstOrDefault().CancellationCharge = cp.CancellationCharge = RoundToDecimalPlaces(cp.CancellationCharge, 2);
                                }
                                else
                                {
                                    if (cp?.CancellationPolicyType?.ToLower() == "limit")
                                    {
                                        prebookCPFactor = 1;
                                        cp.Currency = currency;
                                        cp.CancellationCharge = item.Prebook.FirstOrDefault().Price;
                                    }
                                    else
                                    {
                                        if (bookingAction?.IsUseCustomerCurrencyFactor == true)
                                        {
                                            prebookCPFactor = item.CustomerCurrencyFactor;
                                        }
                                        else
                                        {
                                            prebookCPFactor = ExchangeRateFactor(item.RepricerId, cp.Currency, currency, bookingAction.createdOn.ToString(constant.datetype));
                                        }
                                        cp.Currency = currency;
                                        item.Prebook.FirstOrDefault().CancellationCharge = cp.CancellationCharge = RoundToDecimalPlaces(cp.CancellationCharge * prebookCPFactor, 2);
                                    }
                                }
                            }
                        }

                        var resCP = item.Reservation?.CancellationPoliciesBySource?.FirstOrDefault(x => x.Source == "supplier") ?? item.Reservation?.CancellationPoliciesBySource?.FirstOrDefault();
                        var prebookCP = item.Prebook?.FirstOrDefault()?.CancellationPoliciesBySource?.FirstOrDefault(x => x.Source == "supplier") ?? item.Prebook?.FirstOrDefault()?.CancellationPoliciesBySource?.FirstOrDefault();

                        if (item.Prebook.FirstOrDefault().Price > item.Reservation.Price
                                       && actionPrices.OldBooking.Currency == actionPrices.NewBooking.Currency
                                       && actionPrices.OldBooking.Currency == actionPrices.Gain.Currency
                                       )
                        {
                            if (RoundToDecimalPlaces(prebookCP.CancellationCharge) == RoundToDecimalPlaces(item.Prebook.FirstOrDefault().Price)
                                       && prebookCP.Currency == item.Prebook.FirstOrDefault().Currency
                            )
                            {
                                prebookCP.CancellationCharge = RoundToDecimalPlaces(item.Reservation.Price - item.OptimizationProfit);
                            }
                            item.Prebook.FirstOrDefault().Price = RoundToDecimalPlaces(item.Reservation.Price - item.OptimizationProfit);
                        }
                        if (resCP != null && prebookCP != null)
                        {
                            item.MatchedCancellationPolicyGain = RoundToDecimalPlaces(resCP.CancellationCharge - prebookCP.CancellationCharge);
                        }

                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = _className,
                            MethodName = nameof(CalculatedReservationReport),
                            Params = SerializeDeSerializeHelper.Serialize(new
                            {
                                reservationReportResponse?.OverallSummary?.RePricerId,
                                currency,
                                factor,
                                factorOriginal
                            })
                        };
                        _log.Error(irixErrorEntity, ex);
                    }
                }
            }
            return reservationReportResponse;
        }

        public ReservationReportCalculation CalculatedReservationReportSummary(string currency, decimal factor, ReservationReportCalculation reservationReportResponse, decimal factorOriginal)
        {
            try
            {
                reservationReportResponse.LifetimeOptimisedPrebookPrice = RoundToDecimalPlaces(reservationReportResponse.LifetimeOptimisedPrebookPrice * factor);
                reservationReportResponse.LifetimeOptimisedReservationPrice = RoundToDecimalPlaces(reservationReportResponse.LifetimeOptimisedReservationPrice * factor);
                reservationReportResponse.LifetimePossibleOptimisationProfit = RoundToDecimalPlaces(reservationReportResponse.LifetimePossibleOptimisationProfit * factor);
                reservationReportResponse.MissedGain = RoundToDecimalPlaces(reservationReportResponse.MissedGain * factor);
                reservationReportResponse.OpenGain = RoundToDecimalPlaces(reservationReportResponse.OpenGain * factor);
                reservationReportResponse.RealizedGain = RoundToDecimalPlaces(reservationReportResponse.RealizedGain * factor);

                foreach (var summarizedview in reservationReportResponse.SummarizedView)
                {
                    summarizedview.Currency = currency;
                    summarizedview.MaxProfit = RoundToDecimalPlaces((summarizedview.MaxProfit ?? 0.0m) * factor);
                    summarizedview.PrebookPrice = RoundToDecimalPlaces((summarizedview.PrebookPrice ?? 0.0m) * factor);
                    summarizedview.Profit = RoundToDecimalPlaces((summarizedview.Profit ?? 0.0m) * factor);
                    summarizedview.RealizedGain = RoundToDecimalPlaces((summarizedview.RealizedGain ?? 0.0m) * factor);
                    summarizedview.ReservationPrice = RoundToDecimalPlaces((summarizedview.ReservationPrice ?? 0.0m) * factor);
                }

                return reservationReportResponse;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = nameof(CalculatedReservationReportSummary),
                    Params = SerializeDeSerializeHelper.Serialize(new
                    {
                        reservationReportResponse?.RePricerId,
                        currency,
                        factor,
                        factorOriginal
                    })
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public RepricerInvoiceResponse GetRepricerInvoiceResponse(string currency, decimal factor, RepricerInvoiceResponse reservationinvoiceresponse, decimal factorOriginal)
        {
            var preBookFactor = factor;
            var reservationFactor = factor;
            var gainFactor = factor;

            if (reservationinvoiceresponse != null && reservationinvoiceresponse?.ReservationReports?.Any() == true)
            {
                List<ActionsTaken> actionData = new List<ActionsTaken>();
                var repricerId = reservationinvoiceresponse?.ReservationReports?.FirstOrDefault()?.RepricerId ?? 0;

                if (repricerId > 0)
                {
                    if (repricerId > 0)
                    {
                        var prebookRequest = new PrebookRequest
                        {
                            RepricerId = repricerId
                        };
                        actionData = _masterPersistence.GetBookingActionsTaken(prebookRequest);
                    }
                }

                foreach (var item in reservationinvoiceresponse?.ReservationReports)
                {
                    var bookingAction = item?.ActionsTakens?.OrderByDescending(x => x.createdOn)?.FirstOrDefault(x => x.ActionId == 1) ?? item?.ActionsTakens?.FirstOrDefault();
                    PricesOptimizationBooking actionPrices = null;
                    OptimizationBookingResponse optimizationBookingResponse = null;

                    if (item.ActionsTakens == null)
                    {
                        item.ActionsTakens = actionData.Where(x => x?.repricerId == repricerId && x?.reservationId == item?.ReservationId)?.ToList();
                    }
                    if (item.ActionsTakens != null && item?.ActionsTakens?.Any() == true)
                    {
                        try
                        {
                            bookingAction = item?.ActionsTakens?.OrderByDescending(x => x.createdOn)?.FirstOrDefault(x => x.ActionId == 1) ?? item?.ActionsTakens?.FirstOrDefault();
                            if (!string.IsNullOrEmpty(bookingAction?.ResponseBody))
                            {
                                optimizationBookingResponse = (SerializeDeSerializeHelper.DeSerialize<OptimizationBookingResponse>(bookingAction?.ResponseBody));
                                actionPrices = optimizationBookingResponse?.Optimization?.Prices;

                                if (actionPrices?.OldBooking != null)
                                {
                                    item.Reservation.Currency = actionPrices.OldBooking.Currency;
                                    if (actionPrices.OldBooking.Currency == currency)
                                    {
                                        reservationFactor = 1;
                                        item.Reservation.Price = actionPrices.OldBooking.Value;
                                    }
                                    else
                                    {
                                        reservationFactor = ExchangeRateFactor(item.RepricerId, actionPrices.OldBooking.Currency, currency, item.BookingDate);
                                        item.Reservation.Price = actionPrices.OldBooking.Value * reservationFactor;
                                        item.Reservation.Currency = actionPrices.OldBooking.Currency;
                                    }

                                    item.Prebook.FirstOrDefault().Currency = currency;
                                    if (actionPrices.NewBooking.Currency == currency)
                                    {
                                        reservationFactor = 1;
                                        item.Prebook.FirstOrDefault().Price = actionPrices.NewBooking.Value;
                                    }
                                    else
                                    {
                                        if (bookingAction?.IsUseCustomerCurrencyFactor == true)
                                        {
                                            preBookFactor = item.CustomerCurrencyFactor;
                                        }
                                        else
                                        {
                                            preBookFactor = ExchangeRateFactor(item.RepricerId, actionPrices.NewBooking.Currency, currency, bookingAction.createdOn.ToString(constant.datetype));
                                        }
                                        item.Prebook.FirstOrDefault().Price = actionPrices.NewBooking.Value * preBookFactor;
                                    }

                                    item.CustomerCurrency = currency;
                                    if (actionPrices.Gain.Currency == currency)
                                    {
                                        reservationFactor = 1;
                                        item.ProfitAfterCancellation = item.OptimizationProfit = item.CustomerAmount = item.OptimizationProfit = actionPrices.Gain.Value;
                                    }
                                    else
                                    {
                                        gainFactor = ExchangeRateFactor(item.RepricerId, actionPrices.Gain.Currency, currency, bookingAction.createdOn.ToString(constant.datetype));
                                        item.ProfitAfterCancellation = item.OptimizationProfit = item.CustomerAmount = item.OptimizationProfit = actionPrices.Gain.Value * gainFactor;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(ex.ToString());
                        }
                    }

                    var resCP = item.Reservation?.CancellationPoliciesBySource?.FirstOrDefault(x => x.Source == "supplier") ?? item.Reservation?.CancellationPoliciesBySource?.FirstOrDefault();
                    var prebookCP = item.Prebook?.FirstOrDefault()?.CancellationPoliciesBySource?.FirstOrDefault(x => x.Source == "supplier") ?? item.Prebook?.FirstOrDefault()?.CancellationPoliciesBySource?.FirstOrDefault();
                    if (item.Prebook.FirstOrDefault().Price > item.Reservation.Price
                                       && actionPrices.OldBooking.Currency == actionPrices.NewBooking.Currency
                                       && actionPrices.OldBooking.Currency == actionPrices.Gain.Currency
                                       )
                    {
                        if (RoundToDecimalPlaces(prebookCP.CancellationCharge) == RoundToDecimalPlaces(item.Prebook.FirstOrDefault().Price)
                                   && prebookCP.Currency == item.Prebook.FirstOrDefault().Currency
                        )
                        {
                            prebookCP.CancellationCharge = RoundToDecimalPlaces(item.Reservation.Price - item.OptimizationProfit);
                        }
                        item.Prebook.FirstOrDefault().Price = RoundToDecimalPlaces(item.Reservation.Price - item.OptimizationProfit);
                    }
                    if (resCP != null && prebookCP != null)
                    {
                        item.MatchedCancellationPolicyGain = resCP.CancellationCharge - prebookCP.CancellationCharge;
                    }

                    item.ProfitAfterCancellation = RoundToDecimalPlaces(item.ProfitAfterCancellation * preBookFactor);
                    item.Currency = currency;

                    //item.Reservation.Currency = currency;
                    //item.Reservation.Price = RoundToDecimalPlaces(item.Reservation.Price * factor);
                    //item.Reservation.CancellationCurrency = currency;
                    //item.Reservation.CancellationCharge = RoundToDecimalPlaces(item.Reservation.CancellationCharge * factor);

                    //item.Prebook.Currency = currency;
                    //item.Prebook.Price = RoundToDecimalPlaces(item.Prebook.Price * factor);
                    //item.Prebook.CancellationCurrency = currency;
                    //item.Prebook.CancellationCharge = RoundToDecimalPlaces(item.Prebook.CancellationCharge * factor);
                }
            }

            return reservationinvoiceresponse;
        }

        public decimal RoundToDecimalPlaces(decimal value, int decimalPlaces = 2)
        {
            return Math.Round(value, decimalPlaces);
        }

        public ReservationReportCalculation SuperAdminReservationReportSummary(DashboardSummaryRequest reservationReportRequest)
        {
            var report = new ReservationReportCalculation();

            var repricerSchedules = GetClientScheduler();

            if (repricerSchedules != null)
            {
                foreach (var repricerschedule in repricerSchedules)
                {
                    try
                    {
                        var repricerRequest = new DashboardSummaryRequest
                        {
                            RepricerId = repricerschedule.RepricerId,
                            preBookFromDate = reservationReportRequest?.preBookFromDate,
                            preBookToDate = reservationReportRequest?.preBookToDate,
                            FirstCreatedFromDate = reservationReportRequest?.FirstCreatedFromDate,
                            FirstCreatedToDate = reservationReportRequest?.FirstCreatedToDate,
                            isCached = reservationReportRequest?.isCached
                        };
                        var result = _masterPersistence.ReservationReportSummary(repricerRequest);

                        report = AggregateResults(report, result);
                        if (report == null)
                        {
                            report = new ReservationReportCalculation
                            {
                                RePricerId = reservationReportRequest?.RepricerId ?? 0
                            };
                        }
                        else
                        {
                            report.RePricerId = reservationReportRequest?.RepricerId ?? 0;
                        }
                    }
                    catch (Exception ex)
                    {
                    }
                }
            }
            return report;
        }

        public List<ReservationReportCalculation> SuperAdminReservationReport_SummaryNew(DashboardSummaryRequest reportRequest)
        {
            List<ReservationReportCalculation> returnResult = new List<ReservationReportCalculation>();
            returnResult = SuperAdminReservationReport_SummaryNewAsync(reportRequest)?.GetAwaiter().GetResult();
            if (returnResult?.Count > 0)
            {
                return returnResult;
            }
            try
            {
                var isCacheRefresh = !(reportRequest?.isCached ?? true);
                var cachekey = $"SuperAdminReservationReportSummaryNew_{reportRequest.RepricerId}_{reportRequest.FirstCreatedFromDate}_{reportRequest.FirstCreatedToDate}_{reportRequest.preBookFromDate}_{reportRequest.preBookToDate}";

                if (_memoryCache.TryGetValue(cachekey, out List<ReservationReportCalculation> cachedReports))
                {
                    var clonedCache = Common.DeepClone(cachedReports);
                    if (!isCacheRefresh)
                        return clonedCache;
                }

                var redisReports = RedisCacheHelper.Get<List<ReservationReportCalculation>>(cachekey);
                if (redisReports != null && redisReports.Count > 0)
                {
                    _memoryCache.Set(cachekey, redisReports, DateTimeOffset.UtcNow.AddMinutes(5));
                    if (!isCacheRefresh)
                        return Common.DeepClone(redisReports);
                }

                returnResult = Common.DeepClone(redisReports);

                if ((redisReports == null || redisReports.Count == 0) || (_masterPersistence.IsAllowRun(cachekey, TimeSpan.FromMinutes(1)) && isCacheRefresh))
                {
                    returnResult = new List<ReservationReportCalculation>();
                    var repricerSchedules = GetClientScheduler();
                    ReservationReportCalculation aggregatedReport = null;

                    if (repricerSchedules != null)
                    {
                        foreach (var repricerSchedule in repricerSchedules)
                        {
                            var repricerRequest = new DashboardSummaryRequest
                            {
                                RepricerId = repricerSchedule.RepricerId,
                                preBookFromDate = reportRequest?.preBookFromDate,
                                preBookToDate = reportRequest?.preBookToDate,
                                FirstCreatedFromDate = reportRequest?.FirstCreatedFromDate,
                                FirstCreatedToDate = reportRequest?.FirstCreatedToDate,
                                isCached = true
                            };

                            var result = _masterPersistence.ReservationReportSummary(repricerRequest);
                            result.RePricerId = repricerSchedule.RepricerId;

                            returnResult.Add(result); // add original report

                            var tempReport = Common.DeepClone(result); // clone to avoid reference issues

                            aggregatedReport = aggregatedReport == null
                                ? tempReport
                                : AggregateResults(aggregatedReport, tempReport);
                        }

                        if (aggregatedReport != null)
                        {
                            aggregatedReport.RePricerId = 0;
                            returnResult.Add(aggregatedReport);
                        }

                        returnResult = returnResult.OrderBy(r => r.RePricerId).ToList();

                        if (returnResult.Count > 0)
                        {
                            RedisCacheHelper.Set(cachekey, returnResult, TimeSpan.FromMinutes(15), false);
                            _memoryCache.Set(cachekey, returnResult, TimeSpan.FromMinutes(5));
                        }
                    }
                }

                return returnResult;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = nameof(SuperAdminReservationReport_SummaryNew),
                };
                _log.Error(irixErrorEntity, ex);
            }

            return new List<ReservationReportCalculation>();
        }

        private async Task<List<ReservationReportCalculation>> SuperAdminReservationReport_SummaryNewAsync(DashboardSummaryRequest reportRequest)
        {
            var reports = new List<ReservationReportCalculation>();
            var _method = nameof(SuperAdminReservationReport_SummaryNewAsync);

            try
            {
                if (reportRequest == null)
                    return reports;

                var isCacheRefresh = !(reportRequest.isCached ?? true);
                var cacheKey = $"{_method}_{reportRequest.RepricerId}_{reportRequest.FirstCreatedFromDate}_{reportRequest.FirstCreatedToDate}_{reportRequest.preBookFromDate}_{reportRequest.preBookToDate}";

                // 1. Try memory cache
                if (_memoryCache.TryGetValue(cacheKey, out List<ReservationReportCalculation> cachedReports) && !isCacheRefresh)
                {
                    return Common.DeepClone(cachedReports);
                }

                // 2. Try Redis cache
                var redisReports = RedisCacheHelper.Get<List<ReservationReportCalculation>>(cacheKey);
                if (redisReports?.Count > 0 && !isCacheRefresh)
                {
                    _memoryCache.Set(cacheKey, redisReports, DateTimeOffset.UtcNow.AddMinutes(5));
                    return Common.DeepClone(redisReports);
                }

                // 3. Need to rebuild report
                if ((redisReports == null || redisReports.Count == 0) || (_masterPersistence.IsAllowRun(cacheKey, TimeSpan.FromMinutes(1)) && isCacheRefresh))
                {
                    var repricerSchedules = GetClientScheduler();

                    if (repricerSchedules?.Any() == true)
                    {
                        var reportTasks = repricerSchedules.Select(schedule =>
                            Task.Run(() =>
                            {
                                var repricerRequest = new DashboardSummaryRequest
                                {
                                    RepricerId = schedule.RepricerId,
                                    preBookFromDate = reportRequest.preBookFromDate,
                                    preBookToDate = reportRequest.preBookToDate,
                                    FirstCreatedFromDate = reportRequest.FirstCreatedFromDate,
                                    FirstCreatedToDate = reportRequest.FirstCreatedToDate,
                                    isCached = true
                                };

                                var result = _masterPersistence.ReservationReportSummary(repricerRequest);
                                result.RePricerId = schedule.RepricerId;
                                return result;
                            })
                        );

                        var resultList = await Task.WhenAll(reportTasks).ConfigureAwait(false);

                        reports.AddRange(resultList);

                        // Aggregate all returnResult into one
                        var aggregatedReport = resultList
                            .Select(Common.DeepClone)
                            .Aggregate((agg, next) => AggregateResults(agg, next));

                        aggregatedReport.RePricerId = 0;
                        reports.Add(aggregatedReport);

                        reports = reports.OrderBy(r => r.RePricerId).ToList();

                        // Cache
                        RedisCacheHelper.Set(cacheKey, reports, TimeSpan.FromMinutes(15), false);
                        _memoryCache.Set(cacheKey, reports, TimeSpan.FromMinutes(5));
                    }
                }

                return reports;
            }
            catch (Exception ex)
            {
                _log.Error(new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = _method
                }, ex);

                return new List<ReservationReportCalculation>();
            }
        }

        // Helper method to refresh cache in the background

        private void Refresh_SA_ReservationReport(DashboardSummaryRequest reportRequest, string cachekey)
        {
            try
            {
                var reports = new List<ReservationReportCalculation>();
                var aggregatedReport = new ReservationReportCalculation
                {
                    SummarizedView = new List<SummarizedView>()
                };

                var repricerSchedules = GetClientScheduler();

                if (repricerSchedules != null)
                {
                    foreach (var repricerSchedule in repricerSchedules)
                    {
                        var repricerRequest = new DashboardSummaryRequest
                        {
                            RepricerId = repricerSchedule.RepricerId,
                            preBookFromDate = reportRequest?.preBookFromDate,
                            preBookToDate = reportRequest?.preBookToDate,
                            FirstCreatedFromDate = reportRequest?.FirstCreatedFromDate,
                            FirstCreatedToDate = reportRequest?.FirstCreatedToDate,
                            isCached = true
                        };

                        var result = _masterPersistence.ReservationReportSummary(repricerRequest);
                        result.RePricerId = repricerSchedule.RepricerId;

                        reports.Add(result);

                        var tempReport = new ReservationReportCalculation
                        {
                            LifetimeOptimisedReservationPrice = result.LifetimeOptimisedReservationPrice,
                            LifetimeOptimisedPrebookPrice = result.LifetimeOptimisedPrebookPrice,
                            LifetimePossibleOptimisationProfit = result.LifetimePossibleOptimisationProfit,
                            RealizedGain = result.RealizedGain,
                            RealizedGainCount = result.RealizedGainCount,
                            MissedGain = result.MissedGain,
                            MissedGainCount = result.MissedGainCount,
                            OpenGain = result.OpenGain,
                            OpenGainCount = result.OpenGainCount,
                            LifetimeOptimisedBookingCount = result.LifetimeOptimisedBookingCount,
                            TotalUniqueBookings = result.TotalUniqueBookings,
                            FilteredReservationCount = result.FilteredReservationCount,
                            TotalRefundableReservation = result.TotalRefundableReservation,
                            SummarizedView = result.SummarizedView.Select(sv => new SummarizedView
                            {
                                ReservationPrice = sv.ReservationPrice,
                                PrebookPrice = sv.PrebookPrice,
                                Profit = sv.Profit,
                                MaxProfit = sv.MaxProfit,
                                ProfitPercentage = sv.ProfitPercentage,
                                ProfitPercentageFormula = sv.ProfitPercentageFormula,
                                Currency = sv.Currency,
                                RealizedGain = sv.RealizedGain,
                                RealizedGainCount = sv.RealizedGainCount,
                                ReservationsCount = sv.ReservationsCount,
                                ReportType = sv.ReportType
                            }).ToList()
                        };

                        aggregatedReport = AggregateResults(aggregatedReport, tempReport);
                    }

                    aggregatedReport.RePricerId = 0;
                    reports.Add(aggregatedReport);

                    reports = reports.OrderBy(r => r.RePricerId).ToList();

                    if (reports?.Count > 0)
                    {
                        RedisCacheHelper.Set(cachekey, reports, TimeSpan.FromMinutes(120), false);
                        _memoryCache.Set(cachekey, reports, TimeSpan.FromMinutes(30));
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = nameof(Refresh_SA_ReservationReport),
                };
                _log.Error(irixErrorEntity, ex);
            }
            finally
            {
                lock (_lock_SA_RefreshCacheData)
                {
                    _isRunning_SA_RefreshCacheData = false;
                }
            }
        }

        public ReservationReportCalculation AggregateResults(ReservationReportCalculation report, ReservationReportCalculation newReport)
        {
            if (report == null || newReport == null)
                return report ?? newReport;

            report.LifetimeOptimisedReservationPrice += newReport.LifetimeOptimisedReservationPrice;
            report.LifetimeOptimisedPrebookPrice += newReport.LifetimeOptimisedPrebookPrice;
            report.LifetimePossibleOptimisationProfit += newReport.LifetimePossibleOptimisationProfit;
            report.RealizedGain += newReport.RealizedGain;
            report.RealizedGainCount += newReport.RealizedGainCount;
            report.MissedGain += newReport.MissedGain;
            report.MissedGainCount += newReport.MissedGainCount;
            report.OpenGain += newReport.OpenGain;
            report.OpenGainCount += newReport.OpenGainCount;
            report.LifetimeOptimisedBookingCount += newReport.LifetimeOptimisedBookingCount;
            report.TotalUniqueBookings += newReport.TotalUniqueBookings;
            report.FilteredReservationCount += newReport.FilteredReservationCount;
            report.TotalRefundableReservation += newReport.TotalRefundableReservation;

            if (report.SummarizedView == null)
                report.SummarizedView = new List<SummarizedView>();

            foreach (var newView in newReport.SummarizedView ?? Enumerable.Empty<SummarizedView>())
            {
                var existingView = report.SummarizedView.FirstOrDefault(v => v.ReportType == newView.ReportType);
                if (existingView == null)
                {
                    report.SummarizedView.Add(new SummarizedView
                    {
                        ReservationPrice = newView.ReservationPrice,
                        PrebookPrice = newView.PrebookPrice,
                        Profit = newView.Profit,
                        MaxProfit = newView.MaxProfit,
                        ProfitPercentage = newView.ReservationPrice > 0 ? (newView.Profit / newView.ReservationPrice) * 100 : 0,
                        ProfitPercentageFormula = newView.ProfitPercentageFormula,
                        Currency = newView.Currency,
                        RealizedGain = newView.RealizedGain,
                        RealizedGainCount = newView.RealizedGainCount,
                        ReservationsCount = newView.ReservationsCount,
                        ReportType = newView.ReportType
                    });
                }
                else
                {
                    existingView.ReservationPrice += newView.ReservationPrice;
                    existingView.PrebookPrice += newView.PrebookPrice;
                    existingView.Profit += newView.Profit;
                    existingView.MaxProfit += newView.MaxProfit;
                    existingView.ProfitPercentage = existingView.ReservationPrice > 0 ? (existingView.Profit / existingView.ReservationPrice) * 100 : existingView.ProfitPercentage;
                    existingView.RealizedGain += newView.RealizedGain;
                    existingView.RealizedGainCount += newView.RealizedGainCount;
                    existingView.ReservationsCount += newView.ReservationsCount;
                }
            }

            return report;
        }

        public List<ClientConfigScheduler_Hangfire> GetClientScheduler(bool isRefreshCache = false)
        {
            List<ClientConfigScheduler_Hangfire> clientConfigSchedulers = new List<ClientConfigScheduler_Hangfire>();
            try
            {
                clientConfigSchedulers = _clientPersistence.LoadRePricerScheduleConfig(isRefreshCache);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetClientScheduler),
                };
                _log.Error(irixErrorEntity, ex);
            }
            return clientConfigSchedulers;
        }

        public async Task<GiataRoomMapping> ConvertToGiataRoomMapping(string propertyName, string roomName, int repricerId, string reservationId, bool iscacherefresh = false, string providers = null)
        {
            var giataMappingRoomDetails = await GetGiataRoomDetailsAsync(propertyName, roomName, repricerId, reservationId, iscacherefresh, providers);
            if (giataMappingRoomDetails != null)
            {
                return new GiataRoomMapping
                {
                    PropertyName = giataMappingRoomDetails.PropertyName,
                    GroupName = giataMappingRoomDetails.GroupName,
                    GroupID = giataMappingRoomDetails.GroupID,
                    RoomName = giataMappingRoomDetails.RoomName,
                    GroupConfidence = giataMappingRoomDetails.GroupConfidence,
                    BedDetailDescription = giataMappingRoomDetails.BedDetailDescription,
                    RoomCount = giataMappingRoomDetails.RoomCount,
                    Accessible = giataMappingRoomDetails.Accessible,
                    NonRefundable = giataMappingRoomDetails.NonRefundable,
                    Annex = giataMappingRoomDetails.Annex,
                    SingleUse = giataMappingRoomDetails.SingleUse,
                    SharedBed = giataMappingRoomDetails.SharedBed,
                    AverageRoomType = giataMappingRoomDetails.AverageRoomType,
                    AverageRoomClasses = giataMappingRoomDetails.AverageRoomClasses,
                    AverageRoomViews = giataMappingRoomDetails.AverageRoomViews,
                };
            }
            else
            {
                return null;
            }
        }

        public async Task<GiataMappingRoomDetails> GetGiataRoomDetailsAsync(
                  string propertyName,
                  string roomName,
                  int repricerId,
                  string reservationId,
                  bool isCacheRefresh = false,
                  string providers = null)
        {
            GiataMappingRoomDetails cachedResult = null;
            try
            {
                cachedResult = await GetGiataRoomDetailsFromCachedList(
                   propertyName,
                   roomName,
                   repricerId,
                   reservationId,
                   isCacheRefresh,
                  providers);

                if (cachedResult != null)
                {
                    cachedResult = await UpdateGiataCachedResult(cachedResult, repricerId, Convert.ToInt32(reservationId), providers);
                    return cachedResult;
                }
                /*
                // Step 4: Fetch from the data source if no cache entry is found
                if (isCacheRefresh || cachedResult == null)
                {
                    lock (_lock_GetGiataRoomDetailsAsync)
                    {
                        cachedResult = _masterPersistence.GetGiataRoomDetailsAsync(propertyName, roomName, repricerId, reservationId, providers)?.GetAwaiter().GetResult();
                    }
                }
                if (cachedResult != null)
                {
                    await AddGiataRoomDetailsToCachedList(cachedResult);
                }
                //*/
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(MultiSupplierPreBooklog),
                };
                _log.Error(irixErrorEntity, ex);
                // throw;
            }

            return cachedResult;
        }

        private async Task<GiataMappingRoomDetails> UpdateGiataCachedResult(GiataMappingRoomDetails cachedResult, int repricerId, int reservationId, string providers)
        {
            try
            {
                if (cachedResult != null)
                {
                    // Update necessary fields
                    cachedResult.RepricerID = repricerId;
                    cachedResult.ReservationId = reservationId.ToString();
                    cachedResult.Supplier = !string.IsNullOrWhiteSpace(providers) ? providers : cachedResult.Supplier;
                }
            }
            catch (Exception)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(UpdateGiataCachedResult),
                };
            }
            return cachedResult;
        }

        public void MultiSupplierPreBooklog(MultiSupplierlog multiSupplierlog)
        {
            try
            {
                Task.Run(() => _masterPersistence.InsertOrUpdateMultiSupplierLog(multiSupplierlog));
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = constant.ClientService,
                    MethodName = nameof(MultiSupplierPreBooklog),
                };
                _log.Error(irixErrorEntity, ex);
                // throw;
            }
        }

        /// <summary>
        /// Fetch data from cache or db
        /// </summary>
        /// <param name="reservationRequest"></param>
        /// <returns></returns>
        public async Task<RepricerReportResponse> GetReservationReportAndUpdateAction(RepricerReportRequest reservationRequest)
        {
            var reservationReports = new RepricerReportResponse();
            var currentKey = $"GetReservationReports_{reservationRequest?.RepricerId}_{reservationRequest?.PageSize ?? 20}_{reservationRequest?.PageNumber ?? 1}_{reservationRequest?.IsReservationActionTaken}_{reservationRequest?.ReportType}";

            currentKey = $"GetReservationReports_{reservationRequest.RepricerId}_";
            try
            {
                if ((reservationRequest.IsCached == true || reservationRequest.IsCached == null))
                {
                    if (_memoryCache.TryGetValue(currentKey, out RepricerReportResponse cachedReports))
                    {
                        reservationReports = SerializeDeSerializeHelper.DeSerialize<RepricerReportResponse>(SerializeDeSerializeHelper.Serialize(cachedReports));
                        if (reservationReports != null)
                        {
                            var prebookRequest = new PrebookRequest
                            {
                                RepricerId = reservationRequest.RepricerId,
                                ReservationId = reservationRequest.ReservationId,
                                IsCached = true
                            };
                            var isUpdatedFromDB = PopulateActionsTaken(reservationReports.ReservationReports, prebookRequest, currentKey, reservationReports);
                            if (isUpdatedFromDB && reservationReports?.ReservationReports?.Count > 1)
                            {
                                _memoryCache.Set(currentKey, reservationReports, _memoryCacheTimeSpan);
                                //RedisCacheHelper.Set(currentKey, reservationReports, _redisCacheTimeSpan);
                            }
                            return reservationReports;
                        }
                    }
                }
                if ((reservationRequest.IsCached == true || reservationRequest.IsCached == null) && RedisCacheHelper.KeyExists(currentKey))
                {
                    reservationReports = RedisCacheHelper.Get<RepricerReportResponse>(currentKey);
                    if (reservationReports != null)
                    {
                        var prebookRequest = new PrebookRequest
                        {
                            RepricerId = reservationRequest.RepricerId,
                            ReservationId = reservationRequest.ReservationId,
                            IsCached = true
                        };
                        var isUpdatedFromDB = PopulateActionsTaken(reservationReports.ReservationReports, prebookRequest, currentKey, reservationReports);

                        if (isUpdatedFromDB && reservationReports?.ReservationReports?.Count > 1)
                        {
                            _memoryCache.Set(currentKey, reservationReports, _memoryCacheTimeSpan);
                            //RedisCacheHelper.Set(currentKey, reservationReports, _redisCacheTimeSpan);
                        }
                        return reservationReports;
                    }
                }
                reservationReports = new RepricerReportResponse();

                if (reservationReports?.ReservationReports == null || reservationRequest.IsCached == false
                    || reservationReports?.ReservationReports?.Count == 0
                )
                {
                    reservationReports = _masterPersistence.FetchViewReportFromDatabase(reservationRequest);

                    var prebookRequest = new PrebookRequest
                    {
                        RepricerId = reservationRequest.RepricerId,
                        ReservationId = reservationRequest.ReservationId,
                    };
                    PopulateActionsTaken(reservationReports?.ReservationReports, prebookRequest);

                    var unmaaped = reservationReports.ReservationReports
                       .Where(x => (
                                       x.Reservation != null
                                       && !CompareString(x.Reservation?.Supplier ?? string.Empty, x.Prebook?.FirstOrDefault()?.Supplier ?? string.Empty, false)
                                       && (x.Reservation?.MappingDetails == null
                                            || string.IsNullOrWhiteSpace(x?.Reservation?.MappingDetails?.GroupName ?? string.Empty)
                                       )
                                    )
                                    ||
                                    (
                                       x.Prebook != null
                                        && !CompareString(x.Reservation?.Supplier ?? string.Empty, x.Prebook?.FirstOrDefault()?.Supplier ?? string.Empty, false)
                                        && (x.Prebook?.FirstOrDefault()?.MappingDetails == null
                                            || string.IsNullOrWhiteSpace(x?.Prebook?.FirstOrDefault()?.MappingDetails?.GroupName ?? string.Empty)
                                       )
                                    )
                                ).ToList();

                    try
                    {
                        if (unmaaped?.Count > 0)
                        {
                            foreach (var reservation in unmaaped)
                            {
                                try
                                {
                                    if (reservation?.Reservation?.NumberOfRooms > 1)
                                    {
                                        reservation.Reservation.MappingDetails = await GetRoomMappingFromMongoForReports(
                                            reservation.Reservation.HotelName,
                                            reservation.Reservation.Destination,
                                            reservation.Reservation.RoomName.Split(',').FirstOrDefault());

                                        reservation.Prebook.FirstOrDefault().MappingDetails = await GetRoomMappingFromMongoForReports(
                                            reservation.Prebook.FirstOrDefault().HotelName,
                                            reservation.Prebook.FirstOrDefault().Destination,
                                            reservation.Reservation.RoomName.Split(',').FirstOrDefault());
                                    }
                                    else
                                    {
                                        reservation.Reservation.MappingDetails = await GetRoomMappingFromMongoForReports(
                                           reservation.Reservation.HotelName,
                                           reservation.Reservation.Destination,
                                           reservation.Reservation.RoomName);

                                        reservation.Prebook.FirstOrDefault().MappingDetails = await GetRoomMappingFromMongoForReports(
                                            reservation.Prebook.FirstOrDefault().HotelName,
                                            reservation.Prebook.FirstOrDefault().Destination,
                                            reservation.Reservation.RoomName);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    var irixErrorEntity = new IrixErrorEntity
                                    {
                                        ClassName = _className,
                                        MethodName = nameof(GetReservationReport),
                                        Params = SerializeDeSerializeHelper.Serialize(new
                                        {
                                            reservation
                                        })
                                    };
                                    _log.Error(irixErrorEntity, ex);
                                }
                            }
                        }
                        if (reservationReports?.ReservationReports?.Count > 0)
                        {
                            _memoryCache.Set(currentKey, reservationReports, _memoryCacheTimeSpan);
                            RedisCacheHelper.Set(currentKey, reservationReports, _redisCacheTimeSpan);
                        }
                        else
                        {
                            reservationReports = new RepricerReportResponse
                            {
                                Action = new List<ActionInfo>(),
                                ReservationReports = new List<DashboardReportResponseRow>()
                            };
                            RedisCacheHelper.Set(currentKey, reservationReports, _redisCacheTimeSpan);
                        }
                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = _className,
                            MethodName = nameof(GetReservationReport),
                            RePricerId = reservationRequest.RepricerId
                        };
                        _log.Error(irixErrorEntity, ex);
                    }
                }

                /*
                if (reservationRequest.PageNumber != null || reservationRequest.PageNumber != 0)
                {
                    var nextPageRequest = new RepricerReportRequest
                    {
                        RepricerId = reservationRequest.RepricerId,
                        PageNumber = reservationRequest.PageNumber + 1,
                        PageSize = reservationRequest.PageSize
                    };

                    var nextKey = $"GetReservationReports_{nextPageRequest.RepricerId}_{nextPageRequest.PageSize}_{nextPageRequest.PageNumber}";

                    if (!RedisCacheHelper.KeyExists(nextKey))
                    {
                        Task.Run(() =>
                        {
                            var nextPageReports = _masterPersistence.FetchViewReportFromDatabase(nextPageRequest);
                            RedisCacheHelper.Set(nextKey, nextPageReports, TimeSpan.FromHours(5));
                        });
                    }
                }  */
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetReservationReport),
                    RePricerId = reservationRequest.RepricerId
                };
                _log.Error(irixErrorEntity, ex);
            }

            return reservationReports;
        }

        public async Task<List<BoardMapping>> GetAllBoardMappingsAsync(bool isCacheRefresh = false)
        {
            List<BoardMapping> boardMappings = null;
            int retryCount = 0;
            const int maxRetries = 3;
            var method = nameof(GetAllBoardMappingsAsync);

            while (retryCount < maxRetries)
            {
                try
                {
                    var cacheKey = "GetAllBoardMappingsAsync";

                    // If cache refresh is not requested, check the cache first
                    if (!isCacheRefresh)
                    {
                        try
                        {
                            if (_memoryCache.TryGetValue(cacheKey, out List<BoardMapping> result))
                            {
                                var result1 = SerializeDeSerializeHelper.DeSerialize<List<BoardMapping>>(SerializeDeSerializeHelper.Serialize(result));
                                return result1;
                            }
                            boardMappings = RedisCacheHelper.Get<List<BoardMapping>>(cacheKey);
                            if (boardMappings != null)
                            {
                                _memoryCache.Set(cacheKey, boardMappings, TimeSpan.FromDays(5));
                                return boardMappings;
                            }
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = method,
                                Params = "BoardMappings GET from cache"
                            };
                            _log.Error(irixErrorEntity, ex);
                        }
                    }

                    // Either the cache needs to be refreshed, or there was a cache miss
                    boardMappings = await _masterPersistence.GetAllBoardMappingsAsync();

                    if (boardMappings != null && boardMappings.Count > 0)
                    {
                        // Update the cache regardless of the reason we fetched from the database
                        try
                        {
                            _memoryCache.Set(cacheKey, boardMappings, TimeSpan.FromDays(5));
                            RedisCacheHelper.Set(cacheKey, boardMappings, TimeSpan.FromDays(5));
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = method,
                                Params = "BoardMappings SET to cache"
                            };
                            _log.Error(irixErrorEntity, ex);
                        }
                        return boardMappings;
                    }
                    else
                    {
                        // If boardMappings is empty, log and retry
                        retryCount++;
                        if (retryCount < maxRetries)
                        {
                            _log.Info($"Retry {retryCount}/{maxRetries}: BoardMappings is empty, retrying...");
                            await Task.Delay(1000); // Optional: delay before retrying
                        }
                    }
                }
                catch (Exception ex)
                {
                    retryCount++;
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = method
                    };
                    _log.Error(irixErrorEntity, ex);

                    if (retryCount < maxRetries)
                    {
                        _log.Info($"Retry {retryCount}/{maxRetries}: Exception occurred, retrying...");
                        await Task.Delay(1000); // Optional: delay before retrying
                    }
                }
            }

            // After maximum retries, return null if still no result
            _log.Info("Max retries reached. Returning null for GetAllBoardMappingsAsync.");
            return null;
        }

        public async Task<List<GiataMappingRoomDetails>> GetAllRoomDetailsAsync(bool isCacheRefresh = false)
        {
            try
            {
                var key = "GetAllRoomDetailsAsync";
                var timeSpanForCache = TimeSpan.FromHours(12);
                var resultData = default(List<GiataMappingRoomDetails>);
                if (!isCacheRefresh)
                {
                    if (!_memoryCache.TryGetValue(key, out List<GiataMappingRoomDetails> memoryCachedData))
                    {
                        if (memoryCachedData != null && memoryCachedData?.Count > 0)
                        {
                            resultData = Common.DeepClone(memoryCachedData);
                            return resultData;
                        }
                    }
                }
                resultData = RedisCacheHelper.Get<List<GiataMappingRoomDetails>>(key);

                if (resultData?.Any() == false)
                {
                    resultData = new List<GiataMappingRoomDetails>();
                    //_masterPersistence.GetAllRoomDetailsFromDatabaseAsync()?.GetAwaiter().GetResult();
                }

                if (resultData != null && resultData.Count > 0)
                {
                    lock (_lock_GetAllRoomDetailsAsync)
                    {
                        try
                        {
                            _memoryCache.Set(key, resultData, timeSpanForCache);
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = nameof(GetAllRoomDetailsAsync)
                            };
                            _log.Error(irixErrorEntity, ex);
                        }
                        // Update the cache regardless of the reason we fetched from the database
                        RedisCacheHelper.Set(key, resultData, timeSpanForCache);
                    }
                }

                return resultData;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetAllRoomDetailsAsync)
                };
                _log.Error(irixErrorEntity, ex);
                return null;
            }
        }

        public async Task<GiataMappingRoomDetails> GetGiataRoomDetailsFromCachedList(
             string propertyName,
             string roomName,
             int repricerId,
             string reservationId,
             bool isCacheRefresh = false,
             string providers = null)
        {
            try
            {
                var resultData = await GetAllRoomDetailsAsync(isCacheRefresh);
                var result = default(GiataMappingRoomDetails);

                if (resultData != null && resultData.Count > 0)
                {
                    result = resultData
                                    ?.AsEnumerable()
                                    ?.FirstOrDefault(x =>
                                        CompareString(x.PropertyName, propertyName)
                                        && CompareString(x.RoomName, roomName)
                                       );

                    if (result != null)
                    {
                        result = await UpdateGiataCachedResult(result, repricerId, Convert.ToInt32(reservationId), providers);
                        return result;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetGiataRoomDetailsFromCachedList)
                };
                _log.Error(irixErrorEntity, ex);
                return null;
            }
        }

        public async Task<bool> AddGiataRoomDetailsToCachedList(GiataMappingRoomDetails giataMappingRoomDetails)
        {
            try
            {
                var key = "GetAllRoomDetailsAsync";
                bool result = false;

                // Locking around the cache fetching and updating process to ensure thread safety
                lock (_lock_GetAllRoomDetailsAsync)
                {
                    // Fetch the cache data (either from Redis or a fallback method)
                    var resultData = GetAllRoomDetailsAsync()?.GetAwaiter().GetResult();

                    if (resultData == null || resultData.Count == 0)
                    {
                        resultData = GetAllRoomDetailsAsync(true)?.GetAwaiter().GetResult();
                    }
                    // If the cache is empty, initialize an empty list
                    if (resultData == null || resultData.Count == 0)
                    {
                        resultData = new List<GiataMappingRoomDetails>();
                    }

                    // Check if the room already exists in the cached list
                    var existingRoom = resultData
                                        ?.AsEnumerable()
                                        ?.FirstOrDefault(x =>
                                            CompareString(x.PropertyName, giataMappingRoomDetails?.PropertyName)
                                            && CompareString(x.RoomName, giataMappingRoomDetails?.RoomName)
                                           );
                    // If room doesn't exist in the cache, add it
                    if (existingRoom == null && resultData != null && resultData.Count > 0)
                    {
                        try
                        {
                            _memoryCache.Set(key, resultData, TimeSpan.FromDays(20));
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = nameof(GetAllRoomDetailsAsync)
                            };
                            _log.Error(irixErrorEntity, ex);
                        }

                        result = true;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(AddGiataRoomDetailsToCachedList)
                };
                _log.Error(irixErrorEntity, ex);

                return false; // Return false if there was an exception
            }
        }

        public string GetBoardGroupName(string boardName, bool isCacheRefresh = false)
        {
            BoardMapping result = null;
            string boardGroupName = string.Empty;

            try
            {
                boardName = NormalizeString(boardName) ?? string.Empty;

                var boardMappings = GetAllBoardMappingsAsync(isCacheRefresh)?.GetAwaiter().GetResult();

                if (boardMappings != null)
                {
                    result = boardMappings.FirstOrDefault(x => CompareString(x.Board, boardName));
                    boardGroupName = result?.BoardGroupName ?? boardName ?? string.Empty;
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetBoardGroupName)
                };
                _log.Error(irixErrorEntity, ex);
            }

            return boardGroupName;
        }

        private string NormalizeString(string input)
        {
            if (input == null)
                return string.Empty;

            return input
                .ToLowerInvariant()
                .Trim()
                .Replace("\r\n", " ")
                .Replace("\n", " ")
                .Replace("\t", " ")
                .Replace("  ", " ")
                .Replace(" \t", " ")
                .Replace("\t ", " ")
                .Replace("-", " ")
                .Replace("&", " ")
                .Replace("_", " ")
                .Trim();
        }

        /// <summary>
        /// False on both input being empty
        /// </summary>
        /// <param name="s1"></param>
        /// <param name="s2"></param>
        /// <returns></returns>
        private bool CompareString(string s1, string s2, bool isEqualOnNullEmpty = false)
        {
            string normalizedS1 = NormalizeString(s1);
            string normalizedS2 = NormalizeString(s2);
            if (isEqualOnNullEmpty)
            {
                return (string.IsNullOrWhiteSpace(normalizedS1) && string.IsNullOrWhiteSpace(normalizedS2)) || normalizedS1 == normalizedS2;
            }
            else
            {
                return !string.IsNullOrWhiteSpace(normalizedS1) && !string.IsNullOrWhiteSpace(normalizedS2) && normalizedS1 == normalizedS2;
            }
        }

        public async Task<List<string>> DeleteMemoryCacheKeysAsync(string pattern)
        {
            List<string> deletedKeys = new List<string>();
            try
            {
                var keys = await RedisCacheHelper.GetKeysByPatternAsync(pattern);

                if (keys != null && keys.Any())
                {
                    foreach (var key in keys)
                    {
                        try
                        {
                            if (_memoryCache.TryGetValue(key, out var _))
                            {
                                _memoryCache.Remove(key);
                                deletedKeys.Add(key);
                            }
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = nameof(AddGiataRoomDetailsToCachedList),
                                Params = $"pattern ={pattern}"
                            };
                            _log.Error(irixErrorEntity, ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(DeleteMemoryCacheKeysAsync),
                    Params = $"Pattern ={pattern}"
                };
                _log.Error(irixErrorEntity, ex);
            }
            return deletedKeys;
        }

        public async Task<List<string>> DeleteMemoryCacheKeyByRepricerId(int RepricerId = 0)
        {
            List<string> deletedKeys = new List<string>();
            try
            {
                var patterns = GetAllPatternMatchingKeys(RepricerId);
                if (patterns != null && patterns.Count() > 0)
                {
                    foreach (var pattern in patterns)
                    {
                        try
                        {
                            var deletedKeysResult = await DeleteMemoryCacheKeysAsync(pattern);
                            if (deletedKeysResult?.Any() == true)
                            {
                                deletedKeys.AddRange(deletedKeysResult);
                            }
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = nameof(DeleteMemoryCacheKeyByRepricerId),
                                Params = $"pattern ={pattern}"
                            };
                            _log.Error(irixErrorEntity, ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(DeleteMemoryCacheKeyByRepricerId),
                    Params = $"RepricerId ={RepricerId}"
                };
                _log.Error(irixErrorEntity, ex);
            }
            return deletedKeys;
        }

        public List<string> GetAllPatternMatchingKeys(int repricerId = 0)
        {
            try
            {
                var keys = new List<string>
                {
                    "GetPreBookCriteriaDBAll_{repricerId}_",
                    "RepricerId_{RepricerId}_actionTakenList_LoadFromDatabaseWithRetry",
                    "GetInvoceDataFromDB_{RepricerId}_",
                    "ReservationReportSummary_{RepricerId}_",
                    "supplierCountResponse_{repricerId}_",
                    "GetMultiSupplierReservationReport_{RepricerId}_",
                    "SuperAdminReservationReportSummaryNew_{RepricerId}_",
                    "GetReservationReports_{RepricerId}_"
                };

                var processedKeys = new List<string>();

                foreach (var key in keys)
                {
                    var updatedKey = key.Replace("{RepricerId}", repricerId.ToString())
                                        .Replace("{repricerId}", repricerId.ToString());

                    processedKeys.Add(updatedKey);
                }

                return processedKeys;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetAllPatternMatchingKeys),
                    Params = $"RepricerId ={repricerId}"
                };
                _log.Error(irixErrorEntity, ex);
                return null;
            }
        }

        public async Task<List<GiataDbResp>> InsertGiataRoomsBulkAsync(List<BulkGiataRoom> rooms)
        {
            try
            {
                var giatadbresp = await _masterPersistence.InsertGiataRoomsBulkAsync(rooms);
                return giatadbresp;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(InsertGiataRoomsBulkAsync),
                    Params = $"rooms ={rooms}"
                };
                _log.Error(irixErrorEntity, ex);
                return null;
            }
        }

        private async Task<GiataRoomMapping> GetRoomMappingFromMongoForReports(string hotelname, string destination, string roomname)
        {
            try
            {
                var propertyName = $"{hotelname}";
                if (!string.IsNullOrWhiteSpace(destination))
                {
                    propertyName = $"{propertyName},{destination}";
                }

                var cachedResult = await _giataService.GetGiataRoom(propertyName, roomname);

                return cachedResult;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetRoomMappingFromMongoForReports),
                    Params = SerializeDeSerializeHelper.Serialize(new
                    {
                        hotelname,
                        destination,
                        roomname
                    })
                };
                _log.Error(irixErrorEntity, ex);
            }
            return new GiataRoomMapping();
        }

        private List<ExchangeRate> LoadExchangeRates(int repricerId, string? exchangeRateDate = null)
        {
            var _method = nameof(LoadExchangeRates);
            exchangeRateDate = Common.GetExchangeRateDate(exchangeRateDate);
            var cacheKey = $"CurrencyExchange_{repricerId}_{exchangeRateDate}";

            // Try to get cached data from memory
            if (_memoryCache.TryGetValue(cacheKey, out List<ExchangeRate> cachedExchangeRates))
            {
                var cachedExchangeRates1 = SerializeDeSerializeHelper.DeSerialize<List<ExchangeRate>>(SerializeDeSerializeHelper.Serialize(cachedExchangeRates));
                if (cachedExchangeRates1?.Any() == true)
                {
                    return cachedExchangeRates1;
                }
            }

            // Load data from database if not in cache
            var exchangeRate = _reservationPersistence.LoadExchangeRate(repricerId, exchangeRateDate);

            if (exchangeRate != null)
            {
                // Set data in memory cache
                _memoryCache.Set(cacheKey, exchangeRate, TimeSpan.FromHours(4));
            }

            return exchangeRate;
        }

        private decimal ExchangeRateFactor(int repricerId, string toCurrency, string fromCurrency, string? exchangeRateDate = null)
        {
            var result = 1.0m;
            var _method = nameof(ExchangeRateFactor);
            if (fromCurrency == toCurrency)
            {
                return result;
            }

            exchangeRateDate = Common.GetExchangeRateDate(exchangeRateDate);

            try
            {
                var msg = new
                {
                    Message = "Exchange rate not found !",
                    repricerId,
                    fromCurrency,
                    toCurrency
                };
                var msgNode = SerializeDeSerializeHelper.Serialize(msg);

                var exchangerate = LoadExchangeRates(repricerId, exchangeRateDate);

                if (exchangerate != null)
                {
                    var currencyExchangeRate = exchangerate
                    ?.OrderByDescending(x => x.ExchangeRateDate)
                    ?.FirstOrDefault(
                        r =>
                        r.RePricerId == repricerId
                        && r.FromCurrency == fromCurrency
                        && r.ToCurrency == toCurrency
                        && (r.ExchangeRateDate == exchangeRateDate || string.IsNullOrEmpty(r.ExchangeRateDate))
                    )
                    ??
                    exchangerate
                    ?.OrderByDescending(x => x.ExchangeRateDate)
                    ?.FirstOrDefault(
                        r =>
                        r.RePricerId == repricerId
                        && r.FromCurrency == fromCurrency
                        && r.ToCurrency == toCurrency
                    );

                    if (currencyExchangeRate != null)
                    {
                        return currencyExchangeRate.Factor ?? result;
                    }
                    else
                    {
                        throw new Exception(SerializeDeSerializeHelper.Serialize(msg));
                    }
                }
                else
                {
                    throw new Exception(SerializeDeSerializeHelper.Serialize(msg));
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = _method,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public async Task<List<DailyOptimizationReportModel>> GetDailyOptimizationReportAsync(
            int? repricerId,
            string? reportType,
            DateTime? reportDateFrom,
            DateTime? reportDateTo,
            bool isCacheRefresh = false)
        {
            try
            {
                if (reportDateFrom.HasValue && reportDateTo.HasValue)
                {
                    var daysDifference = (reportDateTo.Value - reportDateFrom.Value).TotalDays;
                    if (daysDifference > 7)
                        throw new ArgumentException("The date range cannot exceed 7 days.");
                }

                // Generate a unique cache key based on input parameters
                var cacheKey = GenerateCacheKey(repricerId, reportType, reportDateFrom, reportDateTo);
                var _cacheDuration = TimeSpan.FromDays(1);

                // Attempt to retrieve from in-memory cache
                if (!isCacheRefresh && _memoryCache.TryGetValue(cacheKey, out List<DailyOptimizationReportModel> cachedReports))
                {
                    var cachedReportsCopy = SerializeDeSerializeHelper.DeSerialize<List<DailyOptimizationReportModel>>(SerializeDeSerializeHelper.Serialize(cachedReports));
                    if (cachedReportsCopy?.Count > 0)
                    {
                        return cachedReports;
                    }
                }

                // Attempt to retrieve from Redis cache
                if (!isCacheRefresh)
                {
                    var redisReports = RedisCacheHelper.Get<List<DailyOptimizationReportModel>>(cacheKey);
                    if (redisReports != null && redisReports.Count > 0)
                    {
                        // Set in-memory cache for faster subsequent access
                        _memoryCache.Set(cacheKey, redisReports, _cacheDuration);
                        return redisReports;
                    }
                }

                // If not found in cache, retrieve from database
                var reports = await _masterPersistence.GetDailyOptimizationReportFromDBAsync(repricerId, reportType, reportDateFrom, reportDateTo);

                if (reports != null && reports.Count > 0)
                {
                    // Set both in-memory and Redis caches
                    _memoryCache.Set(cacheKey, reports, _cacheDuration);
                    RedisCacheHelper.Set(cacheKey, reports, _cacheDuration);
                }

                return reports;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetDailyOptimizationReportAsync),
                };
                _log.Error(irixErrorEntity, ex);
                throw ex;
            }
        }

        private string GenerateCacheKey(int? repricerId, string? reportType, DateTime? reportDateFrom, DateTime? reportDateTo)
        {
            return $"DailyOptimizationReport:RepricerId={repricerId ?? 0};ReportType={reportType ?? "ALL"};From={reportDateFrom?.ToString("yyyyMMdd") ?? "NULL"};To={reportDateTo?.ToString("yyyyMMdd") ?? "NULL"}";
        }

        public void RefreshDbAndCachedReport(int repricerId, int reservationId, bool isSameSupplier)
        {
            var startTimeItem = DateTime.UtcNow;
            var supplier = isSameSupplier ? "SAME_SUPPLIER" : "MULTI_SUPPLIER";
            var itemJobName = $"{repricerId.ToString("000")}_8_2_UPDATE_RRD_{supplier}".ToUpper();

            var task = Task.Run(() =>
            {
                try
                {
                    _reservationPersistence.InsertReportinTable(repricerId);

                    var viewReuestCommon = new RepricerReportRequest
                    {
                        RepricerId = reservationId,
                        IsCached = false,
                    };
                    GetReservationReport(viewReuestCommon);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = itemJobName,
                        RePricerId = repricerId
                    };
                    _log.Error(irixErrorEntity, ex);
                }
                _log.InfoV1(repricerId, _className, itemJobName, $"{nameof(RefreshDbAndCachedReport)}", startTimeItem, true, "END");
            });
        }

        /// <summary>
        /// Get additional prebook options (ranks 2-3) for multiple prebook display
        /// Returns ReservationAndPreBookCompare objects that can be added to the prebook list
        /// </summary>
        /// <param name="reservationRequest">The reservation request parameters</param>
        /// <returns>List of additional prebook options as ReservationAndPreBookCompare</returns>
        public List<ReservationAndPreBookCompare> GetAdditionalPrebookOptions(RepricerReportRequest reservationRequest)
        {
            try
            {
                var additionalData = _masterPersistence.GetAdditionalPrebookOptions(reservationRequest);
                var additionalPrebooks = new List<ReservationAndPreBookCompare>();

                if (additionalData?.Any() == true)
                {
                    // Extract prebook data from DashboardReportResponseRow objects
                    foreach (var data in additionalData)
                    {
                        // The prebook data is now in the Prebook list, get the first (and should be only) item
                        var prebookData = data.Prebook?.FirstOrDefault();
                        if (prebookData != null)
                        {
                            // Set rank based on stored procedure logic (ranks 2-3)
                            prebookData.Rank = prebookData.Rank > 0 ? prebookData.Rank : 2;
                            additionalPrebooks.Add(prebookData);
                        }
                    }
                }

                return additionalPrebooks;
            }
            catch (Exception ex)
            {
                // Log error but don't break the main flow
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetAdditionalPrebookOptions)
                };
                _log.Error(irixErrorEntity, ex);
                return new List<ReservationAndPreBookCompare>();
            }
        }

        /// <summary>
        /// Combine primary prebook response with additional prebook options
        /// Adds additional prebooks to the existing prebook list
        /// </summary>
        /// <param name="primaryResponse">Primary reservation report response</param>
        /// <param name="additionalPrebooks">Additional prebook options</param>
        /// <returns>Combined response with multiple prebook options</returns>
        public ReservationReportResponse CombinePrebookOptions(ReservationReportResponse primaryResponse, List<ReservationAndPreBookCompare> additionalPrebooks)
        {
            try
            {
                if (primaryResponse?.Data == null || !additionalPrebooks.Any())
                {
                    return primaryResponse;
                }

                // Process each reservation report to add additional prebooks
                foreach (var reservationReport in primaryResponse.Data)
                {
                    // Find additional prebooks for this specific reservation
                    var matchingAdditionalPrebooks = additionalPrebooks
                        .Where(ap => ap.ReservationId == reservationReport.ReservationId &&
                                   ap.RepricerId == reservationReport.RepricerId)
                        .OrderBy(ap => ap.Rank) // Ensure proper ordering (rank 2, then rank 3)
                        .ToList();

                    // Add additional prebooks to the existing prebook list
                    if (matchingAdditionalPrebooks.Any() && reservationReport.Prebook != null)
                    {
                        reservationReport.Prebook.AddRange(matchingAdditionalPrebooks);
                    }
                }

                return primaryResponse;
            }
            catch (Exception ex)
            {
                // Log error but return original response to avoid breaking the API
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(CombinePrebookOptions)
                };
                _log.Error(irixErrorEntity, ex);
                return primaryResponse;
            }
        }


    }
}