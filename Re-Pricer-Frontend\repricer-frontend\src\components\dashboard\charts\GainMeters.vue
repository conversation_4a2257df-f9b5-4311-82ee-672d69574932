<template>
    <ConfirmPopup :pt="informationTooltip"></ConfirmPopup>

    <div>
        <div v-if="selectedPeriod">
            <Button type="button" text icon="pi pi-filter-fill" @click="toggle" aria-haspopup="true" size="small"
                aria-controls="CPRange_menu" :label="selectedPeriod.short" class="d-flex ms-auto" />
            <Menu ref="CPRange" id="CPRange_menu" :model="items" :popup="true" style="min-width: auto;" />
        </div>
        <MeterGroup :value="value" labelPosition="start">
            <template #label="{ value }">
                <div class="flex flex-wrap gap-3">
                    <template v-for="val of value" :key="val.label">
                        <Card class="flex-1">
                            <template #content>
                                <div class="flex justify-content-between gap-2">
                                    <div class="flex flex-column gap-1">
                                        <span class="text-secondary text-sm text-nowrap align-items-center d-flex">
                                            {{ val.label }}
                                            <Button @click="showInformation($event, val)" icon="pi pi-info-circle"
                                                class="m-0 p-0" link></Button>
                                        </span>
                                        <span class="font-bold text-lg">{{ val.value }}</span>
                                    </div>
                                    <Tag class="align-self-center inline-flex justify-content-center align-items-center text-center"
                                        severity="secondary"
                                        :style="{ backgroundColor: `${val.color}`, color: '#ffffff' }">
                                        {{ val.count }}
                                    </Tag>
                                </div>
                            </template>
                        </Card>
                    </template>
                </div>
            </template>
            <template #meter="slotProps">
                <span :class="slotProps.class"
                    :style="{ background: `${slotProps.value.color}`, width: `${slotProps.value.width}%` }" />
            </template>
        </MeterGroup>
    </div>
</template>
<script>

import Button from 'primevue/button';
import Card from 'primevue/card';
import MeterGroup from 'primevue/metergroup';
import Tag from 'primevue/tag';
import ConfirmPopup from 'primevue/confirmpopup';
import Menu from 'primevue/menu';

const documentStyle = getComputedStyle(document.documentElement);

export default {
    name: "GainMeters",
    components: { MeterGroup, Card, Button, Tag, ConfirmPopup, Menu },
    data() {
        return {
            informationTooltip: {
                footer: {
                    class: "d-none"
                },
                content: {
                    class: "fz-r0_9"
                },
                icon: {
                    class: "fz-r0_9"
                }
            },
            items: [
                {
                    label: 'Last 30 days',
                    days: 30,
                    short: '1M',
                    command: this.updatePeriodFilter
                },
                {
                    label: 'Last 60 days',
                    days: 60,
                    short: '2M',
                    command: this.updatePeriodFilter
                },
                {
                    label: 'Last 90 days',
                    days: 90,
                    short: '3M',
                    command: this.updatePeriodFilter
                },
                {
                    label: 'Last 365 days',
                    days: 365,
                    short: '1Y',
                    command: this.updatePeriodFilter
                },
                {
                    label: 'All',
                    days: 0,
                    short: 'All',
                    command: this.updatePeriodFilter
                }
            ],
            selectedPeriod: null
        };
    },
    props: ['summary', 'getSummaryByDate'],
    watch: {},
    computed: {
        value() {
            const totalGain = this.summary.missedGain + this.summary.realizedGain + this.summary.openGain;
            const data = [
                {
                    label: 'Realized Gain',
                    color: documentStyle.getPropertyValue('--green-500'),
                    value: this.$filters.priceDisplay(this.summary.realizedGain, this.summary.summarizedView[0].currency, true),
                    width: (this.summary.realizedGain / totalGain) * 100,
                    count: this.summary.realizedGainCount,
                    info: "Lorem ipsum dolor sit amet, consectetur adipiscing elit."
                },
                {
                    label: 'Current Opportunities',
                    color: documentStyle.getPropertyValue('--blue-500'),
                    value: this.$filters.priceDisplay(this.summary.openGain, this.summary.summarizedView[0].currency, true),
                    width: (this.summary.openGain / totalGain) * 100,
                    count: this.summary.openGainCount,
                    info: "Fusce a ante maximus, commodo turpis sit amet, venenatis mauris."
                },
                {
                    label: 'Lost Opportunity',
                    color: documentStyle.getPropertyValue('--red-500'),
                    value: this.$filters.priceDisplay(this.summary.missedGain, this.summary.summarizedView[0].currency, true),
                    width: (this.summary.missedGain / totalGain) * 100,
                    count: this.summary.missedGainCount,
                    info: "Vestibulum eu magna placerat, consequat felis et, euismod arcu."
                },
                {
                    label: 'Lifetime Possible Gain',
                    color: documentStyle.getPropertyValue('--pink-500'),
                    value: this.$filters.priceDisplay(this.summary.lifetimePossibleOptimisationProfit, this.summary.summarizedView[0].currency, true),
                    width: 0,
                    count: this.summary.missedGainCount + this.summary.realizedGainCount + this.summary.openGainCount,
                    info: "Sed vitae quam in urna hendrerit tempus malesuada ac mi."
                }
            ];
            return data
        }
    },
    async mounted() {
        this.selectedPeriod = this.items[3];

    },
    methods: {
        toggle(event) {
            this.$refs.CPRange.toggle(event);
        },
        updatePeriodFilter({ item }) {

            this.selectedPeriod = item;
            this.getSummaryByDate(this.selectedPeriod.days);
        },
        showInformation(event, value) {
            this.$confirm.require({
                target: event.currentTarget,
                message: value.info,
                icon: 'pi pi-info-circle',
                rejectClass: 'd-none',
                acceptClass: 'd-none'
            });
        }
    }
}

</script>
