﻿using Irix.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Irix.Persistence.Contract
{
    public interface IMasterPersistence
    {
        List<CurrencyExchangeRates> LoadExchangeRates();

        List<EmailTemplate> LoadEmailData(int RePricerId, DateTime? fromDate, DateTime? toDate);

        List<EmailContentResult> LoadEmailContent(int RePricerId, DateTime? fromDate, DateTime? toDate);

        GetEmailContentData LoadEmailContentData(int RePricerId, DateTime? fromDate, DateTime? toDate);

        PreBookResult GetPreBookResult(PrebookRequest prebookRequest);

        GetEmailContentData LoadSupplierEmailContentData(int RePricerId, DateTime? fromDate, DateTime? toDate);

        Task InsertUserMapping(int RePricerId, string UserId);

        Task<List<UserMappingResult>> GetUserMapping(int RePricerId);

        List<ActionsTaken> GetBookedAction(PrebookRequest prebookRequest);

        PreBookResult GetRecommendationEdgeCaseCP(PrebookRequest prebookRequest);

        PreBookResult GetRecommendationEdgeCasePR(PrebookRequest prebookRequest);

        List<ActionsTaken> GetBookingActionsTaken(PrebookRequest prebookRequest);

        decimal GetRealizedGain(int RepricerId);

        List<MaxProfit> GetMaxProfitData(int RepricerId, string procedurename);

        ReservationReportCalculation ReservationReportSummary(DashboardSummaryRequest reservationReportRequest, bool iscaching = true);

        List<DateWiseResponse> DateWiseCount(DateWiseRequest prebookRequest, bool iscaching = true);

        RepricerReportResponse GetInvoiceData(CommonReportRequest reservationRequest);

        ExternalApi LoadExternalApiData(string service);

        Task InsertRoomDetailsAsync(GiataMappingRoomDetails roomDetails, string reservationId, int repricerId);

        Task<GiataMappingRoomDetails> GetGiataRoomDetailsAsync(string propertyName, string roomName, int repricerId, string reservationId, string providers = null);

        int InsertOrUpdateMultiSupplierLog(MultiSupplierlog multiSupplierlog);

        Task<List<BoardMapping>> GetAllBoardMappingsAsync();

        RepricerReportResponse FetchViewReportFromDatabase(RepricerReportRequest request);

        Task<List<GiataMappingRoomDetails>> GetAllRoomDetailsFromDatabaseAsync();

        Task<List<GiataDbResp>> InsertGiataRoomsBulkAsync(List<BulkGiataRoom> rooms);

        Task<List<DailyOptimizationReportModel>> GetDailyOptimizationReportFromDBAsync(
            int? repricerId,
            string? reportType,
            DateTime? reportDateFrom,
            DateTime? reportDateTo);

        /// <summary>
        /// Checks method and allow its run , uses redis to track key
        /// if no previous execution or given time has passed, then allow the method
        /// </summary>
        /// <param name="taskKey"></param>
        /// <returns></returns>
        bool IsAllowRun(string taskKey, TimeSpan timeSpan);

        /// <summary>
        /// Get additional prebook options (ranks 2-3) from ReservationReportDetailsAdditionalPrebook table
        /// </summary>
        /// <param name="reservationRequest">The reservation request parameters</param>
        /// <returns>List of additional prebook options</returns>
        List<DashboardReportResponseRow> GetAdditionalPrebookOptions(RepricerReportRequest reservationRequest);
    }
}