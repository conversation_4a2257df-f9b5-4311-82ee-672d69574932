<template>

	<Card class="flex-fill" :pt="passThrough">
		<template #title>
			<div class="d-flex flex-column flex-xl-row gap-3 justify-content-between">
				<div class="align-self-center">Optimizations per day</div>
				<div class="align-items-center d-flex gap-3 me-auto me-lg-0 ms-auto">
					<SelectButton v-model="chartTypeValue" :options="chartTypeOptions" optionLabel="value"
						dataKey="value" aria-labelledby="custom">
						<template #option="slotProps">
							<i :class="slotProps.option.icon"></i>
						</template>
					</SelectButton>
					<Calendar class="align-self-center" v-model="perDayOptimizationRange" view="month"
						dateFormat="MM yy" :manualInput="false" :maxDate="new Date()" showIcon />
				</div>
			</div>
		</template>
		<template #content>
			<Chart v-if="chartTypeValue && chartTypeValue.value == 'line'" type="line" class="w-100" :data="chartData"
				:options="chartOptions" :pt="passThrough" />
			<Chart v-if="chartTypeValue && chartTypeValue.value == 'bar'" type="bar" class="w-100" :data="chartData"
				:options="chartOptions" :pt="passThrough" />
		</template>
	</Card>
</template>
<script>
import Chart from 'primevue/chart';
import Button from 'primevue/button';
import Card from 'primevue/card';
import moment from 'moment';
import Calendar from 'primevue/calendar';
import SelectButton from 'primevue/selectbutton';
import { useRepricerStore } from '@/stores/useRepricerStore';
import { useAuthStore } from '@/stores/useAuthStore';

export default {
	name: "MonthWisePreBooks",
	components: { Chart, Button, Card, Calendar, SelectButton },
	data() {
		return {
			chartData: null,
			chartOptions: null,
			passThrough: {
				body: {
					class: "h-100"
				},
				root: {
					class: "flex-fill"
				}
			},
			perDayOptimizationRange: moment().startOf('month').format('MMMM yy'),
			datesList: [],
			datesListPreviousMonth: [],
			chartTypeValue: {
				value: 'line'
			},
			chartTypeOptions: [
				{ icon: 'pi pi-chart-line', value: 'line' },
				{ icon: 'pi pi-chart-bar', value: 'bar' },
			],
			preBooks: null
		};
	},
	props: [
		'getAllDatesOfMonth',
		'getDayWiseData',
	],
	computed: {
		dateLabels() {
			const dates = [];
			for (let d = 1; d <= 31; d++) {
				dates.push(d)
			}
			return dates; // this.datesList.map(d => d.split('-')[2])
		}
	},
	watch: {
		perDayOptimizationRange: function (newValue) {
			this.getSevenDaysData();
		}
	},
	mounted() {
		this.getSevenDaysData();
		this.chartOptions = this.setChartOptions();
	},
	methods: {
		async getSevenDaysData() {
			let dayWiseCount = [];
			const repricer = useRepricerStore();
			const auth = useAuthStore();
			const userInfo = auth.userInfo;

			if (new Date(this.perDayOptimizationRange).toString().toLowerCase() == "invalid date") {
				this.perDayOptimizationRange = "01 " + this.perDayOptimizationRange
			}
			const givenDate = moment(new Date(this.perDayOptimizationRange));

			const params = {
				"repricerId": userInfo.repricerUserId || this.$route.params.repricerId,
				"fromDate": moment(givenDate).startOf('month').format('YYYY-MM-DD')
			}

			if (givenDate.isSame(moment(), 'month')) {
				params["toDate"] = moment().format('YYYY-MM-DD')
			} else {
				params["toDate"] = moment(givenDate).endOf('month').format('YYYY-MM-DD')
			}

			const currentMonth = await repricer.GetDateWiseReservationCount({
				...params
			});

			if (currentMonth) {
				dayWiseCount = currentMonth.count;
				let dayWiseCountPreviousMonth = [];
				const prevMonthFirstDate = moment(new Date(this.perDayOptimizationRange)).subtract(1, 'months').startOf('month');
				params["fromDate"] = moment(prevMonthFirstDate).startOf('month').format('YYYY-MM-DD');
				params["toDate"] = moment(prevMonthFirstDate).endOf('month').format('YYYY-MM-DD');
				const prevMonth = await repricer.GetDateWiseReservationCount({
					...params
				});
				dayWiseCountPreviousMonth = prevMonth.count;
				this.chartData = this.setChartData(dayWiseCount, dayWiseCountPreviousMonth);
			}
		},
		setChartData(dayWiseCount, dayWiseCountPreviousMonth) {
			const documentStyle = getComputedStyle(document.documentElement);
			const chartData = {
				labels: this.dateLabels,
				datasets: [
					{
						label: 'Selected Month',
						backgroundColor: documentStyle.getPropertyValue('--primary-color'),
						borderColor: documentStyle.getPropertyValue('--primary-color'),
						data: dayWiseCount,
					},
					{
						label: 'Previous Month',
						backgroundColor: documentStyle.getPropertyValue('--text-color-secondary'),
						borderColor: documentStyle.getPropertyValue('--text-color-secondary'),
						data: dayWiseCountPreviousMonth,
					}
				]
			}
			return chartData;
		},
		setChartOptions() {
			const documentStyle = getComputedStyle(document.documentElement);
			const textColor = documentStyle.getPropertyValue('--text-color');
			const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
			const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

			return {
				indexAxis: 'x',
				maintainAspectRatio: false,
				responsive: true,
				plugins: {
					legend: {
						labels: {
							color: textColor
						}
					}
				},
				scales: {
					x: {
						ticks: {
							color: textColorSecondary,
							font: {
								weight: 500
							}
						},
						grid: {
							display: false,
							drawBorder: false,
						},
						title: {
							display: true,
							text: "Day of month"
						}
					},
					y: {
						ticks: {
							color: textColorSecondary
						},
						grid: {
							color: surfaceBorder,
							drawBorder: false
						},
						title: {
							display: true,
							text: "Optimizations"
						}
					}
				}
			};
		}
	}
}

</script>
