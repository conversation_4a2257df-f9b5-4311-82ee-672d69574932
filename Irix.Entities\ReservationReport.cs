﻿using MongoDB.Bson.Serialization.Conventions;
using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Nodes;

namespace Irix.Entities
{
    public class RepricerReportResponse
    {
        public List<DashboardReportResponseRow> ReservationReports
        {
            get; set;
        }

        public List<ActionInfo> Action
        {
            get; set;
        }
    }

    public class RepricerInvoiceResponse
    {
        public List<DashboardReportResponseRow> ReservationReports
        {
            get; set;
        }
    }

    public class DashboardReportResponseRow
    {
        public int RepricerId
        {
            get; set;
        }

        public int ReservationId
        {
            get; set;
        }

        public string ReportType
        {
            get;
            set;
        }

        /// <summary>
        ///  Status of new reservation that is created after optimization
        /// </summary>
        public string? NewReservationStatus
        {
            get;
            set;
        }

        public int NewReservationId
        {
            get; set;
        }

        public string OptimizationDate
        {
            get; set;
        }

        //public int PreBookId { get; set; }

        /// <summary>
        /// Reservation Creation Date
        /// </summary>
        public string? BookingDate
        {
            get; set;
        }

        /// <summary>
        /// Prebook Creation Date
        /// </summary>
        public string? CreatedDate
        {
            get; set;
        }

        public decimal ProfitAfterCancellation
        {
            get; set;
        }

        /// <summary>
        /// Prebook recommendation profit
        /// </summary>
        public decimal OptimizationProfit
        {
            get; set;
        }

        public bool IsCancellationPolicyMatched
        {
            get; set;
        }

        public string? CPStatus
        {
            get; set;
        }

        public int CPDaysGain
        {
            get; set;
        }

        public decimal MatchedCancellationPolicyGain
        {
            get; set;
        }

        public string? Token
        {
            get; set;
        }

        public string? AvailabilityToken
        {
            get; set;
        }

        //public int ReservationNumberOfRooms { get; set; }

        //##TODO
        //public int PrebookNumberOfRooms { get; set; }

        public bool? IsReservationActionTaken
        {
            get; set;
        }

        public int ActionId
        {
            get; set;
        }

        public List<ActionsTaken>? ActionsTakens
        {
            get; set;
        }

        public string? Currency
        {
            get; set;
        }

        public decimal CustomerAmount
        {
            get; set;
        }

        public decimal CustomerCurrencyFactor
        {
            get; set;
        }

        public string? CustomerCurrency
        {
            get; set;
        }

        public decimal CurrencyFactorToEur
        {
            get; set;
        }

        public ClientConfig? ClientConfigiurationUsed
        {
            get; set;
        }

        public ReservationAndPreBookCompare? Reservation
        {
            get; set;
        }

        public ReservationAndPreBookCompare? Prebook
        {
            get; set;
        }

        public ResellerDetails? ResellerDetail
        {
            get; set;
        }

        // Additional properties needed for multiple prebook functionality
        public decimal PreBookPrice { get; set; }
        public string? prebooksupplier { get; set; }
        public DateTime? checkin { get; set; }
        public DateTime? checkout { get; set; }
        public int Prebookadultcount { get; set; }
        public string? PrebookChildAges { get; set; }
        public string? prebookhotelname { get; set; }
        public string? PreBookRoomName { get; set; }
        public string? PrebookRoomInfo { get; set; }
        public string? PreBookRoomBoard { get; set; }
        public string? roomType { get; set; }
        public string? PreBookRoomIndex { get; set; }
        public int NumberOfRooms { get; set; }
        public string? prebookdestination { get; set; }
        public string? ReservationStatus { get; set; }
        public DateTime? MatchedPreBookCancellationDate { get; set; }
        public string? MatchedPreBookCancellationChargeByPolicy { get; set; }
        public string? pricedifferencecurrency { get; set; }
        public string? PreBookCancellationType { get; set; }
        public string? SearchGiataMappingId { get; set; }
        public string? PrebookRoomBoardGroup { get; set; }
        public DateTime? UpdatedOn { get; set; }
        public int PrebookRank { get; set; } = 1; // Default to rank 1 for primary prebooks
    }

    public class ResellerDetails
    {
        public string? ResellerName
        {
            get;
            set;
        }

        public string? ResellerCode
        {
            get;
            set;
        }

        public string? ResellerType
        {
            get;
            set;
        }
    }

    public class ReservationAndPreBookCompare
    {
        public string? CheckIn
        {
            get; set;
        }

        public string? CheckOut
        {
            get; set;
        }

        public int AdultCount
        {
            get; set;
        }

        //public int ChildCount { get; set; }
        public string? ChildAges
        {
            get; set;
        }

        public string? PropertyName
        {
            get; set;
        }

        public string? RoomName
        {
            get; set;
        }

        public string? RoomInfo
        {
            get; set;
        }

        public string? RoomBoard
        {
            get; set;
        }

        public string? RoomType
        {
            get; set;
        }

        public string? RoomIndex
        {
            get; set;
        }

        public int NumberOfRooms
        {
            get; set;
        }

        public decimal Price
        {
            get; set;
        }

        public string? Currency
        {
            get; set;
        }

        public string? Supplier
        {
            get; set;
        }

        public string? HotelName
        {
            get; set;
        }

        public string? Destination
        {
            get; set;
        }

        public string? Status
        {
            get; set;
        }

        public string? CancellationPolicyStartDate
        {
            get; set;
        }

        public decimal CancellationCharge
        {
            get; set;
        }

        public string? CancellationCurrency
        {
            get; set;
        }

        public string? CancellationPolicyType
        {
            get; set;
        }

        public int? MappingId
        {
            get; set;
        }

        public GiataRoomMapping? MappingDetails
        {
            get; set;
        }

        public string? BoardMapping
        {
            get; set;
        }

        public string? LastActivity
        {
            get;
            set;
        }

        public string? CancelledOnDate
        {
            get;
            set;
        }

        /// <summary>
        /// It contains details of cancellation polices by Source (Manual means Reseller, Supplier means supplier cancellation policy)
        /// Cancellation Policy details at root node are the one that was used in comparing and optimization
        /// In this List where IsUsedInComparison == true , it means that th
        /// </summary>
        public List<CancellationPolicyBySource> CancellationPoliciesBySource
        {
            get; set;
        }
        public List<CancellationPolicyBySource> CancellationPoliciesInSupplierCurrency
        {
            get; set;
        }

    }

    public class ReservationReportCalculations
    {
        public List<ReservationReportCalculation> ReportSummary
        {
            get; set;
        }
    }

    public class ReservationReportCalculation
    {
        public int? RePricerId
        {
            get; set;
        }

        /// <summary>
        /// Sum of Reservation Price for LifetimeOptimisedBookingCount
        /// </summary>
        public decimal LifetimeOptimisedReservationPrice
        {
            get; set;
        }

        /// <summary>
        /// Sum of Prebook Price for LifetimeOptimisedBookingCount
        /// </summary>
        public decimal LifetimeOptimisedPrebookPrice
        {
            get; set;
        }

        /// <summary>
        /// Sum of All Possible Optimisation Profit that was valid at any point of time during recommandation process (based on LifetimeOptimisedBookingCount)
        /// </summary>
        public decimal LifetimePossibleOptimisationProfit
        {
            get; set;
        }

        /// <summary>
        /// Sum of amount where new reservation created based on recommandation
        /// </summary>
        public decimal RealizedGain
        {
            get; set;
        }

        public int RealizedGainCount
        {
            get; set;
        }

        /// <summary>
        /// Sum of amount where
        /// 1) no new reservation created based on recommandation and checkin date has passed
        /// 2) or exisitng reservation cancelled (Original Reservation Status <> OK )
        /// 3) or recommndation missed as the recommandation no longer exists
        /// </summary>
        public decimal MissedGain
        {
            get; set;
        }

        public int MissedGainCount
        {
            get; set;
        }

        /// <summary>
        /// Sum of amount where
        /// 1) Prebook recommandation on that action can be taken
        /// 2) CancellationEdgeCase recommandation on that action can be taken
        /// 3) PriceEdgeCase recommandation on that action can be taken
        /// </summary>
        public decimal OpenGain
        {
            get; set;
        }

        public int OpenGainCount
        {
            get; set;
        }

        /// <summary>
        /// Sum of all recommandation  that was valid at any point of time. It includes PriceEdgeCase where recoondation are only search based not on prebook basis.
        /// </summary>
        public int LifetimeOptimisedBookingCount
        {
            get; set;
        }

        /// <summary>
        /// Total number of Refundable Reservations, processed by Repricer based on client configuration
        /// </summary>
        public int TotalUniqueBookings
        {
            get; set;
        }

        /// <summary>
        /// Total number of Refundable Reservations, filtered by Repricer based on client configuration
        /// </summary>
        public int FilteredReservationCount
        {
            get; set;
        }

        /// <summary>
        /// Total number of Refundable Reservations, downloaded by Repricer for processing.
        /// </summary>
        public int TotalRefundableReservation
        {
            get; set;
        }

        public List<SummarizedView> SummarizedView
        {
            get; set;
        }
    }

    public class CommonReportRequest
    {
        public string? ReportType
        {
            get; set;
        }

        public int RepricerId
        {
            get; set;
        }

        public string? FromDate
        {
            get; set;
        }

        public string? ToDate
        {
            get; set;
        }

        public bool? IsCached
        {
            get; set;
        }
    }

    public class SummarizedView
    {
        /// <summary>
        /// Sum of Reservation Prices
        /// </summary>
        public decimal? ReservationPrice
        {
            get; set;
        }

        /// <summary>
        /// Sum of Prebook Prices
        /// </summary>
        public decimal? PrebookPrice
        {
            get; set;
        }

        /// <summary>
        /// Sum of Profits
        /// </summary>
        public decimal? Profit
        {
            get; set;
        }

        /// <summary>
        /// Sum of MaxProfits
        /// </summary>
        public decimal? MaxProfit
        {
            get; set;
        }

        /// <summary>
        /// Profit Percentage (Profit/ReservationPrice)*100
        /// </summary>
        public decimal? ProfitPercentage
        {
            get; set;
        }

        /// <summary>
        /// Profit Percentage Formula (Profit/ReservationPrice)*100
        /// </summary>
        public string ProfitPercentageFormula
        {
            get; set;
        }

        public string? Currency
        {
            get; set;
        }

        /// <summary>
        /// Sum of Realized Gain Report wise
        /// </summary>
        public decimal? RealizedGain
        {
            get; set;
        }

        /// <summary>
        /// Report wise Number of Action Taken Y
        /// </summary>
        public int? RealizedGainCount
        {
            get; set;
        }

        /// <summary>
        /// Count of Reservations
        /// </summary>
        public int ReservationsCount
        {
            get; set;
        }

        public string ReportType
        {
            get;
            set;
        }

        public SummarizedView()
        {
            ProfitPercentageFormula = "(Profit / ReservationPrice) * 100";
        }
    }

    public class ClientConfig
    {
        public bool MarginThresholdIsPercentageUsed
        {
            get; set;
        }

        public decimal? MarginThresholdPercentage
        {
            get; set;
        }

        /// <summary>
        /// MarginThreshold_Value same as PriceDifferenceValue
        /// </summary>
        public decimal? MarginThresholdValue
        {
            get; set;
        }

        public int TravelDaysMaxSearchInDays
        {
            get; set;
        }

        public int TravelDaysMinSearchInDays
        {
            get; set;
        }

        public int DaysDifferenceInPreBookCreation
        {
            get; set;
        }

        public bool IsUseDaysLimitCancellationPolicyEdgeCase
        {
            get; set;
        }

        public int DaysLimitCancellationPolicyEdgeCase
        {
            get; set;
        }

        public bool IsCreatePrebookForPriceEdgeCase
        {
            get; set;
        }

        public int MaxNumberOfTimesOptimization
        {
            get; set;
        }

        //##TODO Assignment pending
        public string? ClientConfig_ReportEmailToSend
        {
            get; set;
        }
    }

    public class ReservationReportResponse
    {
        public RepricerReportRequest? RequestBody
        {
            get; set;
        }

        public PagesSummary? PagesSummary
        {
            get; set;
        }

        public List<ActionInfo> Action
        {
            get; set;
        }

        public ReservationReportCalculation? OverallSummary
        {
            get; set;
        }

        public List<DashboardReportResponseRow>? Data
        {
            get; set;
        }
    }

    public class PagesSummary
    {
        public int? pageNumber
        {
            get; set;
        }

        public int? pageSize
        {
            get; set;
        }

        public int? totalPages
        {
            get; set;
        }

        public int? totalRows
        {
            get; set;
        }
    }

    public class DashboardSummaryRequest
    {
        [Required]
        public int RepricerId
        {
            get; set;
        }   // Default value is 1

        // Date when Updated For any Reservation
        public string? preBookFromDate { get; set; } = null;

        public string? preBookToDate { get; set; } = null;
        // First when PrebookCreated For any Reservation

        public string? FirstCreatedFromDate { get; set; } = null;
        public string? FirstCreatedToDate { get; set; } = null;

        public bool? isCached { get; set; } = true;
    }
}