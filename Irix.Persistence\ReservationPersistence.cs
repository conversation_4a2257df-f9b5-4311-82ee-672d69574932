using Irix.Entities;
using Irix.Persistence.Contract;
using Irix.Persistence.Data;
using Logger.Contract;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Caching.Memory;
using MongoDB.Bson;
using Repricer.Cache;
using RePricer.Util;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using Constant = RePricer.Constants.PersistanceConstant;
using constants = RePricer.Constants.ServiceConstants;

namespace Irix.Persistence
{
    public class ReservationPersistence : PersistanceBase, IReservationPersistence
    {
        private readonly ILogger _log;
        private IMasterPersistence _masterPersistence;
        private IClientPersistance _clientPersistance;
        private readonly string _connectionString = ConfigurationManagerHelper.GetValuefromConfig(Constant.IrixConnectionString);
        private readonly string _RePricerconnectionString = ConfigurationManagerHelper.GetValuefromConfig(Constant.RePricerConnectionString);
        private readonly IMemoryCache _memoryCache;
        private string _className = nameof(ReservationPersistence);
        private readonly object _lock_AddPreBookCriteriaToCache = new object();
        private readonly object _lock_GetPreBookCriteriaDBAll = new object();
        private readonly object _lock_InsertExchangeRateData = new object();
        private readonly object _lock_InsertReservationDataAsync = new object();
        private bool _isMock = false;

        public ReservationPersistence(ILogger log, IMasterPersistence masterPersistence, IClientPersistance clientPersistence, IMemoryCache memoryCache)
        {
            _log = log;
            _masterPersistence = masterPersistence;
            _clientPersistance = clientPersistence;
            _memoryCache = memoryCache;
            _className = nameof(ReservationPersistence);
        }

        private static SemaphoreSlim _semaphore = new SemaphoreSlim(Repricer.Util.Constant.GetCountAsPerProcessorPercent(25));

        public async Task InsertReservationDataAsync(string jsonData, int RePricerId, string RePricerStepToken = null)
        {
            const int maxRetryAttempts = 2;
            int retryCount = 0;
            var watch = Stopwatch.StartNew();
            var startTime = DateTime.UtcNow;
            var _methodName = nameof(InsertReservationDataAsync);
            try
            {
                //await _semaphore.WaitAsync();

                while (retryCount < maxRetryAttempts)
                {
                    #region logging of start

                    //try
                    //{
                    //    var irixErrorEntity4 = new IrixErrorEntity
                    //    {
                    //        ClassName = Constant.ReservationPersistance,
                    //        MethodName = Constant.InsertReservationDataAsync,
                    //        Params = SerializeDeSerializeHelper.Serialize(new
                    //        {
                    //            repricerId,
                    //            Attemps = $"START DBINSERT ({retryCount}\\{maxRetryAttempts}) ({RePricerStepToken})",
                    //            Minute = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2"),
                    //            Method = Constant.InsertReservationDataAsync,
                    //            Message = $"{_methodName}",
                    //            StartTime = startTime,
                    //            EndTime = DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss"),
                    //            EndTimeUTC = DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss"),
                    //        })
                    //    };
                    //    var msg = $"{SerializeDeSerializeHelper.Serialize(irixErrorEntity4)}";
                    //    _log.Info(msg, irixErrorEntity4, true);

                    //}
                    //catch (Exception ex1)
                    //{
                    //}

                    #endregion logging of start

                    #region Processing

                    try
                    {
                        using (SqlConnection connection = new SqlConnection(_connectionString))
                        {
                            await connection.OpenAsync();  // Make sure to use the async version for connection opening

                            using (SqlCommand command = new SqlCommand(Constant.InsertReservationData, connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.CommandTimeout = 120;  // Set a configurable timeout

                                // Add parameters
                                command.Parameters.AddWithValue(Constant.ReservationDataJson, jsonData);
                                command.Parameters.AddWithValue(Constant.RePricerId, RePricerId);

                                await command.ExecuteNonQueryAsync();  // Use async execution
                            }
                        }

                        // Break out of retry loop if successful
                        break;
                    }
                    catch (SqlException ex) when (ex.Number == -2) // SQL Timeout error code
                    {
                        retryCount++;

                        #region logging of intermediate attempts exceptions

                        var irixErrorEntity1 = new IrixErrorEntity
                        {
                            ClassName = Constant.ReservationPersistance,
                            MethodName = Constant.InsertReservationDataAsync,
                            Params = SerializeDeSerializeHelper.Serialize(new
                            {
                                RePricerId,
                                Attemps = $"ERROR DBINSERT ({retryCount}\\{maxRetryAttempts}) ({RePricerStepToken})",
                                Minute = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2"),
                                Method = _methodName,
                                ex.Message,
                                StartTime = startTime,
                                EndTime = DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss"),
                                EndTimeUTC = DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss"),
                                ReservationData = jsonData,
                                ex.StackTrace,
                            })
                        };

                        #endregion logging of intermediate attempts exceptions

                        if (retryCount >= maxRetryAttempts)
                        {
                            _log.Info("Max retry attempts reached for InsertReservationDataAsync.");
                            throw;
                        }

                        await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, retryCount)));
                    }
                    catch (Exception ex)
                    {
                        #region logging of final attempts exceptions

                        var irixErrorEntity2 = new IrixErrorEntity();
                        try
                        {
                            irixErrorEntity2 = new IrixErrorEntity
                            {
                                ClassName = Constant.ReservationPersistance,
                                MethodName = Constant.InsertReservationDataAsync,
                                Params = SerializeDeSerializeHelper.Serialize(new
                                {
                                    RePricerId,
                                    Attemps = $"ERROR ({retryCount}\\{maxRetryAttempts}) ({RePricerStepToken})",
                                    Minute = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2"),
                                    Method = _methodName,
                                    ex.Message,
                                    StartTime = startTime,
                                    EndTime = DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss"),
                                    EndTimeUTC = DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss"),
                                    ReservationData = jsonData,
                                    ex.StackTrace,
                                })
                            };
                            var msg = $"{SerializeDeSerializeHelper.Serialize(irixErrorEntity2)}";
                            _log.Info(msg, irixErrorEntity2, true);
                        }
                        catch (Exception ex1)
                        {
                        }
                        _log.Error(irixErrorEntity2, ex);

                        #endregion logging of final attempts exceptions
                    }

                    #endregion Processing
                }

                #region logging of ending

                var irixErrorEntity3 = new IrixErrorEntity();
                try
                {
                    irixErrorEntity3 = new IrixErrorEntity
                    {
                        ClassName = Constant.ReservationPersistance,
                        MethodName = Constant.InsertReservationDataAsync,
                        Params = SerializeDeSerializeHelper.Serialize(new
                        {
                            RePricerId,
                            Attemps = $"END DBINSERT({retryCount}\\{maxRetryAttempts}) ({RePricerStepToken})",
                            Minute = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2"),
                            Method = _methodName,
                            Message = $"END_{_methodName}",
                            StartTime = startTime,
                            EndTime = DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss"),
                            EndTimeUTC = DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss"),
                        })
                    };
                    var msg = $"{SerializeDeSerializeHelper.Serialize(irixErrorEntity3)}";
                    _log.Info(msg, irixErrorEntity3, true);
                }
                catch (Exception ex1)
                {
                }

                watch.Stop();
                var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
                LoggerPersistance.InsertReservationLogging(
                    Constant.Reservation,
                    "InsertReservation",
                    4,
                    RePricerStepToken,
                    elapsedTimeInSeconds,
                    RePricerId
                );

                #endregion logging of ending
            }
            finally
            {
                // Release the semaphore, allowing the next queued task to proceed
                //_semaphore.Release();
            }
        }

        public void InsertTotalBookingCount(int totalbookingCount, int RePricerId, string steptoken = null)
        {
            try
            {
                var watch = Stopwatch.StartNew();

                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.InsertTotalBookingCount, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                // Add parameters
                command.Parameters.AddWithValue(Constant.TotalBookingCount, totalbookingCount);
                command.Parameters.AddWithValue(Constant.RePricerId, RePricerId);

                command.ExecuteNonQuery();
                connection.Close();

                watch.Stop();
                var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;

                LoggerPersistance.InsertReservationLogging(Constant.Reservation, "InsertTotalBookingCount", 4, steptoken, elapsedTimeInSeconds, RePricerId);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = Constant.InsertReservationDataAsync
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public async Task<List<ReservationMainModel>> GetReservationsAsync(int repricerId, int reservationId = 0, bool isCacheRefresh = false, bool isfilter = true)
        {
            var watch = Stopwatch.StartNew();
            var repricerDetail = (_clientPersistance.LoadRePricerDetail(repricerId)).GetAwaiter().GetResult();

            #region Mock Repricer with restrictions

            //var repricerJSON = @"
            //{
            //    ""isJobsEnable"": true,
            //    ""repricerUserID"": 1,
            //    ""repricerUserName"": ""Accent"",
            //    ""adminUrl"": ""https://tbs.accenttravel.ro/admin"",
            //    ""adminUserId"": ""df23f903ff504c4e8ecdd2172be63c72"",
            //    ""adminPassword"": ""92e4ba0b28954bf39411939264cc4cd3"",
            //    ""resellerUrl"": ""https://tbs.accenttravel.ro/reseller"",
            //    ""resellerUserId"": ""7ac40b9abcb943b6b67005229587531c"",
            //    ""resellerPassword"": ""2f8d4824468c463fac0617be61eb1d97"",
            //    ""adminApiScope"": ""read:reservations"",
            //    ""resellerApiScope"": ""read:hotels-search "",
            //    ""isActive"": true,
            //    ""clientConfiguration"": {
            //        ""service"": ""hotel"",
            //        ""cancelPenalty"": false
            //    },
            //    ""extraClientDetail"": {
            //        ""travelDaysMaxSearchInDays"": 150,
            //        ""travelDaysMinSearchInDays"": 1,
            //        ""maxNumberOfTimesOptimization"": 0,
            //        ""clientConfig_DaysDifferenceInPreBookCreation"": 1,
            //        ""priceDifferenceValue"": 8.00,
            //        ""priceDifferencePercentage"": 0.00,
            //        ""isUsePercentage"": false,
            //        ""reportEmailToSend"": ""<EMAIL>"",
            //        ""daysLimitCancellationPolicyEdgeCase"": 1,
            //        ""isUseDaysLimitCancellationPolicyEdgeCase"": true,
            //        ""isCreatePrebookForPriceEdgeCase"": true,
            //        ""currency"": ""EUR""
            //    },
            //    ""clientConfigScheduler"": {
            //        ""reservation_CronTime"": ""40 1 29 9 *"",
            //        ""preBook_CronTime"": ""30 7,19 * * *"",
            //        ""currencyExchange_CronTime"": ""0 12 * * *"",
            //        ""timeZoneId"": 57,
            //        ""timeZoneName"": ""Asia/Famagusta""
            //    },
            //    ""optimizationType"": 3,
            //    ""modifiedBy"": null,
            //    ""emailTo"": ""<EMAIL>,<EMAIL>"",
            //    ""emailCC"": """",
            //    ""emailBcc"": ""<EMAIL>,<EMAIL>"",
            //    ""restrictedHotel"": [
            //        {
            //            ""repricerId"": 1,
            //            ""hotelId"": 6919702,
            //            ""hotelName"": ""1005 MELBOURNE""
            //        }
            //    ],
            //    ""restrictedCity"": [
            //        {
            //            ""repricerId"": 1,
            //            ""cityId"": 81538,
            //            ""cityName"": ""VANCOUVER, BC""
            //        }
            //    ],
            //    ""restrictedCountry"": [
            //        {
            //            ""repricerId"": 1,
            //            ""countryId"": 27,
            //            ""countryName"": ""Canada""
            //        }
            //    ],
            //    ""restrictedReseller"": [
            //        {
            //            ""repricerId"": 1,
            //            ""resellerId"": 1133,
            //            ""resellerName"": ""VOLERO TOURISM"",
            //            ""resellerCode"": ""VOLERO"",
            //            ""isActive"": true,
            //            ""updatedBy"": ""System"",
            //            ""updateDate"": ""2024-10-07T16:33:25.567""
            //        }
            //    ],
            //    ""restrictedSupplier"": [
            //        {
            //            ""repricerId"": 1,
            //            ""supplierName"": ""w2m"",
            //            ""isActive"": true,
            //            ""updatedBy"": """",
            //            ""updateDate"": ""0001-01-01T00:00:00""
            //        }
            //    ],
            //    ""isOptimizationAllowed"": true
            //}";
            //repricerDetail = SerializeDeSerializeHelper.DeSerialize<RePricerDetail>(repricerJSON);

            #endregion Mock Repricer with restrictions

            List<ReservationMainModel> reservations = new List<ReservationMainModel>();
            try
            {
                var cacheKey = $"Repricer_{repricerId}_usp_get_CreateSearch";// _{isMultiSupplier}";

                if (isCacheRefresh == false)
                {
                    if (_memoryCache.TryGetValue(cacheKey, out List<ReservationMainModel> cachedResults))
                    {
                        if (cachedResults != null && cachedResults?.Count > 0)
                        {
                            if (reservationId > 0)
                            {
                                reservations = cachedResults?.Where(x => x.ReservationId == reservationId)?.ToList() ?? new List<ReservationMainModel>();
                            }
                            if (reservations?.Any() == true)
                            {
                                var reservationsCopy = SerializeDeSerializeHelper.DeSerializeWithNullValueHandling<List<ReservationMainModel>>(SerializeDeSerializeHelper.Serialize(reservations));
                                reservationsCopy = FilterSearchListAsPerRestrictions(reservationsCopy, repricerDetail);
                                return reservationsCopy;
                            }
                        }
                    }
                    if (RedisCacheHelper.KeyExists(cacheKey))
                    {
                        cachedResults = RedisCacheHelper.Get<List<ReservationMainModel>>(cacheKey);
                        if (cachedResults != null && cachedResults?.Count > 0)
                        {
                            if (reservationId > 0)
                            {
                                reservations = cachedResults?.Where(x => x.ReservationId == reservationId)?.ToList() ?? new List<ReservationMainModel>();
                            }
                            if (reservations?.Any() == true)
                            {
                                if (reservationId == 0 && reservations.Count > 0)
                                {
                                    _memoryCache.Set(cacheKey, reservations, TimeSpan.FromHours(20));
                                }
                                var reservationsCopy = SerializeDeSerializeHelper.DeSerializeWithNullValueHandling<List<ReservationMainModel>>(SerializeDeSerializeHelper.Serialize(reservations));
                                reservationsCopy = FilterSearchListAsPerRestrictions(reservationsCopy, repricerDetail);
                                return reservationsCopy;
                            }
                        }
                    }
                }

                var procName = Constant.GetReservation;
                var procName1 = Constant.GetCreateSearchV0;
                if (_isMock)
                {
                    procName = procName1;
                }

                reservations = await GetReservationsAsyncFromDB(procName, repricerId, reservationId);

                if (reservations != null && reservations?.Count > 1 && reservationId == 0)
                {
                    _memoryCache.Set(cacheKey, reservations, TimeSpan.FromHours(20));
                    RedisCacheHelper.Set(cacheKey, reservations, TimeSpan.FromHours(20));
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = Constant.GetReservationsAsync,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;

            //_log.Info($"ReservationinPersistance|GetReservationsAsync|{elapsedTimeInSeconds}|{repricerId} in {watch.Elapsed}");
            if (isfilter == true)
            {
                reservations = FilterSearchListAsPerRestrictions(reservations, repricerDetail);
            }
            return reservations;
        }

        private List<ReservationMainModel> FilterSearchListAsPerRestrictions(List<ReservationMainModel> reservations, RePricerDetail repricerDetail)
        {
            if (reservations != null && reservations.Any())
            {
                var restrictedHotelIds = repricerDetail.RestrictedHotel != null
                    ? repricerDetail.RestrictedHotel.Select(h => h.HotelId).ToList()
                    : new List<int>();

                var restrictedCityIds = repricerDetail.RestrictedCity != null
                   ? repricerDetail.RestrictedCity.Select(h => h.CityId).ToList()
                   : new List<int>();

                var restrictedCountryIds = repricerDetail.RestrictedCountry != null
                   ? repricerDetail.RestrictedCountry.Select(h => h.CountryId).ToList()
                   : new List<int>();

                reservations = reservations
                    .Where(reservation =>
                        !restrictedHotelIds.Contains(reservation.hotelid) &&
                        !restrictedCityIds.Contains(reservation.cityid) &&
                        !restrictedCountryIds.Contains(reservation.countryid)
                    ).ToList();

                reservations = reservations?.Where(x => repricerDetail?.RestrictedCountry?.Any(y => y.CountryId == x.countryid) != true)?.ToList();

                reservations = reservations?.Where(x => repricerDetail?.RestrictedCity?.Any(y => y.CityId == x.cityid) != true)?.ToList();

                reservations = reservations?.Where(x => repricerDetail?.RestrictedHotel?.Any(y => y.HotelId == x.hotelid) != true)?.ToList();

                reservations = reservations?.Where(x => repricerDetail?.RestrictedReseller?.Any(y => y.ResellerId == x.ResellerId) != true)?.ToList();
            }
            return reservations;
        }

        public async Task<List<ReservationRoomModel>> GetReservationsRoomAsync(int RePricerId, int ReservationId = 0)
        {
            var watch = Stopwatch.StartNew();

            List<ReservationRoomModel> reservations = new List<ReservationRoomModel>();
            try
            {
                using SqlConnection connection = Connection();
                await connection.OpenAsync();
                SqlParameter[] parameters =
                {
                 new SqlParameter(Constant.RePricerId, SqlDbType.Int) { Value = RePricerId },
                 new SqlParameter(Constant.ReservationId, SqlDbType.Int) { Value = ReservationId }
                };

                using SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, Constant.GetRoomReservation, parameters);

                while (reader.Read())
                {
                    ReservationRoomModel reservation = new ReservationRoomModel
                    {
                        ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                        RoomId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "roomid"),
                        RoomName = DbPropertyHelper.StringPropertyFromRow(reader, "roomname"),
                        RoomType = DbPropertyHelper.StringPropertyFromRow(reader, "roomtype"),
                        RoomBoard = DbPropertyHelper.StringPropertyFromRow(reader, "roomboard"),
                        RoomInfo = DbPropertyHelper.StringPropertyFromRow(reader, "roominfo"),
                        PassengerCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "PassengerCount"),
                        ChildAges = DbPropertyHelper.StringPropertyFromRow(reader, "ChildAges")
                    };

                    reservations.Add(reservation);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = Constant.GetReservationsAsync,
                };
                _log.Error(irixErrorEntity, ex);
            }
            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;

            //_log.Info($"ReservationinPersistance|GetReservationsRoomAsync|{elapsedTimeInSeconds}|{repricerId} in {watch.Elapsed}");

            return reservations;
        }

        public async Task<PreBookCriteriaResult> GetPreBookCriteria(int reservationId, int repricerId, bool isMultiSupplier = false)
        {
            var watch = Stopwatch.StartNew();
            var cacheKey = $"GetPreBookCriteria_{repricerId}_{reservationId}";
            int maxRetries = 3;  // Maximum number of retries
            int delayMilliseconds = 2000; // Start with a 2-second delay between retries
            int retryCount = 0;

            PreBookCriteriaResult preBookCriteriaResult = null;

            while (retryCount < maxRetries)
            {
                try
                {
                    preBookCriteriaResult = await GetPreBookCriteriaDB(reservationId, repricerId, isMultiSupplier);

                    if (preBookCriteriaResult != null)
                    {
                        return preBookCriteriaResult;
                    }
                }
                catch (Exception ex)
                {
                    retryCount++;

                    if (retryCount >= maxRetries)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = Constant.ReservationPersistance,
                            MethodName = Constant.GetPreBookCriteria,
                        };
                        _log.Error(irixErrorEntity, ex);
                        break;
                    }

                    await Task.Delay(delayMilliseconds);

                    delayMilliseconds *= 2;
                }
            }

            // Return a default value in case of failure after retries
            return new PreBookCriteriaResult
            {
                PreBookCriteriaList = new List<PrebookCriteria>()
            };
        }

        private async Task<List<ReservationMainModel>> GetReservationsAsyncFromDB(string procName, int RePricerId, int ReservationId = 0)
        {
            List<ReservationMainModel> reservations = new List<ReservationMainModel>();
            var strtTime = DateTime.Now;

            try
            {
                // Initialize the SQL connection
                using SqlConnection connection = Connection();
                await connection.OpenAsync(); // Open connection asynchronously

                // Initialize parameters for the stored procedure
                SqlParameter[] parameters =
                {
                    new SqlParameter(Constant.RePricerId, SqlDbType.Int) { Value = RePricerId },
                    new SqlParameter(Constant.ReservationId, SqlDbType.Int) { Value = ReservationId }
                };

                // Initialize the SQL command with stored procedure and set the command timeout
                using SqlCommand command = new SqlCommand(procName, connection)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = _commandTimeout // Set command timeout to 120 seconds
                };

                // Add parameters to the command
                command.Parameters.AddRange(parameters);

                // Execute the stored procedure and get the reader asynchronously
                using SqlDataReader reader = await command.ExecuteReaderAsync();

                // Read each record asynchronously
                while (await reader.ReadAsync())
                {
                    // Map the SQL data to the ReservationMainModel
                    try
                    {
                        ReservationMainModel reservation = new ReservationMainModel
                        {
                            ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                            ACComdationId = DbPropertyHelper.Int32NullablePropertyFromRow(reader, "ACComdationId"),
                            CheckIn = DbPropertyHelper.DateTimePropertyFromRow(reader, "checkIn"),
                            Checkout = DbPropertyHelper.DateTimePropertyFromRow(reader, "Checkout"),
                            LeaderNationality = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "leaderNationality"),
                            AdultCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "adultCount"),
                            ChildrenAges = DbPropertyHelper.StringPropertyFromRow(reader, "childrenAges"),
                            Language = DbPropertyHelper.StringPropertyFromRow(reader, "language"),
                            SellingChannel = DbPropertyHelper.StringPropertyFromRow(reader, "sellingChannel"),
                            CriteriaJson = DbPropertyHelper.StringPropertyFromRow(reader, "CriteriaJson"),
                            Count_notification = DbPropertyHelper.Int32NullablePropertyFromRow(reader, "Count_notification"),
                            supplierName = DbPropertyHelper.StringPropertyFromRow(reader, "supplierName"),
                            BookingDate = DbPropertyHelper.DateTimePropertyFromRow(reader, "BookingDate"),
                            CancellationDate = DbPropertyHelper.DateTimePropertyFromRow(reader, "cancellation_date"),
                            hotelid = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "hotelid"),
                            cityid = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "cityid"),
                            countryid = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "countryid"),
                            ResellerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ResellerId"),
                            hotelname = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "hotelname")
                        };

                        try
                        {
                            var reservationCpJSON = System.Convert.ToString(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCpJSON")).ToString();
                            reservation.CancellationPoliciesBySource = SerializeDeSerializeHelper.DeSerialize<List<CancellationPolicyBySource>>(reservationCpJSON);
                            reservation.CancellationPoliciesInSupplierCurrency = SerializeDeSerializeHelper.DeSerialize<List<CancellationPolicyBySource>>(reservationCpJSON);
                        }
                        catch (Exception ex)
                        {
                        }
                        // Add reservation to the list
                        reservations.Add(reservation);
                    }
                    catch (Exception ex)
                    {
                        // Log the error and rethrow it
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = Constant.ReservationPersistance,
                            MethodName = Constant.GetReservationsAsync,
                        };
                        _log.Error(irixErrorEntity, ex);
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error and rethrow it
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = Constant.GetReservationsAsync,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            var endTime = DateTime.Now;
            var spendTime = (endTime - strtTime).TotalSeconds;
            // Return the list of reservations
            return reservations;
        }

        private async Task<PreBookCriteriaResult> GetPreBookCriteriaDB(int reservationId, int repricerId, bool isMultiSupplier = false)
        {
            PreBookCriteriaResult result = null;
            List<PreBookCriteriaResult> list = null;
            try
            {
                list = await GetPreBookCriteriaDBAll(repricerId, isMultiSupplier, reservationId);

                result = list?.FirstOrDefault(x => x.RepricerId == repricerId) ?? new PreBookCriteriaResult
                {
                    PreBookCriteriaList = new List<PrebookCriteria>()
                };

                if (reservationId > 0)
                {
                    result = list?.FirstOrDefault(x => x.RepricerId == repricerId && x.ReservationId == reservationId) ?? new PreBookCriteriaResult
                    {
                        PreBookCriteriaList = new List<PrebookCriteria>()
                    };
                }

                if (result == null || result.PreBookCriteriaList == null || result.PreBookCriteriaList.Count == 0)
                {
                    result = new PreBookCriteriaResult
                    {
                        PreBookCriteriaList = new List<PrebookCriteria>()
                    };

                    using (var connection = new SqlConnection(_connectionString))
                    {
                        // Attempt to open the connection with retries for transient errors
                        await OpenConnectionWithRetryAsync(connection).ConfigureAwait(false);

                        SqlParameter[] parameters =
                        {
                            new SqlParameter(Constant.ReservationId, SqlDbType.Int) { Value = (object)reservationId ?? DBNull.Value },
                            new SqlParameter(Constant.RepricerId, SqlDbType.Int) { Value = (object)repricerId ?? DBNull.Value },
                            new SqlParameter(Constant.isMultiSupplier, SqlDbType.Int) { Value = isMultiSupplier }
                        };

                        var procName = Constant.GetPreBookCriteria;
                        if (_isMock == true)
                        {
                            procName = Constant.GetPreBookCriteriaV0;
                            SqlParameter[] parameters1 =
                            {
                                new SqlParameter(Constant.ReservationId, SqlDbType.Int) { Value =  reservationId > 0 ? reservationId : DBNull.Value },
                                new SqlParameter(Constant.RepricerId, SqlDbType.Int) { Value = repricerId },
                                new SqlParameter(Constant.isMultiSupplier, SqlDbType.Int) { Value = isMultiSupplier }
                            };
                            parameters = parameters1;
                        }

                        // Execute the stored procedure and read the result set
                        using (var reader = await ExecuteStoredProcedureAsync(connection, procName, parameters).ConfigureAwait(false))
                        {
                            // Process the first result set (room information)
                            while (await reader.ReadAsync().ConfigureAwait(false))
                            {
                                PrebookCriteria prebooking = new PrebookCriteria
                                {
                                    HotelId = reader.GetInt32(reader.GetOrdinal("hotelid")),
                                    HotelName = reader.GetString(reader.GetOrdinal("hotelname")),
                                    RoomName = reader.GetString(reader.GetOrdinal("roomname")),
                                    RoomId = reader.GetInt32(reader.GetOrdinal("roomid")),
                                    RoomType = DbPropertyHelper.StringPropertyFromRow(reader, "roomtype"),
                                    RoomInfo = DbPropertyHelper.StringPropertyFromRow(reader, "roominfo"),
                                    RoomBoard = DbPropertyHelper.StringPropertyFromRow(reader, "roomboard"),
                                    passengerCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "passengerCount"),
                                    ChildAges = DbPropertyHelper.StringPropertyFromRow(reader, "ChildAges"),
                                    Destinations = DbPropertyHelper.StringPropertyFromRow(reader, "Destinations"),
                                    Currency = DbPropertyHelper.StringPropertyFromRow(reader, "Currency"),
                                    IssueNet = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "issue_net")
                                };

                                result.PreBookCriteriaList.Add(prebooking);
                            }

                            // Move to the next results set
                            await reader.NextResultAsync().ConfigureAwait(false);

                            // Process the second result set (reservation Price)
                            while (await reader.ReadAsync().ConfigureAwait(false))
                            {
                                result.Currency = reader.GetString(reader.GetOrdinal("currency"));
                                result.TotalSelling = reader.GetDecimal(reader.GetOrdinal("total_selling"));
                                result.IssueNet = reader.GetDecimal(reader.GetOrdinal("issue_net"));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }
            if (result != null && result.PreBookCriteriaList != null && result.PreBookCriteriaList.Count > 0)
            {
                await AddPreBookCriteriaToCache(result, list, repricerId, isMultiSupplier);
            }
            return result;
        }

        private async Task<bool> AddPreBookCriteriaToCache(PreBookCriteriaResult preBookCriteriaResult, List<PreBookCriteriaResult> list, int repricerId, bool isMultiSupplier = false)
        {
            try
            {
                lock (_lock_AddPreBookCriteriaToCache)
                {
                    if (list == null && list.Count == 0)
                    {
                        list = GetPreBookCriteriaDBAll(repricerId, isMultiSupplier)?.GetAwaiter().GetResult();
                    }
                    var found = list?.AsEnumerable()?.FirstOrDefault(x => x.RepricerId == preBookCriteriaResult.RepricerId && x.ReservationId == preBookCriteriaResult.ReservationId) ?? null;

                    if (found == null && list != null && list.Count > 0)
                    {
                        list.Add(preBookCriteriaResult);
                        var cacheKey = $"GetPreBookCriteriaDBAll_{repricerId:000}_";// {isMultiSupplier}";

                        if (_isMock == false)
                        {
                            _memoryCache.Set(cacheKey, list, TimeSpan.FromHours(3));
                        }

                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(AddPreBookCriteriaToCache),
                    RePricerId = repricerId,
                    ReservationId = preBookCriteriaResult.ReservationId
                };
                _log.Error(irixErrorEntity, ex);
            }
            return false;
        }

        public async Task<List<PreBookCriteriaResult>> GetPreBookCriteriaDBAll(int repricerId, bool isMultiSupplier = false, int reservationId = 0)
        {
            // Define a unique cache key based on the input parameters
            var cacheKey = $"GetPreBookCriteriaDBAll_{repricerId}_";// _{isMultiSupplier}";

            // Check if the result is already in the cache
            if (_memoryCache.TryGetValue(cacheKey, out List<PreBookCriteriaResult> cachedResults))
            {
                try
                {
                    if (cachedResults != null && cachedResults.Count > 0 && _isMock == false && !cachedResults.Any(x => x.PreBookCriteriaList.Any(x => string.IsNullOrEmpty(x.Currency))))
                    {
                        return cachedResults;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.ToString());
                }
            }

            try
            {
                var results = new List<PreBookCriteriaResult>();
                var details = new List<PrebookCriteria>();
                var summaries = new List<PreBookCriteriaResult>();
                lock (_lock_GetPreBookCriteriaDBAll)
                {
                    // Open connection with retry logic
                    using (var connection = new SqlConnection(_connectionString))
                    {
                        // Attempt to open the connection with retries for transient errors
                        OpenConnectionWithRetryAsync(connection).GetAwaiter().GetResult();

                        SqlParameter[] parameters =
                        {
                            new SqlParameter(Constant.ReservationId, SqlDbType.Int) { Value = DBNull.Value },
                            new SqlParameter(Constant.RepricerId, SqlDbType.Int) { Value = repricerId },
                            new SqlParameter(Constant.isMultiSupplier, SqlDbType.Int) { Value = isMultiSupplier }
                        };

                        var procName = Constant.GetPreBookCriteria;

                        if (_isMock == true)
                        {
                            procName = Constant.GetPreBookCriteriaV0;
                            SqlParameter[] parameters1 =
                            {
                                new SqlParameter(Constant.ReservationId, SqlDbType.Int) { Value =  reservationId > 0 ? reservationId : DBNull.Value },
                                new SqlParameter(Constant.RepricerId, SqlDbType.Int) { Value = repricerId },
                                new SqlParameter(Constant.isMultiSupplier, SqlDbType.Int) { Value = isMultiSupplier }
                            };
                            parameters = parameters1;
                        }

                        using (var reader = ExecuteStoredProcedureAsync(connection, procName, parameters).GetAwaiter().GetResult())
                        {
                            // Process the first result set (room information)
                            while (reader.Read())
                            {
                                try
                                {
                                    var detail = new PrebookCriteria
                                    {
                                        HotelId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "hotelid"),
                                        RepricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerId"),
                                        ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                                        HotelName = DbPropertyHelper.StringPropertyFromRow(reader, "hotelname"),
                                        RoomName = DbPropertyHelper.StringPropertyFromRow(reader, "roomname"),
                                        RoomId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RoomId"),
                                        RoomType = DbPropertyHelper.StringPropertyFromRow(reader, "roomtype"),
                                        RoomInfo = DbPropertyHelper.StringPropertyFromRow(reader, "roominfo"),
                                        RoomBoard = DbPropertyHelper.StringPropertyFromRow(reader, "roomboard"),
                                        passengerCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "passengerCount"),
                                        ChildAges = DbPropertyHelper.StringPropertyFromRow(reader, "ChildAges"),
                                        Destinations = DbPropertyHelper.StringPropertyFromRow(reader, "Destinations"),
                                        Currency = DbPropertyHelper.StringPropertyFromRow(reader, "Currency"),
                                        IssueNet = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "issue_net")
                                    };

                                    if (detail != null)
                                    {
                                        details.Add(detail);
                                    }
                                }
                                catch (Exception ex)
                                {
                                }
                            }

                            // Move to the next result set
                            reader.NextResult();

                            // Process the second result set (reservation Price)
                            while (reader.Read())
                            {
                                try
                                {
                                    var summary = new PreBookCriteriaResult
                                    {
                                        RepricerId = reader.GetInt32(reader.GetOrdinal("RepricerId")),
                                        ReservationId = reader.GetInt32(reader.GetOrdinal("ReservationId")),
                                        Currency = reader.GetString(reader.GetOrdinal("currency")),
                                        TotalSelling = reader.GetDecimal(reader.GetOrdinal("total_selling")),
                                        IssueNet = reader.GetDecimal(reader.GetOrdinal("issue_net")),
                                    };

                                    if (summary != null)
                                    {
                                        summaries.Add(summary);
                                    }
                                }
                                catch (Exception ex)
                                {
                                }
                            }
                        }
                    }

                    // Join summaries and details
                    var query = from s in summaries
                                join d in details on new
                                {
                                    s.ReservationId,
                                    s.RepricerId
                                } equals new
                                {
                                    d.ReservationId,
                                    d.RepricerId
                                }
                                group d by new
                                {
                                    s.ReservationId,
                                    s.RepricerId,
                                    s.Currency,
                                    s.IssueNet,
                                    s.TotalSelling
                                } into groupedDetails
                                select new PreBookCriteriaResult
                                {
                                    ReservationId = groupedDetails.Key.ReservationId,
                                    RepricerId = groupedDetails.Key.RepricerId,
                                    Currency = groupedDetails.Key.Currency,
                                    IssueNet = groupedDetails.Key.IssueNet,
                                    TotalSelling = groupedDetails.Key.TotalSelling,
                                    PreBookCriteriaList = groupedDetails.ToList() // Collecting all matching details
                                };

                    results = query.Where(x => x.PreBookCriteriaList.Count > 0).ToList();

                    // Cache the result for 30 minutes
                    if (results != null && results.Count > 0 && _isMock == false)
                    {
                        _memoryCache.Set(cacheKey, results, TimeSpan.FromHours(3));
                    }
                }

                return results;
            }
            catch (SqlException sqlEx)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetPreBookCriteriaDBAll),
                    RePricerId = repricerId
                };
                _log.Error(irixErrorEntity, sqlEx);

                throw new Exception("An error occurred while accessing the database.", sqlEx);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetPreBookCriteriaDBAll),
                    RePricerId = repricerId
                };
                _log.Error(irixErrorEntity, ex);
                throw new Exception("An unexpected error occurred.", ex);
            }
        }

        private async Task OpenConnectionWithRetryAsync(SqlConnection connection)
        {
            int maxRetries = 3;
            int delayMilliseconds = 2000;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    // Attempt to open the connection
                    await connection.OpenAsync().ConfigureAwait(false);
                    return;
                }
                catch (SqlException ex)
                {
                    if (attempt == maxRetries)
                    {
                        throw;  // Re-throw the exception after max retries
                    }

                    // Log and retry
                    var msg = $"Attempt {attempt} to open database connection failed. Retrying...";
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(OpenConnectionWithRetryAsync),
                        Params = msg
                    };
                    _log.Error(irixErrorEntity, ex);
                    await Task.Delay(delayMilliseconds).ConfigureAwait(false);
                }
            }
        }

        /// <summary>
        /// When Prebook is created example prebook case and CP edge case
        /// </summary>
        /// <param name="availabilityToken"></param>
        /// <param name="reservationId"></param>
        /// <param name="RePricerId"></param>
        /// <param name="isEmailSent"></param>
        /// <param name="CurrentPrice"></param>
        /// <param name="preBookCount"></param>
        /// <param name="emailbody"></param>
        /// <param name="PreBookResponseJson"></param>
        /// <param name="reservationemailbody"></param>
        /// <param name="prebookemailbody"></param>
        /// <param name="Profit"></param>
        /// <param name="ProfitAfterCancellation"></param>
        /// <param name="cancellationPolicyResult"></param>
        /// <param name="token"></param>
        /// <param name="roomResult"></param>
        /// <param name="searchSyncJson"></param>
        /// <param name="currencyfactor"></param>
        /// <param name="ClientConfig_DaysDifferenceInPreBookCreation"></param>
        /// <param name="steptoken"></param>
        public void InsertPreBookReservation(string availabilityToken, int reservationId, int RePricerId, int isEmailSent, decimal CurrentPrice, int preBookCount, string emailbody, string PreBookResponseJson, string reservationemailbody, string prebookemailbody, decimal Profit, decimal ProfitAfterCancellation, CancellationPolicyResult cancellationPolicyResult, string token, RoomResult roomResult, string searchSyncJson, decimal currencyfactor, int ClientConfig_DaysDifferenceInPreBookCreation, string steptoken = null, bool isoptimized = false)
        {
            try
            {
                if (isoptimized)
                {
                    PreBookResponseJson = PreBookResponseJson.Replace("'", " ");
                    searchSyncJson = searchSyncJson.Replace("'", " ");
                }
                else
                {
                    PreBookResponseJson = string.Empty;
                    searchSyncJson = string.Empty;
                }
                var input = new
                {
                    availabilityToken,
                    reservationId,
                    RePricerId,
                    isEmailSent,
                    CurrentPrice,
                    emailbody,
                    PreBookResponseJson = string.Empty,
                    reservationemailbody,
                    prebookemailbody,
                    Profit,
                    ProfitAfterCancellation,
                    cancellationPolicyResult,
                    token,
                    roomResult,
                    searchSyncJson = string.Empty,
                    currencyfactor,
                    ClientConfig_DaysDifferenceInPreBookCreation,
                    steptoken,
                    isoptimized
                };
                var watchPreBookReservation = Stopwatch.StartNew();

                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.InsertPreBookReservation, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                // Add parameters
                command.Parameters.AddWithValue(Constant.AvailabilityToken, availabilityToken);
                command.Parameters.AddWithValue(Constant.ReservationId, reservationId);
                command.Parameters.AddWithValue(Constant.RePricerId, RePricerId);
                command.Parameters.AddWithValue(Constant.IsMailSent, isEmailSent);
                command.Parameters.AddWithValue(Constant.PreBookPrice, CurrentPrice);
                command.Parameters.AddWithValue(Constant.preBookCount, preBookCount);
                command.Parameters.AddWithValue(Constant.emailbody, emailbody);
                command.Parameters.AddWithValue(Constant.PreBookResponseJson, PreBookResponseJson);
                command.Parameters.AddWithValue(Constant.reservationemailbody, reservationemailbody);
                command.Parameters.AddWithValue(Constant.prebookemailbody, prebookemailbody);
                command.Parameters.AddWithValue(Constant.Profit, Profit);
                command.Parameters.AddWithValue(Constant.ProfitAfterCancellation, ProfitAfterCancellation);
                command.Parameters.AddWithValue(Constant.IsCancellationPolicyMatched, cancellationPolicyResult.IsCancellationPolicyMatched);
                command.Parameters.AddWithValue(Constant.cPStatus, cancellationPolicyResult.cPStatus);
                command.Parameters.AddWithValue(Constant.cpdaysgain, cancellationPolicyResult.daysgain);
                command.Parameters.AddWithValue(Constant.matchedcancellationpolicygain, cancellationPolicyResult.matchedcancellationpolicygain);
                command.Parameters.AddWithValue(Constant.ClientConfig_DaysDifferenceInPreBookCreation, ClientConfig_DaysDifferenceInPreBookCreation);
                command.Parameters.AddWithValue(Constant.ReservationAdultCount, roomResult.ReservationPassengerCount);
                command.Parameters.AddWithValue(Constant.PreBookAdultCount, roomResult.PreBookPassengerCount);
                command.Parameters.AddWithValue(Constant.ReservationChildAges, roomResult.ReservationchildAges);
                command.Parameters.AddWithValue(Constant.PreBookChildAges, roomResult.PrebookChildAges);
                command.Parameters.AddWithValue(Constant.ReservationRoomName, roomResult.ReservationRoomName);
                command.Parameters.AddWithValue(Constant.PreBookRoomName, roomResult.PreBookRoomName);
                command.Parameters.AddWithValue(Constant.ReservationRoomBoard, roomResult.ReservationRoomBoard);
                command.Parameters.AddWithValue(Constant.PreBookRoomBoard, roomResult.PreBookRoomBoard);
                command.Parameters.AddWithValue(Constant.ReservationRoomInfo, roomResult.ReservationRoomInfo);
                command.Parameters.AddWithValue(Constant.PrebookRoomInfo, roomResult.PreBookRoomInfo);
                command.Parameters.AddWithValue(Constant.PreBookRoomIndex, roomResult.PreBookRoomIndex);

                command.Parameters.AddWithValue(Constant.BookingDate, roomResult.BookingDate);
                command.Parameters.AddWithValue(Constant.ReservationPrice, roomResult.ReservationPrice);
                command.Parameters.AddWithValue(Constant.Providers, roomResult.ReservationProviders);
                command.Parameters.AddWithValue(Constant.PrebookProviders, roomResult.PrebookProviders);
                command.Parameters.AddWithValue(Constant.SearchJson, searchSyncJson);

                command.Parameters.AddWithValue(Constant.MatchedPreBookCancellationChargeByPolicy, cancellationPolicyResult.PreBookCancellationChargeByPolicies);
                command.Parameters.AddWithValue(Constant.MatchedReservationCancellationChargeByPolicy, cancellationPolicyResult.ReservationCancellationChargeByPolicies);
                command.Parameters.AddWithValue(Constant.MatchedPreBookCancellationDate, cancellationPolicyResult.PreBookCancellationDate);
                command.Parameters.AddWithValue(Constant.MatchedReservationCancellationDate, cancellationPolicyResult.ReservationCancellationDate);
                command.Parameters.AddWithValue(Constant.CurrencyFactor, currencyfactor);

                command.Parameters.AddWithValue(Constant.token, token);

                command.Parameters.AddWithValue(Constant.ReservationCancellationType, cancellationPolicyResult.ReservationCancellationType);
                command.Parameters.AddWithValue(Constant.PreBookCancellationType, cancellationPolicyResult.PreBookCancellationType);
                command.Parameters.AddWithValue(Constant.CancellationPolicyRemark, cancellationPolicyResult.CancellationPolicyRemark);

                if (!string.IsNullOrEmpty(roomResult.ReservationGiataMappingId))
                {
                    command.Parameters.AddWithValue(Constant.ReservationGiataMappingId, roomResult.ReservationGiataMappingId);
                }
                if (!string.IsNullOrEmpty(roomResult.SearchGiataMappingId))
                {
                    command.Parameters.AddWithValue(Constant.SearchGiataMappingId, roomResult.SearchGiataMappingId);
                }
                if (!string.IsNullOrEmpty(roomResult.ResservationGiataPropertyName))
                {
                    command.Parameters.AddWithValue(Constant.ReservationGiataPropertyName, roomResult.ResservationGiataPropertyName);
                }
                if (!string.IsNullOrEmpty(roomResult.PreBookGiataPropertyName))
                {
                    command.Parameters.AddWithValue(Constant.PreBookGiataPropertyName, roomResult.PreBookGiataPropertyName);
                }
                if (!string.IsNullOrEmpty(roomResult.ReservationRoomBoardGroup))
                {
                    command.Parameters.AddWithValue(Constant.ReservationRoomBoardGroup, roomResult.ReservationRoomBoardGroup);
                }
                if (!string.IsNullOrEmpty(roomResult.PrebookRoomBoardGroup))
                {
                    command.Parameters.AddWithValue(Constant.PrebookRoomBoardGroup, roomResult.PrebookRoomBoardGroup);
                }

                command.Parameters.AddWithValue(Constant.IsOptimized, isoptimized);

                var result = command.ExecuteNonQuery();
                connection.Close();
                watchPreBookReservation.Stop();
                var elapsedTimeInSeconds = watchPreBookReservation.Elapsed.TotalSeconds;
                LoggerPersistance.SearchSyncLogging(Constant.PreBook, reservationId, 11, steptoken, elapsedTimeInSeconds, "insertprebook", RePricerId);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = Constant.InsertReservation,
                };
                _log.Error(irixErrorEntity, ex);
                //throw;
            }
        }

        /// <summary>
        /// Price Edge Cases
        /// </summary>
        /// <param name="availabilityToken"></param>
        /// <param name="reservationId"></param>
        /// <param name="RePricerId"></param>
        /// <param name="isEmailSent"></param>
        /// <param name="CurrentPrice"></param>
        /// <param name="PreBookResponseJson"></param>
        /// <param name="Profit"></param>
        /// <param name="ProfitAfterCancellation"></param>
        /// <param name="cancellationPolicyResult"></param>
        /// <param name="token"></param>
        /// <param name="roomResult"></param>
        /// <param name="searchSyncJson"></param>
        /// <param name="currencyfactor"></param>
        /// <param name="steptoken"></param>
        public void InsertPreBookReservationLog(string availabilityToken, int reservationId, int RePricerId, int isEmailSent, decimal CurrentPrice, string PreBookResponseJson, decimal Profit, decimal ProfitAfterCancellation, CancellationPolicyResult cancellationPolicyResult, string token, RoomResult roomResult, string searchSyncJson, decimal currencyfactor, string steptoken = null)
        {
            if (cancellationPolicyResult != null)
            {
                try
                {
                    //if (isoptimized)
                    //{
                    //    PreBookResponseJson = PreBookResponseJson.Replace("'", " ");
                    //    searchSyncJson = searchSyncJson.Replace("'", " ");
                    //}
                    //else
                    {
                        PreBookResponseJson = string.Empty;
                        searchSyncJson = string.Empty;
                    }
                    var watchPreBookReservationlog = Stopwatch.StartNew();

                    using SqlConnection connection = new SqlConnection(_connectionString);
                    connection.Open();

                    using SqlCommand command = new SqlCommand(Constant.InsertPreBookReservationlog, connection);
                    command.CommandType = CommandType.StoredProcedure;
                    command.CommandTimeout = _commandTimeout;

                    // Add parameters
                    command.Parameters.AddWithValue(Constant.AvailabilityToken, availabilityToken);
                    command.Parameters.AddWithValue(Constant.ReservationId, reservationId);
                    command.Parameters.AddWithValue(Constant.RePricerId, RePricerId);
                    command.Parameters.AddWithValue(Constant.PreBookPrice, CurrentPrice);
                    command.Parameters.AddWithValue(Constant.PreBookResponseJson, PreBookResponseJson);
                    command.Parameters.AddWithValue(Constant.Profit, Profit);
                    command.Parameters.AddWithValue(Constant.ProfitAfterCancellation, ProfitAfterCancellation);
                    command.Parameters.AddWithValue(Constant.IsCancellationPolicyMatched, cancellationPolicyResult.IsCancellationPolicyMatched);
                    command.Parameters.AddWithValue(Constant.cPStatus, cancellationPolicyResult.cPStatus);
                    command.Parameters.AddWithValue(Constant.cpdaysgain, cancellationPolicyResult.daysgain);
                    command.Parameters.AddWithValue(Constant.matchedcancellationpolicygain, cancellationPolicyResult.matchedcancellationpolicygain);

                    command.Parameters.AddWithValue(Constant.ReservationAdultCount, roomResult.ReservationPassengerCount);
                    command.Parameters.AddWithValue(Constant.PreBookAdultCount, roomResult.PreBookPassengerCount);
                    command.Parameters.AddWithValue(Constant.ReservationChildAges, roomResult.ReservationchildAges);
                    command.Parameters.AddWithValue(Constant.PreBookChildAges, roomResult.PrebookChildAges);
                    command.Parameters.AddWithValue(Constant.ReservationRoomName, roomResult.ReservationRoomName);
                    command.Parameters.AddWithValue(Constant.PreBookRoomName, roomResult.PreBookRoomName);
                    command.Parameters.AddWithValue(Constant.ReservationRoomBoard, roomResult.ReservationRoomBoard);
                    command.Parameters.AddWithValue(Constant.PreBookRoomBoard, roomResult.PreBookRoomBoard);
                    command.Parameters.AddWithValue(Constant.ReservationRoomInfo, roomResult.ReservationRoomInfo);
                    command.Parameters.AddWithValue(Constant.PrebookRoomInfo, roomResult.PreBookRoomInfo);
                    command.Parameters.AddWithValue(Constant.PreBookRoomIndex, roomResult.PreBookRoomIndex);

                    command.Parameters.AddWithValue(Constant.BookingDate, roomResult.BookingDate);
                    command.Parameters.AddWithValue(Constant.ReservationPrice, roomResult.ReservationPrice);
                    command.Parameters.AddWithValue(Constant.Providers, roomResult.ReservationProviders);
                    command.Parameters.AddWithValue(Constant.PrebookProviders, roomResult.PrebookProviders);
                    command.Parameters.AddWithValue(Constant.SearchJson, searchSyncJson);

                    command.Parameters.AddWithValue(Constant.MatchedPreBookCancellationChargeByPolicy, cancellationPolicyResult.PreBookCancellationChargeByPolicies);
                    command.Parameters.AddWithValue(Constant.MatchedReservationCancellationChargeByPolicy, cancellationPolicyResult.ReservationCancellationChargeByPolicies);
                    command.Parameters.AddWithValue(Constant.MatchedPreBookCancellationDate, cancellationPolicyResult.PreBookCancellationDate);
                    command.Parameters.AddWithValue(Constant.MatchedReservationCancellationDate, cancellationPolicyResult.ReservationCancellationDate);
                    command.Parameters.AddWithValue(Constant.CurrencyFactor, currencyfactor);

                    command.Parameters.AddWithValue(Constant.token, token);

                    command.Parameters.AddWithValue(Constant.ReservationCancellationType, cancellationPolicyResult.ReservationCancellationType);
                    command.Parameters.AddWithValue(Constant.PreBookCancellationType, cancellationPolicyResult.PreBookCancellationType);
                    command.Parameters.AddWithValue(Constant.CancellationPolicyRemark, cancellationPolicyResult.CancellationPolicyRemark);

                    if (!string.IsNullOrEmpty(roomResult.ReservationGiataMappingId))
                    {
                        command.Parameters.AddWithValue(Constant.ReservationGiataMappingId, roomResult.ReservationGiataMappingId);
                    }
                    if (!string.IsNullOrEmpty(roomResult.SearchGiataMappingId))
                    {
                        command.Parameters.AddWithValue(Constant.SearchGiataMappingId, roomResult.SearchGiataMappingId);
                    }
                    if (!string.IsNullOrEmpty(roomResult.ResservationGiataPropertyName))
                    {
                        command.Parameters.AddWithValue(Constant.ReservationGiataPropertyName, roomResult.ResservationGiataPropertyName);
                    }
                    if (!string.IsNullOrEmpty(roomResult.PreBookGiataPropertyName))
                    {
                        command.Parameters.AddWithValue(Constant.PreBookGiataPropertyName, roomResult.PreBookGiataPropertyName);
                    }
                    if (!string.IsNullOrEmpty(roomResult.ReservationRoomBoardGroup))
                    {
                        command.Parameters.AddWithValue(Constant.ReservationRoomBoardGroup, roomResult.ReservationRoomBoardGroup);
                    }
                    if (!string.IsNullOrEmpty(roomResult.PrebookRoomBoardGroup))
                    {
                        command.Parameters.AddWithValue(Constant.PrebookRoomBoardGroup, roomResult.PrebookRoomBoardGroup);
                    }

                    command.ExecuteNonQuery();
                    connection.Close();
                    watchPreBookReservationlog.Stop();
                    var elapsedTimeInSeconds = watchPreBookReservationlog.Elapsed.TotalSeconds;
                    LoggerPersistance.SearchSyncLogging(Constant.PreBook, reservationId, 11, steptoken, elapsedTimeInSeconds, "insertprebooklog", RePricerId);

                    //_log.Info($"ReservationPersistence|PreBookReservationlog|{elapsedTimeInSeconds}|{reservationId} {watchPreBookReservationlog.Elapsed}");
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = Constant.ReservationPersistance,
                        MethodName = Constant.InsertIntoPrebooklog,
                    };
                    _log.Error(irixErrorEntity, ex);
                    //throw;
                }
            }
        }

        public async Task<List<ReservationPrebookCount>> GetPrebookReservationIdsAsync(int RePricerId)
        {
            var watchPreBookReservationlog = Stopwatch.StartNew();

            var reservationData = new List<ReservationPrebookCount>();
            //try
            //{
            //    using SqlConnection connection = Connection();
            //    await connection.OpenAsync();
            //    SqlParameter[] parameters =
            //    {
            //      new SqlParameter(Constant.repricerId, SqlDbType.Int) { Value = repricerId }
            //    };

            //    using SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, Constant.GetPreBookReservation, parameters);
            //    while (reader.Read())
            //    {
            //        var reservationPrebook = new ReservationPrebookCount
            //        {
            //            ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
            //            PreBookCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "PreBookCount"),
            //            CreateDate = DbPropertyHelper.DateTimePropertyFromRow(reader, "createdate")
            //        };
            //        reservationData.Add(reservationPrebook);
            //    }
            //}
            //catch (Exception ex)
            //{
            //    var irixErrorEntity = new IrixErrorEntity
            //    {
            //        ClassName = Constant.ReservationPersistance,
            //        MethodName = Constant.GetPreBookReservation,
            //    };
            //    _log.Error(irixErrorEntity, ex);
            //}
            //watchPreBookReservationlog.Stop();
            //var elapsedTimeInSeconds = watchPreBookReservationlog.Elapsed.TotalSeconds;

            //_log.Info($"ReservationPersistence|GetPrebookReservationIdsAsync|{elapsedTimeInSeconds}|{repricerId} {watchPreBookReservationlog.Elapsed}");

            return reservationData;
        }

        public void InsertCriteriaJson(string criteriajson, int reservationid)
        {
            Task.Run(() =>
            {
                try
                {
                    using SqlConnection connection = new SqlConnection(_connectionString);
                    connection.Open();

                    using SqlCommand command = new SqlCommand(Constant.InsertCriteriaJson, connection);
                    command.CommandType = CommandType.StoredProcedure;
                    command.CommandTimeout = _commandTimeout;

                    // Add parameters
                    command.Parameters.AddWithValue(Constant.ReservationId, reservationid);
                    command.Parameters.AddWithValue(Constant.CriteriaJson, criteriajson);

                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = Constant.ReservationPersistance,
                        MethodName = Constant.InsertReservationDataAsync,
                    };
                    _log.Error(irixErrorEntity, ex);
                    throw;
                }
            });
        }

        public bool UpdateReservationCancellation(int reservationId)
        {
            bool isUpdateSuccessful = false;

            try
            {
                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.UpdateCancelledReservation, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                command.Parameters.AddWithValue(Constant.ReservationId, reservationId);

                int rowsAffected = command.ExecuteNonQuery();

                isUpdateSuccessful = rowsAffected > 0;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = "ReservationPersistence",
                    MethodName = "UpdateReservationCancellation",
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            return isUpdateSuccessful;
        }

        public bool DeletePreBook()
        {
            try
            {
                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.deletePrebook, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                command.ExecuteNonQuery();
                connection.Close();

                return true; // Return true to indicate success
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = Constant.InsertReservationDataAsync,
                };
                _log.Error(irixErrorEntity, ex);

                return false; // Return false to indicate failure
            }
        }

        public List<ReservationComparison> LoadReservationComparisons()
        {
            var reservationComparisons = new List<ReservationComparison>();

            try
            {
                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.GetReservationForBilling, connection);
                command.CommandType = CommandType.Text;

                using SqlDataReader reader = command.ExecuteReader();

                while (reader.Read())
                {
                    var reservationComparison = new ReservationComparison
                    {
                        OldReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "OldReservationId"),
                        NewReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "NewReservationId"),
                        ACComdationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ACComdationId"),
                        CheckIn = DbPropertyHelper.DateTimePropertyFromRow(reader, "CheckIn"),
                        Checkout = DbPropertyHelper.DateTimePropertyFromRow(reader, "Checkout"),
                        LeaderNationality = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "LeaderNationality"),
                        AdultCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "AdultCount"),
                        ChildrenAges = DbPropertyHelper.StringPropertyFromRow(reader, "ChildrenAges"),
                        IsCancelled = DbPropertyHelper.BoolPropertyFromRow(reader, "IsCancelled"),
                        NewPrice = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "NewPrice"),
                        OldPrice = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "OldPrice")
                    };

                    reservationComparisons.Add(reservationComparison);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ClientPersistance,
                    MethodName = Constant.LoadRePricerDetail,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            return reservationComparisons;
        }

        public List<ReservationBilling> LoadReservationBilling()
        {
            var reservationComparisons = new List<ReservationBilling>();

            try
            {
                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.GetReservationForBillings, connection);
                command.CommandType = CommandType.Text;

                using SqlDataReader reader = command.ExecuteReader();

                while (reader.Read())
                {
                    var reservationComparison = new ReservationBilling
                    {
                        OldReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "OldReservationId"),
                        NewReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "NewReservationId"),
                        NewReservationPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "NewReservationPrice"),
                        OldReservationPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "OldReservationPrice"),
                        RepricerPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "RepricerPrice"),
                        NewACComdationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "NewACComdationId"),
                        OldACComdationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "OldACComdationId"),
                        NewCheckIn = DbPropertyHelper.StringPropertyFromRow(reader, "NewCheckIn"),
                        OldCheckIn = DbPropertyHelper.StringPropertyFromRow(reader, "OldCheckIn"),
                        NewCheckOut = DbPropertyHelper.StringPropertyFromRow(reader, "NewCheckOut"),
                        OldCheckOut = DbPropertyHelper.StringPropertyFromRow(reader, "OldCheckOut"),
                        NewLeaderNationality = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "NewLeaderNationality"),
                        NewAdultCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "NewAdultCount"),
                        NewChildrenAges = DbPropertyHelper.StringPropertyFromRow(reader, "NewChildrenAges"),
                        PreBookCreatedDateForOldReservation = DbPropertyHelper.StringPropertyFromRow(reader, "PreBookCreatedDateForOldReservation"),
                        NewReservationBookingDate = DbPropertyHelper.StringPropertyFromRow(reader, "NewReservationBookingDate"),
                        IsPassengerCheck = DbPropertyHelper.BoolPropertyFromRow(reader, "IsPassengerCheck"),
                        IsRoomNameCheck = DbPropertyHelper.BoolPropertyFromRow(reader, "IsRoomNameCheck")
                    };

                    reservationComparisons.Add(reservationComparison);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ClientPersistance,
                    MethodName = Constant.LoadRePricerDetail,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            return reservationComparisons;
        }

        public void InsertExchangeRateData(int RePricerId, List<ExchangeRate> exchangeRates, string ExchangeRateDate)
        {
            const int maxRetryAttempts = 3;
            int retryCount = 0;
            var watch = Stopwatch.StartNew();

            while (retryCount < maxRetryAttempts)
            {
                try
                {
                    lock (_lock_InsertExchangeRateData)  // Thread-safe lock to serialize access
                    {
                        using (SqlConnection connection = new SqlConnection(_connectionString))
                        {
                            connection.Open();

                            foreach (var exchangeRate in exchangeRates)
                            {
                                using (SqlCommand command = new SqlCommand(Constant.InsertExchangeRateData, connection))
                                {
                                    command.CommandType = CommandType.StoredProcedure;
                                    command.CommandTimeout = _commandTimeout;

                                    // Add parameters
                                    command.Parameters.AddWithValue(Constant.RePricerId, RePricerId);
                                    command.Parameters.AddWithValue(Constant.FromCurrency, exchangeRate.FromCurrency);
                                    command.Parameters.AddWithValue(Constant.ToCurrency, exchangeRate.ToCurrency);
                                    command.Parameters.AddWithValue(Constant.Factor, exchangeRate.Factor);
                                    command.Parameters.AddWithValue(Constant.ExchangeRateDate, ExchangeRateDate);

                                    command.ExecuteNonQuery();
                                }
                            }
                        }
                    }

                    // If successful, break out of the retry loop
                    break;
                }
                catch (SqlException sqlEx) when (sqlEx.Number == -2) // SQL Timeout error code
                {
                    retryCount++;
                    _log.Info($"SQL Timeout occurred in InsertExchangeRateData (attempt {retryCount}): {sqlEx.Message}");
                    if (retryCount >= maxRetryAttempts)
                    {
                        _log.Info("Max retry attempts reached for InsertExchangeRateData.");
                        throw;  // After max retries, throw the exception
                    }
                    // Exponential backoff delay before retrying
                    Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, retryCount))).Wait();
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = Constant.ReservationPersistance,
                        MethodName = Constant.MethInsertExchangeRateData,
                        Params = ex.Message
                    };
                    _log.Error(irixErrorEntity, ex);
                    throw;
                }
            }

            watch.Stop();
            var elapsedTimeInSeconds = watch.Elapsed.TotalSeconds;
            _log.Info($"ReservationPersistence|InsertExchangeRateData|Elapsed Time: {elapsedTimeInSeconds} seconds|RePricerId: {RePricerId}");
        }

        public List<ExchangeRate> LoadExchangeRate(int repricerId, string? exchangeRateDate = null)
        {
            var exchangrateResult = new List<ExchangeRate>();

            try
            {
                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.GetExchangeRate, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                // Add the repricerId parameter
                command.Parameters.AddWithValue("@RePricerId", repricerId);
                if (!string.IsNullOrEmpty(exchangeRateDate))
                {
                    command.Parameters.AddWithValue("@exchangeRateDate", exchangeRateDate);
                }

                using SqlDataReader reader = command.ExecuteReader();

                while (reader.Read())
                {
                    try
                    {
                        var exchangeRate = new ExchangeRate
                        {
                            RePricerId = DbPropertyHelper.Int32PropertyFromRow(reader, "RePricerId"),
                            FromCurrency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "FromCurrency"),
                            ToCurrency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ToCurrency"),
                            Factor = DbPropertyHelper.DecimalPropertyFromRow(reader, "Factor"),
                            CurrencyBufferPercent = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "CurrencyBufferPercent"),
                            ExchangeRateDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ExchangeRateDate"),
                            UpdatedOn = DbPropertyHelper.DateTimeNullablePropertyFromRow(reader, "UpdatedOn")
                        };

                        exchangrateResult.Add(exchangeRate);
                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = Constant.ClientPersistance,
                            MethodName = Constant.LoadRePricerDetail,
                        };
                        _log.Error(irixErrorEntity, ex);
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ClientPersistance,
                    MethodName = Constant.LoadRePricerDetail,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            return exchangrateResult;
        }

        public ReportData GetReportData(int Repricerid, int traveldaysmaxsearchindays, int traveldaysminsearchindays)
        {
            var reportData = new ReportData();
            try
            {
                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.GetRePortData, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                // Add the repricerId parameter
                command.Parameters.AddWithValue(Constant.RePricerId, Repricerid);

                command.Parameters.AddWithValue(Constant.traveldaysmaxsearchindays, traveldaysmaxsearchindays);
                command.Parameters.AddWithValue(Constant.traveldaysminsearchindays, traveldaysminsearchindays);

                using SqlDataReader reader = command.ExecuteReader();
                while (reader.Read())
                {
                    reportData.ReservationDownloadCount = Convert.ToInt32(reader["reservationdownloadCount"]);

                    if (reader.NextResult() && reader.Read())
                    {
                        reportData.EmailDeliveredCount = Convert.ToInt32(reader["EmailDeleiveredCount"]);
                    }

                    if (reader.NextResult() && reader.Read())
                    {
                        reportData.FilteredReservationCount = Convert.ToInt32(reader["filteredreservationcout"]);
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ClientPersistance,
                    MethodName = Constant.LoadRePricerDetail,
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }

            return reportData;
        }

        public void InsertPreBookClientConfiguration(SmtpConfigModel configModel, int repricerId, int reservationId, string token)
        {
            var watchPreBookReservationlog = Stopwatch.StartNew();

            try
            {
                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.InsertPreBookCriteria, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                // Add parameters
                command.Parameters.AddWithValue(Constant.RepricerId, repricerId);
                command.Parameters.AddWithValue(Constant.ReservationId, reservationId);
                command.Parameters.AddWithValue(Constant.CreatedDate, DateTime.UtcNow);
                command.Parameters.AddWithValue(Constant.PriceDifferenceValue, configModel.PriceDifferenceValue);
                command.Parameters.AddWithValue(Constant.PriceDifferencePercentage, configModel.PriceDifferencePercentage);
                command.Parameters.AddWithValue(Constant.IsUsePercentage, configModel.IsUsePercentage);
                command.Parameters.AddWithValue(Constant.MaxNumberOfTimesOptimization, configModel.MaxNumberOfTimesOptimization);
                command.Parameters.AddWithValue(Constant.Traveldaysmaxsearchindays, configModel.traveldaysmaxsearchindays);
                command.Parameters.AddWithValue(Constant.Traveldaysminsearchindays, configModel.traveldaysminsearchindays);
                command.Parameters.AddWithValue(Constant.ClientConfig_DaysDifferenceInPreBookCreation, configModel.ClientConfig_DaysDifferenceInPreBookCreation);
                command.Parameters.AddWithValue(Constant.Pricedifferencecurrency, configModel.pricedifferencecurrency);
                command.Parameters.AddWithValue(Constant.token, token);

                command.ExecuteNonQuery();
                connection.Close();
                watchPreBookReservationlog.Stop();
                var elapsedTimeInSeconds = watchPreBookReservationlog.Elapsed.TotalSeconds;

                _log.Info($"ReservationPersistence|InsertExchangeRateData|{elapsedTimeInSeconds}|{reservationId} {watchPreBookReservationlog.Elapsed}");
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance, // Replace with your class name
                    MethodName = "InsertPreBookClientConfiguration",
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public async Task InsertSerachSync(string searchdatajson, int repricerId, int reservationId)
        {
            var watchPreBookReservationlog = Stopwatch.StartNew();

            try
            {
                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.InsertSearchSync, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                // Add parameters
                command.Parameters.AddWithValue(Constant.SearchSyncData, searchdatajson);

                command.Parameters.AddWithValue(Constant.RepricerId, repricerId);
                command.Parameters.AddWithValue(Constant.ReservationId, reservationId);
                //command.Parameters.AddWithValue(Constant.CreatedDate, DateTime.UtcNow);

                await command.ExecuteNonQueryAsync();
                connection.Close();
                watchPreBookReservationlog.Stop();
                var elapsedTimeInSeconds = watchPreBookReservationlog.Elapsed.TotalSeconds;

                _log.Info($"ReservationPersistence|InsertSerachSync|{elapsedTimeInSeconds}|{reservationId} {watchPreBookReservationlog.Elapsed}");
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance, // Replace with your class name
                    MethodName = "InsertPreBookClientConfiguration",
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public async Task SupplierInsertSerachSync(string searchdatajson, int repricerId, int reservationId)
        {
            var watchPreBookReservationlog = Stopwatch.StartNew();

            try
            {
                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.SupplierInsertSearchSync, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                // Add parameters
                command.Parameters.AddWithValue(Constant.SearchSyncData, searchdatajson);

                command.Parameters.AddWithValue(Constant.RepricerId, repricerId);
                command.Parameters.AddWithValue(Constant.ReservationId, reservationId);
                //command.Parameters.AddWithValue(Constant.CreatedDate, DateTime.UtcNow);

                await command.ExecuteNonQueryAsync();
                connection.Close();
                watchPreBookReservationlog.Stop();
                var elapsedTimeInSeconds = watchPreBookReservationlog.Elapsed.TotalSeconds;

                _log.Info($"ReservationPersistence|InsertSerachSync|{elapsedTimeInSeconds}|{reservationId} {watchPreBookReservationlog.Elapsed}");
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance, // Replace with your class name
                    MethodName = "InsertPreBookClientConfiguration",
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public async Task InsertIntoPrebooklog(
                             int reservationid,
                             int repricerid,
                             RoomResult roomResult,
                             string hotelname,
                             decimal percentageDifference,
                             decimal amountLess,
                             decimal PreBookPrice,
                             string currency,
                             string criteriajson, string searchsyncjson
                                  , decimal currencyfactor)
        {
            var watchPreBookReservationlog = Stopwatch.StartNew();

            searchsyncjson = string.Empty;
            try
            {
                using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                using SqlCommand command = new SqlCommand(Constant.InsertIntoPrebooklog, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                // Add parameters
                command.Parameters.AddWithValue(Constant.ReservationId, reservationid);
                command.Parameters.AddWithValue(Constant.RePricerId, repricerid);
                command.Parameters.AddWithValue(Constant.PreBookPrice, PreBookPrice);
                command.Parameters.AddWithValue(Constant.SearchSyncCriteriaJson, criteriajson);
                command.Parameters.AddWithValue(Constant.Profit, amountLess);

                command.Parameters.AddWithValue(Constant.ReservationAdultCount, roomResult.ReservationPassengerCount);
                command.Parameters.AddWithValue(Constant.PreBookAdultCount, roomResult.PreBookPassengerCount);
                command.Parameters.AddWithValue(Constant.ReservationChildAges, roomResult.ReservationchildAges);
                command.Parameters.AddWithValue(Constant.PreBookChildAges, roomResult.PrebookChildAges);
                command.Parameters.AddWithValue(Constant.ReservationRoomName, roomResult.ReservationRoomName);
                command.Parameters.AddWithValue(Constant.PreBookRoomName, roomResult.PreBookRoomName);
                command.Parameters.AddWithValue(Constant.ReservationRoomBoard, roomResult.ReservationRoomBoard);
                command.Parameters.AddWithValue(Constant.PreBookRoomBoard, roomResult.PreBookRoomBoard);
                command.Parameters.AddWithValue(Constant.ReservationRoomInfo, roomResult.ReservationRoomInfo);
                command.Parameters.AddWithValue(Constant.PrebookRoomInfo, roomResult.PreBookRoomInfo);
                command.Parameters.AddWithValue(Constant.PreBookRoomIndex, roomResult.PreBookRoomIndex);

                command.Parameters.AddWithValue(Constant.BookingDate, roomResult.BookingDate);
                command.Parameters.AddWithValue(Constant.ReservationPrice, roomResult.ReservationPrice);
                command.Parameters.AddWithValue(Constant.Providers, roomResult.ReservationProviders);
                command.Parameters.AddWithValue(Constant.PrebookProviders, roomResult.PrebookProviders);
                command.Parameters.AddWithValue(Constant.SearchJson, searchsyncjson);
                command.Parameters.AddWithValue(Constant.CurrencyFactor, currencyfactor);
                if (!string.IsNullOrEmpty(roomResult.ReservationGiataMappingId))
                {
                    command.Parameters.AddWithValue(Constant.ReservationGiataMappingId, roomResult.ReservationGiataMappingId);
                }
                if (!string.IsNullOrEmpty(roomResult.SearchGiataMappingId))
                {
                    command.Parameters.AddWithValue(Constant.SearchGiataMappingId, roomResult.SearchGiataMappingId);
                }
                if (!string.IsNullOrEmpty(roomResult.ResservationGiataPropertyName))
                {
                    command.Parameters.AddWithValue(Constant.ReservationGiataPropertyName, roomResult.ResservationGiataPropertyName);
                }
                if (!string.IsNullOrEmpty(roomResult.PreBookGiataPropertyName))
                {
                    command.Parameters.AddWithValue(Constant.PreBookGiataPropertyName, roomResult.PreBookGiataPropertyName);
                }
                if (!string.IsNullOrEmpty(roomResult.ReservationRoomBoardGroup))
                {
                    command.Parameters.AddWithValue(Constant.ReservationRoomBoardGroup, roomResult.ReservationRoomBoardGroup);
                }
                if (!string.IsNullOrEmpty(roomResult.PrebookRoomBoardGroup))
                {
                    command.Parameters.AddWithValue(Constant.PrebookRoomBoardGroup, roomResult.PrebookRoomBoardGroup);
                }

                await command.ExecuteNonQueryAsync();
                connection.Close();
                watchPreBookReservationlog.Stop();
                var elapsedTimeInSeconds = watchPreBookReservationlog.Elapsed.TotalSeconds;

                _log.Info($"ReservationPersistence|InsertIntoPrebooklog|{elapsedTimeInSeconds} {watchPreBookReservationlog.Elapsed}");
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = "YourClassName", // Replace with your class name
                    MethodName = "InsertIntoPrebooklog",
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public async Task<List<CancellationPolicyReservation>> GetCancellationPolicyReservationIdsAsync(int RePricerId, int? reservationId = 0)
        {
            var watchPreBookReservationlog = Stopwatch.StartNew();

            var cancellationData = new List<CancellationPolicyReservation>();
            try
            {
                using SqlConnection connection = Connection();
                await connection.OpenAsync();
                SqlParameter[] parameters =
                {
                   new SqlParameter(Constant.RePricerId, SqlDbType.Int) { Value = RePricerId }
                };
                if (reservationId > 0)
                {
                    parameters = new SqlParameter[2]
                                          {
                                                new SqlParameter(Constant.RePricerId, SqlDbType.Int)
                                                {
                                                    Value = RePricerId
                                                },
                                                new SqlParameter(Constant.ReservationId, SqlDbType.Int)
                                                {
                                                    Value = reservationId
                                                },
                                          };
                }

                var procName = Constant.GetCancellationPolicy;
                if (_isMock)
                {
                    procName = Constant.GetCancellationPolicyV0;
                }

                using SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, procName, parameters);
                while (reader.Read())
                {
                    var cancellationpolicyreservation = new CancellationPolicyReservation
                    {
                        ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                        CancellationType = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Cancellation_type"),
                        DaysDifference = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "DaysDifference"),
                        CancellationDate = DbPropertyHelper.DateTimePropertyFromRow(reader, "cancellationdate"),
                        CancellationCharge = DbPropertyHelper.DecimalPropertyFromRow(reader, "Cancellation_Charge"),
                        Currency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Currency"),
                        MainCancellationDate = DbPropertyHelper.DateTimePropertyFromRow(reader, "maincancellationdate")
                    };
                    cancellationData.Add(cancellationpolicyreservation);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = "GetCancellationPolicyReservationIdsAsync",
                };
                _log.Error(irixErrorEntity, ex);
            }
            watchPreBookReservationlog.Stop();
            var elapsedTimeInSeconds = watchPreBookReservationlog.Elapsed.TotalSeconds;

            _log.Info($"ReservationPersistence|GetCancellationPolicyReservationIdsAsync|{elapsedTimeInSeconds}|{RePricerId} {watchPreBookReservationlog.Elapsed}");

            return cancellationData;
        }

        public void UpdateBookingActionTakenInDB(List<BookingActionTakenModel> updateModels, bool isCacheRefresh = false)
        {
            var watchPreBookReservationlog = Stopwatch.StartNew();

            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    foreach (var updateModel in updateModels)
                    {
                        try
                        {
                            using (SqlCommand command = new SqlCommand("dbo.usp_upd_BookingActionsTaken", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.CommandTimeout = _commandTimeout;

                                command.Parameters.AddWithValue("@RepricerId", updateModel.RepricerId);
                                command.Parameters.AddWithValue("@ReservationId", updateModel.ReservationId);
                                command.Parameters.AddWithValue("@ExtraData", updateModel.ExtraData);
                                command.Parameters.AddWithValue("@HMNotes", updateModel.HMNotes);
                                command.Parameters.AddWithValue("@NewBookingId", updateModel.NewBookingId);
                                command.Parameters.AddWithValue("@ActionId", updateModel.ActionId);
                                command.Parameters.AddWithValue("@NewBookingPrice", updateModel.NewBookingPrice);
                                command.Parameters.AddWithValue("@CreatedById", updateModel.createdById);
                                command.Parameters.AddWithValue("@CreatedByName", updateModel.createdByName);
                                command.Parameters.AddWithValue("@token", updateModel.token);
                                command.Parameters.AddWithValue("@ResponseBody", updateModel.ResponseBody);
                                command.Parameters.AddWithValue("@GainConvertedCurrency", updateModel.GainConvertedCurrency);
                                command.Parameters.AddWithValue("@CurrencyFactor", updateModel.CurrencyFactor);
                                command.Parameters.AddWithValue("@GainAmountInOriginalCurrency", updateModel.GainAmountInOriginalCurrency);
                                command.Parameters.AddWithValue("@OriginalCurrency", updateModel.OriginalCurrency);
                                command.Parameters.AddWithValue("@CreatedOn", updateModel.CreatedOn);
                                command.ExecuteNonQuery();

                                PrebookRequest prebookRequest = new PrebookRequest();
                                prebookRequest.RepricerId = updateModel.RepricerId;
                                prebookRequest.ReservationId = updateModel.ReservationId.ToString();
                                prebookRequest.IsCached = false;
                                if (isCacheRefresh
                                    && (
                                    updateModel.ExtraData == OptimizationStatusEnum.DryRunOptimizationSucess_ExpectedGainAboveThreshold.ToString()
                                    || updateModel.ActionId == 8
                                    || updateModel.ActionId == 1
                                    )
                                )
                                {
                                    prebookRequest.IsCached = true;
                                }
                                if (prebookRequest.IsCached)
                                {
                                    Task.Run(() =>
                                    {
                                        #region Update cache

                                        try
                                        {
                                            _masterPersistence.GetBookingActionsTaken(prebookRequest);
                                        }
                                        catch (Exception ex)
                                        {
                                            var irixErrorEntity = new IrixErrorEntity
                                            {
                                                ClassName = Constant.ReservationPersistance, // Replace with your class name
                                                MethodName = "UpdateReservationTable->CacheUpdate",
                                            };
                                            _log.Error(irixErrorEntity, ex);
                                        }

                                        #endregion Update cache
                                    });
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = Constant.ReservationPersistance, // Replace with your class name
                                MethodName = "UpdateReservationTable",
                            };
                            _log.Error(irixErrorEntity, ex);
                        }
                    }

                    connection.Close();
                }
                watchPreBookReservationlog.Stop();
                var elapsedTimeInSeconds = watchPreBookReservationlog.Elapsed.TotalSeconds;

                _log.Info($"ReservationPersistence|UpdateReservationTable|{elapsedTimeInSeconds}|{updateModels} {watchPreBookReservationlog.Elapsed}");
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance, // Replace with your class name
                    MethodName = "UpdateReservationTable",
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public void InsertPreBookReservationSupplierSearch(string availabilityToken, int reservationId, int RePricerId, int isEmailSent, decimal CurrentPrice, int preBookCount, string emailbody, string PreBookResponseJson, string reservationemailbody, string prebookemailbody, decimal Profit, decimal ProfitAfterCancellation, CancellationPolicyResult cancellationPolicyResult, string token, RoomResult roomResult, string searchSyncJson, decimal currencyfactor, int ClientConfig_DaysDifferenceInPreBookCreation, string JobToken)
        {
            try
            {
                PreBookResponseJson = string.Empty;
                searchSyncJson = string.Empty;

                var watchPreBookReservation = Stopwatch.StartNew();

                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.InsertPreBookReservationSupplier, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                // Add parameters
                command.Parameters.AddWithValue(Constant.AvailabilityToken, availabilityToken);
                command.Parameters.AddWithValue(Constant.ReservationId, reservationId);
                command.Parameters.AddWithValue(Constant.RePricerId, RePricerId);
                command.Parameters.AddWithValue(Constant.IsMailSent, isEmailSent);
                command.Parameters.AddWithValue(Constant.PreBookPrice, CurrentPrice);
                command.Parameters.AddWithValue(Constant.preBookCount, preBookCount);
                command.Parameters.AddWithValue(Constant.emailbody, emailbody);
                command.Parameters.AddWithValue(Constant.PreBookResponseJson, PreBookResponseJson);
                command.Parameters.AddWithValue(Constant.reservationemailbody, reservationemailbody);
                command.Parameters.AddWithValue(Constant.prebookemailbody, prebookemailbody);
                command.Parameters.AddWithValue(Constant.Profit, Profit);
                command.Parameters.AddWithValue(Constant.ProfitAfterCancellation, ProfitAfterCancellation);
                command.Parameters.AddWithValue(Constant.IsCancellationPolicyMatched, cancellationPolicyResult.IsCancellationPolicyMatched);
                command.Parameters.AddWithValue(Constant.cPStatus, cancellationPolicyResult.cPStatus);
                command.Parameters.AddWithValue(Constant.cpdaysgain, cancellationPolicyResult.daysgain);
                command.Parameters.AddWithValue(Constant.matchedcancellationpolicygain, cancellationPolicyResult.matchedcancellationpolicygain);
                command.Parameters.AddWithValue(Constant.ClientConfig_DaysDifferenceInPreBookCreation, ClientConfig_DaysDifferenceInPreBookCreation);
                command.Parameters.AddWithValue(Constant.ReservationAdultCount, roomResult.ReservationPassengerCount);
                command.Parameters.AddWithValue(Constant.PreBookAdultCount, roomResult.PreBookPassengerCount);
                command.Parameters.AddWithValue(Constant.ReservationChildAges, roomResult.ReservationchildAges);
                command.Parameters.AddWithValue(Constant.PreBookChildAges, roomResult.PrebookChildAges);
                command.Parameters.AddWithValue(Constant.ReservationRoomName, roomResult.ReservationRoomName);
                command.Parameters.AddWithValue(Constant.PreBookRoomName, roomResult.PreBookRoomName);
                command.Parameters.AddWithValue(Constant.ReservationRoomBoard, roomResult.ReservationRoomBoard);
                command.Parameters.AddWithValue(Constant.PreBookRoomBoard, roomResult.PreBookRoomBoard);
                command.Parameters.AddWithValue(Constant.ReservationRoomInfo, roomResult.ReservationRoomInfo);
                command.Parameters.AddWithValue(Constant.PrebookRoomInfo, roomResult.PreBookRoomInfo);
                command.Parameters.AddWithValue(Constant.PreBookRoomIndex, roomResult.PreBookRoomIndex);

                command.Parameters.AddWithValue(Constant.BookingDate, roomResult.BookingDate);
                command.Parameters.AddWithValue(Constant.ReservationPrice, roomResult.ReservationPrice);
                command.Parameters.AddWithValue(Constant.Providers, roomResult.ReservationProviders);
                command.Parameters.AddWithValue(Constant.PrebookProviders, roomResult.PrebookProviders);
                command.Parameters.AddWithValue(Constant.SearchJson, searchSyncJson);

                command.Parameters.AddWithValue(Constant.MatchedPreBookCancellationChargeByPolicy, cancellationPolicyResult.PreBookCancellationChargeByPolicies);
                command.Parameters.AddWithValue(Constant.MatchedReservationCancellationChargeByPolicy, cancellationPolicyResult.ReservationCancellationChargeByPolicies);
                command.Parameters.AddWithValue(Constant.MatchedPreBookCancellationDate, cancellationPolicyResult.PreBookCancellationDate);
                command.Parameters.AddWithValue(Constant.MatchedReservationCancellationDate, cancellationPolicyResult.ReservationCancellationDate);
                command.Parameters.AddWithValue(Constant.CurrencyFactor, currencyfactor);

                command.Parameters.AddWithValue(Constant.token, token);
                command.Parameters.AddWithValue(Constant.JobToken, JobToken);

                command.ExecuteNonQuery();
                connection.Close();
                watchPreBookReservation.Stop();
                var elapsedTimeInSeconds = watchPreBookReservation.Elapsed.TotalSeconds;

                _log.Info($"ReservationPersistence|PreBookSaveTime|{elapsedTimeInSeconds}|{reservationId} {watchPreBookReservation.Elapsed}");
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = Constant.InsertReservation,
                };
                _log.Error(irixErrorEntity, ex);
                //throw;
            }
        }

        public void InsertPreBookReservationSupplierSearchLog(string availabilityToken, int reservationId, int RePricerId, int isEmailSent, decimal CurrentPrice, string PreBookResponseJson, decimal Profit, decimal ProfitAfterCancellation, CancellationPolicyResult cancellationPolicyResult, string token, RoomResult roomResult, string searchSyncJson, decimal currencyfactor, string JobToken)
        {
            try
            {
                searchSyncJson = PreBookResponseJson = string.Empty;

                var watchPreBookReservationlog = Stopwatch.StartNew();

                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.InsertPreBookReservationlogSupplier, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                // Add parameters
                command.Parameters.AddWithValue(Constant.AvailabilityToken, availabilityToken);
                command.Parameters.AddWithValue(Constant.ReservationId, reservationId);
                command.Parameters.AddWithValue(Constant.RePricerId, RePricerId);
                command.Parameters.AddWithValue(Constant.PreBookPrice, CurrentPrice);
                command.Parameters.AddWithValue(Constant.PreBookResponseJson, PreBookResponseJson);
                command.Parameters.AddWithValue(Constant.Profit, Profit);
                command.Parameters.AddWithValue(Constant.ProfitAfterCancellation, ProfitAfterCancellation);
                command.Parameters.AddWithValue(Constant.IsCancellationPolicyMatched, cancellationPolicyResult.IsCancellationPolicyMatched);
                command.Parameters.AddWithValue(Constant.cPStatus, cancellationPolicyResult.cPStatus);
                command.Parameters.AddWithValue(Constant.cpdaysgain, cancellationPolicyResult.daysgain);
                command.Parameters.AddWithValue(Constant.matchedcancellationpolicygain, cancellationPolicyResult.matchedcancellationpolicygain);

                command.Parameters.AddWithValue(Constant.ReservationAdultCount, roomResult.ReservationPassengerCount);
                command.Parameters.AddWithValue(Constant.PreBookAdultCount, roomResult.PreBookPassengerCount);
                command.Parameters.AddWithValue(Constant.ReservationChildAges, roomResult.ReservationchildAges);
                command.Parameters.AddWithValue(Constant.PreBookChildAges, roomResult.PrebookChildAges);
                command.Parameters.AddWithValue(Constant.ReservationRoomName, roomResult.ReservationRoomName);
                command.Parameters.AddWithValue(Constant.PreBookRoomName, roomResult.PreBookRoomName);
                command.Parameters.AddWithValue(Constant.ReservationRoomBoard, roomResult.ReservationRoomBoard);
                command.Parameters.AddWithValue(Constant.PreBookRoomBoard, roomResult.PreBookRoomBoard);
                command.Parameters.AddWithValue(Constant.ReservationRoomInfo, roomResult.ReservationRoomInfo);
                command.Parameters.AddWithValue(Constant.PrebookRoomInfo, roomResult.PreBookRoomInfo);
                command.Parameters.AddWithValue(Constant.PreBookRoomIndex, roomResult.PreBookRoomIndex);

                command.Parameters.AddWithValue(Constant.BookingDate, roomResult.BookingDate);
                command.Parameters.AddWithValue(Constant.ReservationPrice, roomResult.ReservationPrice);
                command.Parameters.AddWithValue(Constant.Providers, roomResult.ReservationProviders);
                command.Parameters.AddWithValue(Constant.SearchJson, searchSyncJson);

                command.Parameters.AddWithValue(Constant.MatchedPreBookCancellationChargeByPolicy, cancellationPolicyResult.PreBookCancellationChargeByPolicies);
                command.Parameters.AddWithValue(Constant.MatchedReservationCancellationChargeByPolicy, cancellationPolicyResult.ReservationCancellationChargeByPolicies);
                command.Parameters.AddWithValue(Constant.MatchedPreBookCancellationDate, cancellationPolicyResult.PreBookCancellationDate);
                command.Parameters.AddWithValue(Constant.MatchedReservationCancellationDate, cancellationPolicyResult.ReservationCancellationDate);
                command.Parameters.AddWithValue(Constant.CurrencyFactor, currencyfactor);

                command.Parameters.AddWithValue(Constant.JobToken, JobToken);

                command.Parameters.AddWithValue(Constant.token, token);

                command.ExecuteNonQuery();
                connection.Close();
                watchPreBookReservationlog.Stop();
                var elapsedTimeInSeconds = watchPreBookReservationlog.Elapsed.TotalSeconds;

                _log.Info($"ReservationPersistence|PreBookReservationSupplierSearchlog|{elapsedTimeInSeconds}|{reservationId} {watchPreBookReservationlog.Elapsed}");
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = Constant.InsertIntoPrebooklog,
                };
                _log.Error(irixErrorEntity, ex);
                //throw;
            }
        }

        public async Task InsertIntoPrebookSupplierlog(
                             int reservationid,
                             int repricerid,
                             RoomResult roomResult,
                             string hotelname,
                             decimal percentageDifference,
                             decimal amountLess,
                             decimal PreBookPrice,
                             string currency,
                             string criteriajson, string searchsyncjson
                                  , decimal currencyfactor, string jobtoken = null)
        {
            var watchPreBookReservationlog = Stopwatch.StartNew();
            searchsyncjson = string.Empty;
            try
            {
                using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                using SqlCommand command = new SqlCommand(Constant.InsertIntoPrebookSupplierlog, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                // Add parameters
                command.Parameters.AddWithValue(Constant.ReservationId, reservationid);
                command.Parameters.AddWithValue(Constant.RePricerId, repricerid);
                command.Parameters.AddWithValue(Constant.PreBookPrice, PreBookPrice);
                command.Parameters.AddWithValue(Constant.SearchSyncCriteriaJson, criteriajson);
                command.Parameters.AddWithValue(Constant.Profit, amountLess);

                command.Parameters.AddWithValue(Constant.ReservationAdultCount, roomResult.ReservationPassengerCount);
                command.Parameters.AddWithValue(Constant.PreBookAdultCount, roomResult.PreBookPassengerCount);
                command.Parameters.AddWithValue(Constant.ReservationChildAges, roomResult.ReservationchildAges);
                command.Parameters.AddWithValue(Constant.PreBookChildAges, roomResult.PrebookChildAges);
                command.Parameters.AddWithValue(Constant.ReservationRoomName, roomResult.ReservationRoomName);
                command.Parameters.AddWithValue(Constant.PreBookRoomName, roomResult.PreBookRoomName);
                command.Parameters.AddWithValue(Constant.ReservationRoomBoard, roomResult.ReservationRoomBoard);
                command.Parameters.AddWithValue(Constant.PreBookRoomBoard, roomResult.PreBookRoomBoard);
                command.Parameters.AddWithValue(Constant.ReservationRoomInfo, roomResult.ReservationRoomInfo);
                command.Parameters.AddWithValue(Constant.PrebookRoomInfo, roomResult.PreBookRoomInfo);
                command.Parameters.AddWithValue(Constant.PreBookRoomIndex, roomResult.PreBookRoomIndex);

                command.Parameters.AddWithValue(Constant.BookingDate, roomResult.BookingDate);
                command.Parameters.AddWithValue(Constant.ReservationPrice, roomResult.ReservationPrice);
                command.Parameters.AddWithValue(Constant.Providers, roomResult.ReservationProviders);
                command.Parameters.AddWithValue(Constant.SearchJson, searchsyncjson);
                command.Parameters.AddWithValue(Constant.CurrencyFactor, currencyfactor);
                command.Parameters.AddWithValue(Constant.JobToken, jobtoken);

                await command.ExecuteNonQueryAsync();
                connection.Close();
                watchPreBookReservationlog.Stop();
                var elapsedTimeInSeconds = watchPreBookReservationlog.Elapsed.TotalSeconds;

                _log.Info($"ReservationPersistence|InsertIntoPrebooklog|{elapsedTimeInSeconds} {watchPreBookReservationlog.Elapsed}");
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = "InsertIntoPrebooklog",
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public async Task InsertSupplierAgentMaster(int RePricerId)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (SqlCommand command = new SqlCommand(Constant.InsertSupplierAgent, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;
                        command.Parameters.AddWithValue(Constant.RePricerId, RePricerId);

                        await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = Constant.InsertSupplierAgentMaster
                };
                _log.Error(irixErrorEntity, ex);
            }
        }

        public async Task<List<SupplierMaster>> GetSupplierMaster(int RePricerId)
        {
            var key = $"GetSupplierMaster_{RePricerId}";

            var supplierMasters = new List<SupplierMaster>();
            if (RedisCacheHelper.KeyExists(key))
            {
                supplierMasters = RedisCacheHelper.Get<List<SupplierMaster>>(key);
            }
            else
            {
                try
                {
                    using SqlConnection connection = Connection();
                    await connection.OpenAsync();
                    SqlParameter[] parameters =
                    {
                   new SqlParameter(Constant.RePricerId, SqlDbType.Int) { Value = RePricerId }
                };

                    using SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, Constant.GetSupplierMaster, parameters);
                    while (reader.Read())
                    {
                        var suppliermaster = new SupplierMaster
                        {
                            RepricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerId"),

                            SupplierName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "SupplierName"),
                            UpdatedBy = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "UpdatedBy"),
                            UpdateDate = DbPropertyHelper.DateTimePropertyFromRow(reader, "UpdateDate"),

                            IsActive = DbPropertyHelper.BoolDefaultPropertyFromRow(reader, "IsActive")
                        };
                        supplierMasters.Add(suppliermaster);
                    }
                    RedisCacheHelper.Set(key, supplierMasters, TimeSpan.FromHours(6));
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = Constant.ReservationPersistance,
                        MethodName = "GetSupplierMaster",
                    };
                    _log.Error(irixErrorEntity, ex);
                    throw;
                }
            }

            return supplierMasters;
        }

        public async Task<List<ResellerMaster>> GetResellerMaster(int RePricerId)
        {
            var key = $"GetResellerMaster_{RePricerId}";

            var resellerMasters = new List<ResellerMaster>();
            if (RedisCacheHelper.KeyExists(key))
            {
                resellerMasters = RedisCacheHelper.Get<List<ResellerMaster>>(key);
            }
            else
            {
                try
                {
                    using SqlConnection connection = Connection();
                    await connection.OpenAsync();
                    SqlParameter[] parameters =
                    {
                   new SqlParameter(Constant.RePricerId, SqlDbType.Int) { Value = RePricerId }
                };

                    using SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, Constant.GetResellerMaster, parameters);
                    while (reader.Read())
                    {
                        var resellerMaster = new ResellerMaster
                        {
                            RepricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerId"),
                            ResellerCode = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ResellerCode"),
                            ResellerName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ResellerName"),
                            ResellerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ResellerId"),
                            UpdatedBy = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "UpdatedBy"),
                            UpdateDate = DbPropertyHelper.DateTimePropertyFromRow(reader, "UpdateDate"),

                            IsActive = DbPropertyHelper.BoolDefaultPropertyFromRow(reader, "IsActive")
                        };
                        resellerMasters.Add(resellerMaster);
                    }
                    RedisCacheHelper.Set(key, resellerMasters, TimeSpan.FromHours(6));
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = Constant.ReservationPersistance,
                        MethodName = "GetSupplierMaster",
                    };
                    _log.Error(irixErrorEntity, ex);
                    throw;
                }
            }

            return resellerMasters;
        }

        public void UpdateSupplierMaster(List<SupplierMaster> supplierMasters)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    foreach (var supplierMaster in supplierMasters)
                    {
                        using (SqlCommand command = new SqlCommand("dbo.usp_upd_SupplierMaster", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.CommandTimeout = _commandTimeout;

                            command.Parameters.AddWithValue("@RepricerId", supplierMaster.RepricerId);
                            command.Parameters.AddWithValue("@IsActive", supplierMaster.IsActive);
                            command.Parameters.AddWithValue("@SupplierName", supplierMaster.SupplierName);
                            command.Parameters.AddWithValue("@UpdatedBy", supplierMaster.UpdatedBy);

                            command.ExecuteNonQuery();
                        }
                    }

                    connection.Close();
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance, // Replace with your class name
                    MethodName = "UpdateSupplierMaster",
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public void UpdateResellerMaster(List<ResellerMaster> resellerMasters)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    foreach (var resellerMaster in resellerMasters)
                    {
                        using (SqlCommand command = new SqlCommand("dbo.usp_upd_ResellerMaster", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.CommandTimeout = _commandTimeout;

                            command.Parameters.AddWithValue("@RepricerId", resellerMaster.RepricerId);
                            command.Parameters.AddWithValue("@IsActive", resellerMaster.IsActive);
                            command.Parameters.AddWithValue("@ResellerId", resellerMaster.ResellerId);
                            command.Parameters.AddWithValue("@UpdatedBy", resellerMaster.UpdatedBy);
                            command.Parameters.AddWithValue("@ResellerCode", resellerMaster.ResellerCode);

                            command.ExecuteNonQuery();
                        }
                    }

                    connection.Close();
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance, // Replace with your class name
                    MethodName = "UpdateResellerMaster",
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public void InsertReservationStatus(int repricerId, int reservationId, bool reservationStatus)
        {
            SqlConnection connection = null;
            try
            {
                connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand("dbo.usp_ins_ReservationStatus", connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                command.Parameters.AddWithValue("@RepricerID", repricerId);
                command.Parameters.AddWithValue("@ReservationID", reservationId);
                command.Parameters.AddWithValue("@CreatedDate", DateTime.UtcNow);
                command.Parameters.AddWithValue("@ReservationStatus", reservationStatus);

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance, // Replace with your class name
                    MethodName = "InsertReservationStatus",
                };
                _log.Error(irixErrorEntity, ex);
                //throw;
            }
            finally
            {
                if (connection != null && connection.State == ConnectionState.Open)
                    connection.Close();
            }
        }

        public void InsertReportinTable(int repricerId, int reservationId = 0)
        {
            int maxRetries = 3;
            int retryDelayMilliseconds = 2000; // Delay between retries (2 seconds)
            int attempt = 0;
            try
            {
                while (attempt < maxRetries)
                {
                    try
                    {
                        using (SqlConnection connection = new SqlConnection(_connectionString))
                        {
                            try
                            {
                                connection.Open();

                                using SqlCommand command = new SqlCommand(Constant.InsertReportData, connection);
                                command.CommandType = CommandType.StoredProcedure;
                                command.CommandTimeout = _commandTimeout;

                                command.Parameters.AddWithValue("@Repricerid", repricerId);
                                if (reservationId > 0)
                                {
                                    command.Parameters.AddWithValue("@Reservationid", reservationId);
                                }

                                command.ExecuteNonQuery();

                                // CRITICAL FIX: Call additional prebook stored procedure to populate ReservationReportDetailsAdditionalPrebook
                                using SqlCommand additionalPrebookCommand = new SqlCommand("usp_upd_reservationreport_AdditionalPrebook", connection);
                                additionalPrebookCommand.CommandType = CommandType.StoredProcedure;
                                additionalPrebookCommand.CommandTimeout = _commandTimeout;

                                additionalPrebookCommand.Parameters.AddWithValue("@Repricerid", repricerId);
                                if (reservationId > 0)
                                {
                                    additionalPrebookCommand.Parameters.AddWithValue("@Reservationid", reservationId);
                                }

                                additionalPrebookCommand.ExecuteNonQuery();

                                // If the execution is successful, break out of the retry loop
                                break;
                            }
                            catch (Exception ex)
                            {
                                // Log the error on each retry attempt
                                var message = ($"Attempt {attempt + 1} failed");

                                var irixErrorEntity = new IrixErrorEntity
                                {
                                    ClassName = Constant.ReservationPersistance, // Replace with your class name
                                    MethodName = nameof(InsertReportinTable),
                                    RePricerId = repricerId,
                                    Params = message,
                                };
                                _log.Error(irixErrorEntity, ex);

                                // If this was the last attempt, rethrow the exception
                                if (attempt == maxRetries - 1)
                                {
                                    throw;
                                }

                                // Wait before retrying
                                System.Threading.Thread.Sleep(retryDelayMilliseconds);
                            }
                            finally
                            {
                                if (connection != null && connection.State == ConnectionState.Open)
                                    connection.Close();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = Constant.ReservationPersistance, // Replace with your class name
                            MethodName = nameof(InsertReportinTable),
                        };
                        _log.Error(irixErrorEntity, ex);

                        // If this was the last attempt, rethrow the exception
                        if (attempt == maxRetries - 1)
                        {
                            throw;
                        }
                    }

                    attempt++;
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(InsertReportinTable),
                    RePricerId = repricerId
                };
                _log.Error(irixErrorEntity, ex);
            }
        }

        public void UpdateReservationMainMarkedCancel(int RepricerId)
        {
            try
            {
                //using SqlConnection connection = new SqlConnection(_connectionString);
                //connection.Open();

                //using SqlCommand command = new SqlCommand(Constant.UpdateReservationMainMarkedCancel, connection);
                //command.CommandType = CommandType.StoredProcedure;
                //command.CommandTimeout = _commandTimeout;

                //// Add parameters
                //command.Parameters.AddWithValue(Constant.RepricerId, RepricerId);

                //command.ExecuteNonQuery();
                //connection.Close();
                _log.ConsoleLog(RepricerId, "UpdateReservationMainMarkedCancel", "Method_Code_Commneted");
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = Constant.InsertReservationDataAsync,
                };
                _log.Error(irixErrorEntity, ex);
            }
        }

        public void InsertOrUpdateClientConfigurationExtraCriteria(int RepricerId,
        ExtraClientDetail extraClientDetail, OptimizationType OptimizationType)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    using (SqlCommand command = new SqlCommand(Constant.InserExtraCriteria, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        command.Parameters.AddWithValue("@RepricerUserID", RepricerId);
                        command.Parameters.AddWithValue("@priceDifferenceValue", extraClientDetail.PriceDifferenceValue);
                        command.Parameters.AddWithValue("@pricedifferencecurrency", extraClientDetail.Currency);
                        command.Parameters.AddWithValue("@priceDifferencePercentage", extraClientDetail.PriceDifferencePercentage);
                        command.Parameters.AddWithValue("@isUsePercentage", extraClientDetail.IsUsePercentage);
                        command.Parameters.AddWithValue("@TravelDaysMaxSearchInDays", extraClientDetail.TravelDaysMaxSearchInDays);
                        command.Parameters.AddWithValue("@TravelDaysMinSearchInDays", extraClientDetail.TravelDaysMinSearchInDays);
                        command.Parameters.AddWithValue("@MaxNumberOfTimesOptimization", extraClientDetail.MaxNumberOfTimesOptimization);
                        command.Parameters.AddWithValue("@ClientConfig_DaysDifferenceInPreBookCreation", extraClientDetail.ClientConfig_DaysDifferenceInPreBookCreation);
                        command.Parameters.AddWithValue("@DaysLimitCancellationPolicyEdgeCase", extraClientDetail.DaysLimitCancellationPolicyEdgeCase);
                        command.Parameters.AddWithValue("@IsUseDaysLimitCancellationPolicyEdgeCase", extraClientDetail.IsUseDaysLimitCancellationPolicyEdgeCase);
                        command.Parameters.AddWithValue("@OptimizationType", OptimizationType);

                        // Add new properties with backward compatibility - only if stored procedure supports them
                        try
                        {
                            command.Parameters.AddWithValue(Constant.PriceDifferenceValueAllowedBufferPercentage, extraClientDetail.PriceDifferenceValueAllowedBufferPercentage);
                            command.Parameters.AddWithValue(Constant.IsCheckCrossSupperBeforeOptimization, extraClientDetail.IsCheckCrossSupperBeforeOptimization);
                        }
                        catch (Exception paramEx)
                        {
                            // Log parameter addition failure but continue without new parameters for backward compatibility
                            var paramErrorEntity = new IrixErrorEntity
                            {
                                ClassName = Constant.ReservationPersistance,
                                MethodName = nameof(InsertOrUpdateClientConfigurationExtraCriteria) + "_NewParameters",
                                Params = $"RepricerId: {RepricerId}, NewParameters: PriceDifferenceValueAllowedBufferPercentage={extraClientDetail.PriceDifferenceValueAllowedBufferPercentage}, IsCheckCrossSupperBeforeOptimization={extraClientDetail.IsCheckCrossSupperBeforeOptimization}"
                            };
                            _log.Error(paramErrorEntity, paramEx);
                        }

                        connection.Open();

                        try
                        {
                            command.ExecuteNonQuery();
                        }
                        catch (SqlException sqlEx) when (sqlEx.Message.Contains("parameter") && (sqlEx.Message.Contains("PriceDifferenceValueAllowedBufferPercentage") || sqlEx.Message.Contains("IsCheckCrossSupperBeforeOptimization")))
                        {
                            // Remove new parameters and retry for backward compatibility
                            var parametersToRemove = command.Parameters.Cast<SqlParameter>()
                                .Where(p => p.ParameterName == Constant.PriceDifferenceValueAllowedBufferPercentage ||
                                           p.ParameterName == Constant.IsCheckCrossSupperBeforeOptimization)
                                .ToList();

                            foreach (var param in parametersToRemove)
                            {
                                command.Parameters.Remove(param);
                            }

                            // Log fallback execution
                            var fallbackErrorEntity = new IrixErrorEntity
                            {
                                ClassName = Constant.ReservationPersistance,
                                MethodName = nameof(InsertOrUpdateClientConfigurationExtraCriteria) + "_FallbackExecution",
                                Params = $"RepricerId: {RepricerId}, Reason: Stored procedure doesn't support new parameters yet"
                            };
                            _log.Error(fallbackErrorEntity, sqlEx);

                            // Retry without new parameters
                            command.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = nameof(InsertOrUpdateClientConfigurationExtraCriteria),
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
        }

        public void InsertRepricerJobLogging(int repricerId, System.DateTime startDate, DateTime startDateTime, DateTime endDateTime, string methodName)
        {
            SqlConnection connection = null;
            try
            {
                connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.InsertRepricerJobLogging, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                command.Parameters.AddWithValue("@RepricerId", repricerId);
                command.Parameters.AddWithValue("@StartDate", startDate);
                command.Parameters.AddWithValue("@StartDateTime", startDateTime);
                command.Parameters.AddWithValue("@EndDateTime", endDateTime);
                command.Parameters.AddWithValue("@MethodName", methodName);

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance, // Replace with your class name
                    MethodName = nameof(InsertRepricerJobLogging),
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            finally
            {
                if (connection != null && connection.State == ConnectionState.Open)
                    connection.Close();
            }
        }

        public void InsertConfiguration(int repricerId, ConfigurationResult config)
        {
            SqlConnection connection = null;
            try
            {
                connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.InsertRepricerConfiguration, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                command.Parameters.AddWithValue("@RepricerId", repricerId);
                command.Parameters.AddWithValue("@allowedProvidersForOptimization", config.AllowedProvidersForOptimization);
                command.Parameters.AddWithValue("@minimumSupplierReservationPrice", (object)config.MinimumSupplierReservationPrice ?? DBNull.Value);
                command.Parameters.AddWithValue("@minimumSupplierReservationPriceCurrency", (object)config.MinimumSupplierReservationPriceCurrency ?? DBNull.Value);
                command.Parameters.AddWithValue("@allowNonRefundableReservations", (object)config.AllowNonRefundableReservations ?? DBNull.Value);
                command.Parameters.AddWithValue("@daysBeforeCancellationPolicy", (object)config.DaysBeforeCancellationPolicy ?? DBNull.Value);
                command.Parameters.AddWithValue("@rejectPayNowReservations", (object)config.RejectPayNowReservations ?? DBNull.Value);
                command.Parameters.AddWithValue("@rejectOptimizedReservations", (object)config.RejectOptimizedReservations ?? DBNull.Value);
                command.Parameters.AddWithValue("@minimumOfferGainValue", (object)config.MinimumOfferGainValue ?? DBNull.Value);
                command.Parameters.AddWithValue("@minimumOfferGainCurrency", (object)config.MinimumOfferGainCurrency ?? DBNull.Value);
                command.Parameters.AddWithValue("@offerCancellationDeadlineRestriction", (object)config.OfferCancellationDeadlineRestriction ?? DBNull.Value);
                command.Parameters.AddWithValue("@clientLicense", (object)config.ClientLicense ?? DBNull.Value);

                command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance, // Replace with your class name
                    MethodName = nameof(InsertConfiguration),
                };
                _log.Error(irixErrorEntity, ex);
                throw;
            }
            finally
            {
                if (connection != null && connection.State == ConnectionState.Open)
                    connection.Close();
            }
        }

        public async Task<List<string>> GetAllowedProviders(int rePricerId)
        {
            var allowedproviders = new List<string>();
            var cacheKey = $"AllowedProviders_{rePricerId}";

            try
            {
                if (_memoryCache.TryGetValue(cacheKey, out List<string> cachedProviders))
                {
                    if (cachedProviders?.Any() == true)
                    {
                        var cachedReportCopy = SerializeDeSerializeHelper.DeSerialize<List<string>>(SerializeDeSerializeHelper.Serialize(cachedProviders));
                        if (cachedReportCopy != null && cachedReportCopy?.Count > 1)
                        {
                            return cachedReportCopy;
                        }
                    }
                }

                allowedproviders = RedisCacheHelper.Get<List<string>>(cacheKey);

                if (allowedproviders != null && allowedproviders?.Count > 1)
                {
                    _memoryCache.Set(cacheKey, allowedproviders, TimeSpan.FromHours(3));
                    return allowedproviders;
                }

                allowedproviders = new List<string>();

                using (SqlConnection connection = Connection())
                {
                    await connection.OpenAsync();

                    SqlParameter[] parameters =
                    {
                       new SqlParameter(Constant.RePricerId, SqlDbType.Int) { Value = rePricerId }
                    };

                    using (SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, Constant.GetAllowedProviders, parameters))
                    {
                        while (await reader.ReadAsync())
                        {
                            var provider = DbPropertyHelper.StringPropertyFromRow(reader, "Provider");
                            if (!string.IsNullOrEmpty(provider))
                            {
                                allowedproviders.Add(provider);
                            }
                        }
                    }
                }
                if (allowedproviders != null && allowedproviders.Count > 0)
                {
                    RedisCacheHelper.Set(cacheKey, allowedproviders, TimeSpan.FromHours(3));
                    _memoryCache.Set(cacheKey, allowedproviders, TimeSpan.FromHours(3));
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = Constant.GetAllowedProviders,
                };
                _log.Error(irixErrorEntity, ex);
            }

            return allowedproviders;
        }

        public void InsertMultiSupplierRooms(List<MultiSupplierRoom> multiSupplierRooms)
        {
            try
            {
                var watchPreBookReservation = Stopwatch.StartNew();
                Task.Run(() =>
                {
                    using (SqlConnection connection = new SqlConnection(_connectionString))
                    {
                        connection.Open();

                        foreach (var room in multiSupplierRooms)
                        {
                            try
                            {
                                using SqlCommand command = new SqlCommand(Constant.InsertMultiSupplierRoom, connection);
                                command.CommandType = CommandType.StoredProcedure;
                                command.CommandTimeout = _commandTimeout;

                                // Add parameters
                                command.Parameters.AddWithValue(Constant.RepricerId, room.RepricerId);
                                command.Parameters.AddWithValue(Constant.ReservationId, room.ReservationId);
                                command.Parameters.AddWithValue(Constant.OldSupplier, room.OldSupplier);
                                command.Parameters.AddWithValue(Constant.NewSupplier, room.NewSupplier);
                                command.Parameters.AddWithValue(Constant.RoomIndex, room.RoomIndex);
                                command.Parameters.AddWithValue(Constant.RoomName, room.RoomName);
                                command.Parameters.AddWithValue(Constant.RoomStatus, room.RoomStatus);
                                command.Parameters.AddWithValue(Constant.RoomBoard, room.RoomBoard);
                                command.Parameters.AddWithValue(Constant.RoomBoardBasis, room.RoomBoardBasis);
                                command.Parameters.AddWithValue(Constant.NonRefundable, room.NonRefundable);
                                command.Parameters.AddWithValue(Constant.RoomInfo, room.RoomInfo);
                                command.Parameters.AddWithValue(Constant.SellingValue, room.SellingValue);
                                command.Parameters.AddWithValue(Constant.SellingCurrency, room.SellingCurrency);
                                command.Parameters.AddWithValue(Constant.SupplierValue, room.SupplierValue);
                                command.Parameters.AddWithValue(Constant.SupplierCurrency, room.SupplierCurrency);
                                command.Parameters.AddWithValue(Constant.MarkupValue, room.MarkupValue);
                                command.Parameters.AddWithValue(Constant.MarkupCurrency, room.MarkupCurrency);
                                command.Parameters.AddWithValue(Constant.CommissionValue, room.CommissionValue);
                                command.Parameters.AddWithValue(Constant.CommissionCurrency, room.CommissionCurrency);
                                command.Parameters.AddWithValue(Constant.CancellationDate, room.CancellationDate);
                                command.Parameters.AddWithValue(Constant.CancellationChargeValue, room.CancellationChargeValue);
                                command.Parameters.AddWithValue(Constant.CancellationChargeCurrency, room.CancellationChargeCurrency);
                                command.Parameters.AddWithValue(Constant.UpdatedOn, DateTime.UtcNow);
                                command.Parameters.AddWithValue(Constant.PassengerCount, room.passengerCount);
                                command.Parameters.AddWithValue(Constant.ChildrenAges, room.ChildAges);
                                command.Parameters.AddWithValue(Constant.IsRoomNameMatched, room.isRoomMatched);

                                command.ExecuteNonQuery();
                            }
                            catch (Exception ex)
                            {
                                var irixErrorEntity = new IrixErrorEntity
                                {
                                    ClassName = Constant.ReservationPersistance,
                                    MethodName = nameof(InsertMultiSupplierRooms),
                                };
                                _log.Error(irixErrorEntity, ex);
                            }
                        }

                        connection.Close();
                    }
                });
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = nameof(InsertMultiSupplierRooms),
                };
                _log.Error(irixErrorEntity, ex);
                //throw;
            }
        }

        public async Task<List<MultiSupplierRoom>> GetMultiSupplierRoomsAsync(int repricerId, int reservationId = 0, int PageSize = 20, int PageNumber = 1, bool iscaching = true)
        {
            var key = $"GetMultiSupplierRooms_{reservationId}_{repricerId}_{PageSize}_{PageNumber}";

            var multiSupplierRooms = new List<MultiSupplierRoom>();
            if (RedisCacheHelper.KeyExists(key) && iscaching)
            {
                multiSupplierRooms = RedisCacheHelper.Get<List<MultiSupplierRoom>>(key);
            }
            else
            {
                try
                {
                    using SqlConnection connection = Connection();
                    await connection.OpenAsync();
                    SqlParameter[] parameters =
                    {
                      new SqlParameter("@ReservationId", SqlDbType.Int) { Value = reservationId },
                      new SqlParameter("@RepricerId", SqlDbType.Int) { Value = repricerId },
                      new SqlParameter("@PageSize", SqlDbType.Int) { Value = PageSize },
                      new SqlParameter("@PageNumber", SqlDbType.Int) { Value = PageNumber }
                    };

                    using SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, "usp_get_multiSupplierRooms", parameters);
                    while (reader.Read())
                    {
                        var multiSupplierRoom = new MultiSupplierRoom
                        {
                            MultiSupplierRoomId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "MultiSupplierRoomId"),
                            RepricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerId"),
                            ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                            OldSupplier = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "OldSupplier"),
                            NewSupplier = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "NewSupplier"),
                            RoomIndex = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "RoomIndex"),
                            RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "RoomName"),
                            RoomStatus = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "RoomStatus"),
                            RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "RoomBoard"),
                            RoomBoardBasis = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "RoomBoardBasis"),
                            NonRefundable = DbPropertyHelper.BoolDefaultPropertyFromRow(reader, "NonRefundable"),
                            RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "RoomInfo"),
                            SellingValue = DbPropertyHelper.DoubleNullablePropertyFromRow(reader, "SellingValue"),
                            SellingCurrency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "SellingCurrency"),
                            SupplierValue = DbPropertyHelper.DoubleNullablePropertyFromRow(reader, "SupplierValue"),
                            SupplierCurrency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "SupplierCurrency"),
                            MarkupValue = DbPropertyHelper.DoubleNullablePropertyFromRow(reader, "MarkupValue"),
                            MarkupCurrency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MarkupCurrency"),
                            CommissionValue = DbPropertyHelper.DoubleNullablePropertyFromRow(reader, "CommissionValue"),
                            CommissionCurrency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CommissionCurrency"),
                            CancellationDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CancellationDate"),
                            CancellationChargeValue = DbPropertyHelper.DoubleNullablePropertyFromRow(reader, "CancellationChargeValue"),
                            CancellationChargeCurrency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CancellationChargeCurrency"),
                            ChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ChildrenAges"),
                            passengerCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "PassengerCount"),
                            isRoomMatched = DbPropertyHelper.BoolDefaultPropertyFromRow(reader, "IsRoomNameMatched"),
                            Destinations = DbPropertyHelper.StringPropertyFromRow(reader, "Destinations"),
                            HotelName = DbPropertyHelper.StringPropertyFromRow(reader, "hotelname"),
                            ReservationCancellationDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reservationcancellationdate"),
                            ReservationCancellationChargeValue = DbPropertyHelper.DoubleNullablePropertyFromRow(reader, "ReservationCancellationCharge"),
                            ReservationCancellationChargeCurrency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCancellationCurrency"),
                            Checkin = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Checkin"),
                            Checkout = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Checkout"),
                            DistinctReservationCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "TotalReservationCount")
                        };
                        multiSupplierRooms.Add(multiSupplierRoom);
                    }
                    RedisCacheHelper.Set(key, multiSupplierRooms, TimeSpan.FromHours(6));
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = Constant.ReservationPersistance,
                        MethodName = nameof(GetMultiSupplierRoomsAsync),
                    };
                    _log.Error(irixErrorEntity, ex);
                    throw;
                }
            }

            return multiSupplierRooms;
        }

        public void InsertMultiSupplierRoomsMapping(MultiRoomMapping mapping)
        {
            try
            {
                var watchPreBookReservation = Stopwatch.StartNew();

                using SqlConnection connection = new SqlConnection(_connectionString);
                connection.Open();

                using SqlCommand command = new SqlCommand(Constant.InsertMultiSupplierRoomMapping, connection);
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = _commandTimeout;

                command.Parameters.AddWithValue(Constant.RepricerId, mapping.RepricerId);
                command.Parameters.AddWithValue(Constant.ReservationId, mapping.ReservationId);
                command.Parameters.AddWithValue(Constant.MultiRoomId, mapping.MultiRoomId);
                command.Parameters.AddWithValue(Constant.ReservationRoomId, mapping.ReservationRoomId);
                command.Parameters.AddWithValue(Constant.CreatedBy, mapping.CreatedBy ?? (object)DBNull.Value);
                command.Parameters.AddWithValue(Constant.UpdatedBy, mapping.UpdatedBy ?? (object)DBNull.Value);

                command.ExecuteNonQuery();

                connection.Close();
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = nameof(InsertMultiSupplierRooms),
                };
                _log.Error(irixErrorEntity, ex);
            }
        }

        public async Task<List<string>> GetDistinctCurrencyByRepricerId(int rePricerId)
        {
            var distinctCurrency = new List<string>();

            try
            {
                using SqlConnection connection = Connection();
                await connection.OpenAsync();

                SqlParameter[] parameters =
                {
                 new SqlParameter(Constant.RePricerId, SqlDbType.Int) { Value = rePricerId }
                };

                using SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, Constant.GetDistinctCurrency, parameters);
                while (await reader.ReadAsync())
                {
                    var currency = DbPropertyHelper.StringPropertyFromRow(reader, "Currency");
                    if (!string.IsNullOrEmpty(currency))
                    {
                        distinctCurrency.Add(currency);
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = nameof(GetDistinctCurrencyByRepricerId),
                };
                _log.Error(irixErrorEntity, ex);
            }

            return distinctCurrency;
        }

        public async Task<List<int>> GetReservationIds(int rePricerId)
        {
            var reservationids = new List<int>();

            try
            {
                using (SqlConnection connection = Connection())
                {
                    await connection.OpenAsync();

                    SqlParameter[] parameters =
                    {
                      new SqlParameter(Constant.RePricerId, SqlDbType.Int) { Value = rePricerId }
                    };

                    using SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, Constant.GetReservationId, parameters);
                    while (await reader.ReadAsync())
                    {
                        var reservationid = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId");

                        reservationids.Add(reservationid);
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = nameof(GetReservationIds),
                };
                _log.Error(irixErrorEntity, ex);
            }

            return reservationids;
        }

        public async Task UpdateReservationStatusAsync(List<ReservationResponse> reservations, int RepricerId)
        {
            var tasks = new List<Task>();

            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();  // Async opening of the connection

                    foreach (var reservation in reservations)
                    {
                        // Wait for the semaphore, which limits the concurrent calls to 2
                        await _semaphore.WaitAsync();

                        // Capture each reservation's update in a task and add it to the tasks list
                        var task = Task.Run(async () =>
                        {
                            try
                            {
                                using (SqlCommand command = new SqlCommand(Constant.UpdateStatus, connection))
                                {
                                    command.CommandType = CommandType.StoredProcedure;
                                    command.CommandTimeout = _commandTimeout;

                                    // Add parameters specific to usp_UpdateReservationStatusAndConnection
                                    command.Parameters.AddWithValue(Constant.RePricerId, RepricerId);
                                    command.Parameters.AddWithValue(Constant.ReservationId, reservation.id);
                                    command.Parameters.AddWithValue(Constant.ReservationStatus, reservation?.service?.status);
                                    command.Parameters.AddWithValue(Constant.ConnectionDescription, reservation?.connection?.description);
                                    command.Parameters.AddWithValue(Constant.ConnectionStatus, reservation?.connection?.status);
                                    command.Parameters.AddWithValue(Constant.LastActivityDate, reservation.lastActivity.ToString(constants.datetypeDB));

                                    await command.ExecuteNonQueryAsync();  // Execute asynchronously
                                }
                            }
                            catch (Exception ex)
                            {
                                var irixErrorEntity = new IrixErrorEntity
                                {
                                    ClassName = Constant.ReservationPersistance,
                                    MethodName = nameof(UpdateReservationStatusAsync),
                                };
                                _log.Error(irixErrorEntity, ex);
                            }
                            finally
                            {
                                // Always release the semaphore to allow the next task to execute
                                _semaphore.Release();
                            }
                        });

                        tasks.Add(task);
                    }

                    // Wait for all tasks to Complete
                    await Task.WhenAll(tasks);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = nameof(UpdateReservationStatusAsync),
                };
                _log.Error(irixErrorEntity, ex);
            }
        }

        public async Task<List<RoomMappingModel>> GetRoomMappingsAsync(int RepricerId, int reservationId, int reservationRoomId)
        {
            var roomMappings = new List<RoomMappingModel>();

            try
            {
                using (SqlConnection connection = Connection())
                {
                    await connection.OpenAsync();

                    SqlParameter[] parameters =
                    {
                      new SqlParameter("@ReservationId", SqlDbType.Int) { Value = reservationId },
                      new SqlParameter("@ReservationRoomId", SqlDbType.Int) { Value = reservationRoomId },
                      new SqlParameter("@RepricerId", SqlDbType.Int) { Value = RepricerId },
                    };

                    using (SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, "dbo.usp_get_roommapping", parameters))
                    {
                        while (await reader.ReadAsync())
                        {
                            var roomMapping = new RoomMappingModel
                            {
                                RepricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerId"),
                                ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                                ReservationRoomId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationRoomId"),
                                MultiRoomId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "MultiRoomId"),
                                ReservationRoomName = DbPropertyHelper.StringPropertyFromRow(reader, "ReservationRoomName"),
                                ReservationRoomBoard = DbPropertyHelper.StringPropertyFromRow(reader, "ReservationRoomBoard"),
                                ReservationRoomInfo = DbPropertyHelper.StringPropertyFromRow(reader, "ReservationRoomInfo"),
                                SearchRoomName = DbPropertyHelper.StringPropertyFromRow(reader, "SearchRoomName"),
                                SearchRoomBoard = DbPropertyHelper.StringPropertyFromRow(reader, "SearchRoomBoard"),
                                SearchRoomInfo = DbPropertyHelper.StringPropertyFromRow(reader, "SearchRoomInfo"),
                                SearchSupplier = DbPropertyHelper.StringPropertyFromRow(reader, "SearchSupplier"),
                                ReservationSupplier = DbPropertyHelper.StringPropertyFromRow(reader, "ReservationSupplier")
                            };

                            roomMappings.Add(roomMapping);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = "ReservationPersistence",  // Replace with your class name
                    MethodName = nameof(GetRoomMappingsAsync)
                };
                _log.Error(irixErrorEntity, ex);
            }

            return roomMappings;
        }

        public async Task DeactivateRoomMappingAsync(int reservationId, int reservationRoomId, int multiRoomId, string updatedBy)
        {
            try
            {
                using (SqlConnection connection = Connection())
                {
                    await connection.OpenAsync();

                    using (SqlCommand command = new SqlCommand("dbo.usp_deactivate_roommapping", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        command.Parameters.Add(new SqlParameter("@ReservationId", SqlDbType.Int) { Value = reservationId });
                        command.Parameters.Add(new SqlParameter("@ReservationRoomId", SqlDbType.Int) { Value = reservationRoomId });
                        command.Parameters.Add(new SqlParameter("@MultiRoomId", SqlDbType.Int) { Value = multiRoomId });
                        command.Parameters.Add(new SqlParameter("@UpdateBy", SqlDbType.VarChar, 100) { Value = updatedBy });

                        await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = "ReservationPersistence",
                    MethodName = nameof(DeactivateRoomMappingAsync)
                };
                _log.Error(irixErrorEntity, ex);

                throw;
            }
        }

        public void InsertOrUpdateMultiSupplierReservationRoom(MultiSupplierRoomList model)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("dbo.usp_ins_MultiSupplierReservationRoom", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        // Add parameters
                        command.Parameters.AddWithValue("@RepricerId", model.RepricerId);
                        command.Parameters.AddWithValue("@ReservationId", model.ReservationId);
                        command.Parameters.AddWithValue("@RoomName", model.RoomName ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@RoomBoard", model.RoomBoard ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@RoomInfo", model.RoomInfo ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@RoomCode", model.RoomCode ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Descriptions", model.Descriptions ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@FacilitiesDescription", model.FacilitiesDescription ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@RoomStatus", model.RoomStatus ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@RoomBoardBasis", model.RoomBoardBasis ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@IssueNetPrice", model.IssueNetPrice);
                        command.Parameters.AddWithValue("@IssueCurrency", model.IssueCurrency ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CancellationDate", (object)model.CancellationDate ?? DBNull.Value);
                        command.Parameters.AddWithValue("@CancellationCurrency", model.CancellationCurrency ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CancellationCharge", (object)model.CancellationCharge ?? DBNull.Value);
                        command.Parameters.AddWithValue("@RoomRateTags", model.RoomRateTags ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@SupplierName", model.SupplierName ?? (object)DBNull.Value);

                        SqlParameter imageListParam = new SqlParameter("@ImageList", SqlDbType.Structured)
                        {
                            TypeName = "ImageListType",
                            Value = CreateImageListTable(model.Images)
                        };
                        command.Parameters.Add(imageListParam);

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = "ReservationPersistence",
                    MethodName = nameof(InsertOrUpdateMultiSupplierReservationRoom)
                };
                _log.Error(irixErrorEntity, ex);

                throw;
            }
        }

        public void InsertOrUpdateMultiSupplierSearchRoom(MultiSupplierRoomList model)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("dbo.usp_ins_MultiSupplierSearchRoom", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        // Add parameters
                        command.Parameters.AddWithValue("@RepricerId", model.RepricerId);
                        command.Parameters.AddWithValue("@ReservationId", model.ReservationId);
                        command.Parameters.AddWithValue("@RoomName", model.RoomName ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@RoomBoard", model.RoomBoard ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@RoomInfo", model.RoomInfo ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@RoomCode", model.RoomCode ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Descriptions", model.Descriptions ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@FacilitiesDescription", model.FacilitiesDescription ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@RoomStatus", model.RoomStatus ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@RoomBoardBasis", model.RoomBoardBasis ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@IssueNetPrice", model.IssueNetPrice);
                        command.Parameters.AddWithValue("@IssueCurrency", model.IssueCurrency ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CancellationDate", (object)model.CancellationDate ?? DBNull.Value);
                        command.Parameters.AddWithValue("@CancellationCurrency", model.CancellationCurrency ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CancellationCharge", (object)model.CancellationCharge ?? DBNull.Value);
                        command.Parameters.AddWithValue("@RoomRateTags", model.RoomRateTags ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@SupplierName", model.SupplierName ?? (object)DBNull.Value);

                        SqlParameter imageListParam = new SqlParameter("@ImageList", SqlDbType.Structured)
                        {
                            TypeName = "ImageListType",
                            Value = CreateImageListTable(model.Images)
                        };
                        command.Parameters.Add(imageListParam);

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = "ReservationPersistence",
                    MethodName = nameof(InsertOrUpdateMultiSupplierSearchRoom)
                };
                _log.Error(irixErrorEntity, ex);

                throw;
            }
        }

        private DataTable CreateImageListTable(List<string?>? images)
        {
            DataTable table = new DataTable();
            table.Columns.Add("Image", typeof(string));

            if (images != null)
            {
                foreach (var image in images)
                {
                    table.Rows.Add(image);
                }
            }

            return table;
        }

        public MultiSupplierRoomDetailsResp GetMultiSupplierRoomDetailsResp(int repricerId, int reservationId = 0, int PageSize = 20, int PageNumber = 1, bool iscaching = true)
        {
            var reports = new MultiSupplierRoomDetailsResp();
            using (var connection = new SqlConnection(_connectionString))
            {
                connection.Open();
                using (var command = new SqlCommand("dbo.usp_get_MultiSupplierRoomDetailsByRepricerId", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.CommandTimeout = _commandTimeout;

                    command.Parameters.AddWithValue("@RepricerId", repricerId);
                    command.Parameters.AddWithValue("@ReservationId", reservationId);
                    command.Parameters.AddWithValue("@PageSize", PageSize);
                    command.Parameters.AddWithValue("@PageNumber", PageNumber);

                    using (var reader = command.ExecuteReader())
                    {
                        var masterData = new MasterData();
                        reports.search = masterData.GetMultiSupplierRoomDetails(reader);
                        if (reader.NextResult() && reader.Read())
                        {
                            reports.reservation = masterData.GetMultiSupplierRoomDetails(reader);
                        }
                        if (reader.NextResult() && reader.Read())
                        {
                            reports.distinctreservationcount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "distinctreservationcount");
                        }
                    }
                }
            }
            return reports;
        }

        /// <summary>
        /// Used by web hook to update status
        /// </summary>
        /// <param name="RepricerId"></param>
        /// <param name="ReservationId"></param>
        /// <param name="ReservationStatus"></param>
        public void UpdateReservationStatusById(int RepricerId, int ReservationId, string ReservationStatus)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    try
                    {
                        using (SqlCommand command = new SqlCommand(Constant.InsertWehbookReservationStatus, connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.CommandTimeout = _commandTimeout;

                            // Add parameters specific to the stored procedure
                            command.Parameters.AddWithValue(Constant.RePricerId, RepricerId);
                            command.Parameters.AddWithValue(Constant.ReservationId, ReservationId);
                            command.Parameters.AddWithValue(Constant.ReservationStatus, ReservationStatus);
                            command.Parameters.AddWithValue(Constant.CreateDate, DateTime.UtcNow);

                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = Constant.ReservationPersistance,
                            MethodName = nameof(UpdateReservationStatusById), // Updated method name
                            Params = SerializeDeSerializeHelper.Serialize(new
                            {
                                RepricerId,
                                ReservationId,
                                ReservationStatus
                            })
                        };
                        _log.Error(irixErrorEntity, ex);
                    }

                    connection.Close();
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = Constant.ReservationPersistance,
                    MethodName = nameof(UpdateReservationStatusById), // Updated method name
                };
                _log.Error(irixErrorEntity, ex);
            }
        }

        public async Task<List<RepricerJobLogging>> GetRepricerJobLoggingsAsync(int? repricerId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var repricerJobLoggings = new List<RepricerJobLogging>();

            try
            {
                using (SqlConnection connection = Connection())
                {
                    await connection.OpenAsync();

                    SqlParameter[] parameters =
                    {
                new SqlParameter("@RepricerId", SqlDbType.Int) { Value = (object)repricerId ?? DBNull.Value },
                new SqlParameter("@FromDate", SqlDbType.DateTime) { Value = (object)fromDate ?? DBNull.Value },
                new SqlParameter("@ToDate", SqlDbType.DateTime) { Value =  (object)toDate ?? DBNull.Value },
                    };

                    using (SqlDataReader reader = await ExecuteStoredProcedureAsync(connection, "dbo.usp_get_repricerjoblogging", parameters))
                    {
                        while (await reader.ReadAsync())
                        {
                            var repricerJobLogging = new RepricerJobLogging
                            {
                                RepricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerId"),
                                StartDate = DbPropertyHelper.DateTimePropertyFromRow(reader, "StartDate"),
                                StartDateTime = DbPropertyHelper.DateTimePropertyFromRow(reader, "StartDateTime"),
                                EndDateTime = DbPropertyHelper.DateTimePropertyFromRow(reader, "EndDateTime"),
                                MethodName = DbPropertyHelper.StringPropertyFromRow(reader, "MethodName"),
                            };

                            repricerJobLoggings.Add(repricerJobLogging);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = nameof(GetRepricerJobLoggingsAsync),  // Replace with your class name
                    MethodName = nameof(GetRepricerJobLoggingsAsync)
                };
                _log.Error(irixErrorEntity, ex);
            }

            return repricerJobLoggings;
        }

        public ChangeLogEntriesResp GetChangeLogEntries(int rePricerId, string columnName, DateTime? changeDateStart, DateTime? changeDateEnd = null, string changedBy = null)
        {
            try
            {
                var changeLogEntriesResp = new ChangeLogEntriesResp();
                using (var connection = new SqlConnection(_RePricerconnectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("dbo.usp_Get_ChangeLogEntries", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        command.Parameters.AddWithValue("@ColumnName", (object)columnName ?? DBNull.Value);
                        command.Parameters.AddWithValue("@ChangeDateStart", (object)changeDateStart ?? DBNull.Value);
                        command.Parameters.AddWithValue("@ChangeDateEnd", (object)changeDateEnd ?? DBNull.Value);
                        command.Parameters.AddWithValue("@ChangedBy", (object)changedBy ?? DBNull.Value);
                        command.Parameters.AddWithValue("@RePricerId", (object)rePricerId ?? DBNull.Value);

                        using (var reader = command.ExecuteReader())
                        {
                            var masterData = new MasterData();
                            changeLogEntriesResp.entries = masterData.GetChangeLogEntries(reader);
                        }
                    }
                }
                return changeLogEntriesResp;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public SupplierCountResponse GetSupplierCount(int repricerId, bool isCacheRefresh = false)
        {
            var cachekey = $"supplierCountResponse_{repricerId}_";
            try
            {
                var supplierCountResponse = new SupplierCountResponse();

                if (!isCacheRefresh && _memoryCache.TryGetValue(cachekey, out SupplierCountResponse cachedSupplierCount))
                {
                    var cachedSupplierCountCopy = SerializeDeSerializeHelper.DeSerialize<SupplierCountResponse>(SerializeDeSerializeHelper.Serialize(cachedSupplierCount));
                    return cachedSupplierCountCopy;
                }

                supplierCountResponse = RedisCacheHelper.Get<SupplierCountResponse>(cachekey);

                if (supplierCountResponse != null && supplierCountResponse?.SupplierCounts?.Count > 0 && !isCacheRefresh)
                {
                    _memoryCache.Set(cachekey, supplierCountResponse, DateTimeOffset.UtcNow.AddMinutes(30));
                    return supplierCountResponse;
                }
                supplierCountResponse = new SupplierCountResponse();
                using (var connection = new SqlConnection(_connectionString)) // Ensure _connectionString is defined
                {
                    connection.Open();
                    using (var command = new SqlCommand("dbo.usp_GetSupplierCountByRepricerId", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        // Adding parameters with handling for null values
                        if (repricerId > 0)
                        {
                            command.Parameters.AddWithValue("@RepricerID", repricerId);
                        }

                        using (var reader = command.ExecuteReader())
                        {
                            var masterData = new MasterData();
                            supplierCountResponse.SupplierCounts = masterData.GetSupplierCounts(reader);
                        }

                        var keyValuePairs = new ConcurrentDictionary<int, string>();

                        Parallel.ForEach(supplierCountResponse.SupplierCounts.Select(x => x.RepricerID).Distinct(), (repricerId) =>
                        {
                            try
                            {
                                var repricerDetails = _clientPersistance.LoadRePricerDetail(repricerId).GetAwaiter().GetResult();
                                keyValuePairs.TryAdd(repricerId, repricerDetails.RepricerUserName);
                            }
                            catch (Exception ex)
                            {
                                // Handle or log exceptions here
                            }
                        });

                        Parallel.ForEach(supplierCountResponse.SupplierCounts, (item) =>
                        {
                            try
                            {
                                if (keyValuePairs.TryGetValue(item.RepricerID, out var repricerName))
                                {
                                    item.RepricerName = repricerName;
                                }
                            }
                            catch (Exception ex)
                            {
                                item.RepricerName = string.Empty;
                            }
                        });
                    }
                }
                if (supplierCountResponse != null && supplierCountResponse?.SupplierCounts?.Count > 0)
                {
                    RedisCacheHelper.Set(cachekey, supplierCountResponse);
                    _memoryCache.Set(cachekey, supplierCountResponse, TimeSpan.FromMinutes(30));
                }
                return supplierCountResponse;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = nameof(ReservationPersistence),  // Replace with your class name
                    MethodName = nameof(GetSupplierCount)
                };
                _log.Error(irixErrorEntity, ex);
                return null; // Or handle the exception as required
            }
        }

        public MultiSupplierReport GetMultiSupplierPrebookSummary(int RepricerId = 0, DateTime? createdFromDate = null, DateTime? createdToDate = null, bool isCacheRefresh = false)
        {
            var cachekey = $"GetMultiSupplierReservationReport_{RepricerId}_";
            var result = new MultiSupplierReport();

            if (!isCacheRefresh && _memoryCache.TryGetValue(cachekey, out result))
            {
                var resultCopy = SerializeDeSerializeHelper.DeSerialize<MultiSupplierReport>(SerializeDeSerializeHelper.Serialize(result));

                return resultCopy;
            }

            result = RedisCacheHelper.Get<MultiSupplierReport>(cachekey);

            if ((result != null && result?.multiSupplierReports?.Count > 0) && !isCacheRefresh)
            {
                _memoryCache.Set(cachekey, result, DateTimeOffset.UtcNow.AddMinutes(30));
                return result;
            }
            result = new MultiSupplierReport();

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    using (var command = new SqlCommand("dbo.usp_GenerateMultiSupplierRepricerReport", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.CommandTimeout = _commandTimeout;

                        command.Parameters.AddWithValue("@CreatedFromDate", createdFromDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CreatedToDate", createdToDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@RepricerId", RepricerId);

                        using (var reader = command.ExecuteReader())
                        {
                            var summaries = new List<MultiSupplierReportSummary>();
                            while (reader.Read())
                            {
                                summaries.Add(new MultiSupplierReportSummary
                                {
                                    RePricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RePricerId"),
                                    ReportDate = DbPropertyHelper.DateTimePropertyFromRow(reader, "ReportaDate"),
                                    TotalRefundable = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "Total_Refundable"),
                                    FilteredOut = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "FilteredOut"),
                                    Failed = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "Failed"),
                                    Success = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "Success"),
                                    SameSupplier = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "Same_Supplier"),
                                    DifferentSupplierInView = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "Different_Supplier_InView"),
                                    ReportedInDashboard = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "Reported_InDashboard"),
                                    OutsideCriteria = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "Outside_Criteria"),
                                    OutsideCriteriaOther = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "Outside_Criteria_Other"),
                                });
                            }
                            result.multiSupplierReports = summaries;

                            if (reader.NextResult())
                            {
                                var failedLogs = new List<MultiSupplierFailedLogsModel>();
                                while (reader.Read())
                                {
                                    failedLogs.Add(new MultiSupplierFailedLogsModel
                                    {
                                        RePricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RePricerId"),
                                        ReasonMessage = reader.GetString(reader.GetOrdinal("ReasonMessage")),
                                        ReservationCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationCount")
                                    });
                                }
                                result.multiSupplierFailedLogs = failedLogs;
                            }
                        }
                    }
                }

                if (result?.multiSupplierReports?.Count > 0)
                {
                    RedisCacheHelper.Set(cachekey, result);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = nameof(ReservationPersistence),
                    MethodName = nameof(GetSupplierCount)
                };
                _log.Error(irixErrorEntity, ex);
                return null;
            }

            return result;
        }

        public void InsertHotelMaster(int RepricerId, List<HotelApi> hotelMasters)
        {
            DataTable hotelDataTable = ConvertToDataTable(RepricerId, hotelMasters);

            var parameter = new SqlParameter("@HotelMaster", SqlDbType.Structured)
            {
                TypeName = "dbo.HotelMaster",
                Value = hotelDataTable
            };

            using (SqlConnection conn = new SqlConnection(_connectionString))
            {
                try
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("dbo.usp_ins_hotelMaster", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.Add(parameter);

                        cmd.ExecuteNonQuery();
                    }
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = nameof(ReservationPersistence),  // Replace with your class name
                        MethodName = nameof(InsertHotelMaster)
                    };
                    _log.Error(irixErrorEntity, ex);
                }
            }
        }

        // Helper method to convert List of HotelMaster to DataTable

        private DataTable ConvertToDataTable(int RepricerId, List<HotelApi> hotelMasters)
        {
            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("RepricerId", typeof(int));
            dataTable.Columns.Add("HotelId", typeof(int));
            dataTable.Columns.Add("HotelName", typeof(string));
            dataTable.Columns.Add("createdate", typeof(string));

            foreach (var hotelMaster in hotelMasters)
            {
                dataTable.Rows.Add(RepricerId, hotelMaster.id, hotelMaster.name, DateTime.Now);
            }

            return dataTable;
        }

        public void InsertCountryMaster(int RepricerId, List<HotelCountry> countryMasters)
        {
            DataTable countryDataTable = ConvertToDataTable(RepricerId, countryMasters);

            var parameter = new SqlParameter("@CountryMaster", SqlDbType.Structured)
            {
                TypeName = "dbo.CountryMaster",  // Name of the user-defined table type in SQL Server
                Value = countryDataTable
            };

            using (SqlConnection conn = new SqlConnection(_connectionString))
            {
                try
                {
                    // Open the connection
                    conn.Open();

                    // Create the command
                    using (SqlCommand cmd = new SqlCommand("dbo.usp_ins_countryMaster", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add the parameter to the command
                        cmd.Parameters.Add(parameter);

                        // Execute the stored procedure
                        cmd.ExecuteNonQuery();
                    }
                }
                catch (Exception ex)
                {
                    // Handle exceptions
                    Console.WriteLine($"Error: {ex.Message}");
                }
            }
        }

        private DataTable ConvertToDataTable(int RepricerId, List<HotelCountry> countryMasters)
        {
            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("RespricerId", typeof(int));
            dataTable.Columns.Add("CountryId", typeof(int));
            dataTable.Columns.Add("CountryName", typeof(string));

            foreach (var countryMaster in countryMasters)
            {
                dataTable.Rows.Add(RepricerId, countryMaster.id, countryMaster.name);
            }

            return dataTable;
        }
    }
}