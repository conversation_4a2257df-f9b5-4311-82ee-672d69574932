﻿using Irix.Entities;
using Irix.Persistence.Contract;
using Irix.Service.Contract;
using Irix.ServiceAdapters;
using Logger.Contract;
using Microsoft.Extensions.Caching.Memory;
using Repricer.Cache;
using RePricer.Constants;
using RePricer.Util;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text.RegularExpressions;
using Constant = RePricer.Constants.PersistanceConstant;
using constants = RePricer.Constants.ServiceConstants;
using UtilCommonConstants = Repricer.Util.Constant;

namespace Irix.Service
{
    public class SupplierSearchService : ISupplierSearchService
    {
        /// <summary>
        /// Defines the _irixAdapter
        /// </summary>
        private readonly IIrixAdapter _irixAdapter;

        /// <summary>
        /// Defines the _reservationPersistence
        /// </summary>
        private readonly IReservationPersistence _reservationPersistance;

        /// <summary>
        /// Defines the _masterService
        /// </summary>
        private readonly IMasterService _masterService;

        /// <summary>
        /// Defines the _clientServices
        /// </summary>
        private readonly IClientServices _clientServices;

        /// <summary>
        /// Defines the _memoryCache
        /// </summary>
        private readonly IMemoryCache _memoryCache;

        /// <summary>
        /// Defines the _emailService
        /// </summary>
        private readonly IEmailService _emailService;

        /// <summary>
        /// Defines the _exchangeRateService
        /// </summary>
        private readonly IExchangeRateService _exchangeRateService;

        /// <summary>
        /// Defines the _log
        /// </summary>
        private readonly ILogger _log;

        /// <summary>
        /// Defines the _searchServiceHelper
        /// </summary>
        private readonly ISearchServicerHelper _searchserviceHelper;

        /// <summary>
        /// Defines the _optimizationService
        /// </summary>
        private readonly IOptimizationService _optimizationService;

        /// <summary>
        /// Defines the _clientPersistence
        /// </summary>
        private readonly IClientPersistance _clientPersistance;

        /// <summary>
        /// Defines the _dryRunOptimizationService
        /// </summary>
        private readonly IDryRunOptimizationService _dryRunOptimizationService;

        /// <summary>
        /// Defines the _optimizationHelper
        /// </summary>
        private readonly IOptimizationHelper _optimizationHelper;

        /// <summary>
        /// Defines the _configurationInAPI
        /// </summary>
        private readonly IConfigurationService _configuration;

        /// <summary>
        /// Defines the _searchHelper
        /// </summary>
        private readonly ISearchHelper _searchHelper;

        /// <summary>
        /// Defines the _giataService
        /// </summary>
        private readonly IGiataService _giataService;

        private readonly ILoggerMongoDb _loggerMongoDb;
        private readonly List<BoardMapping> _boardMappings;
        private readonly List<GiataMappingRoomDetails> _giataMappingRoomDetails;
        private readonly string _className = nameof(SupplierSearchService);
        private readonly object _lock_UpdateErrorAndReason = new object();
        private readonly object _consoleLock = new object();
        private readonly object _lock_prebookresult = new object();
        private bool _isSameSupplier = false;
        private bool _isMock = false;

        /// <summary>
        /// Initializes logInfoEntry new instance of the <see cref="SupplierSearchService"/> class.
        /// </summary>
        /// <param name="irixAdapter">The irixAdapter<see cref="IIrixAdapter"/></param>
        /// <param name="reservationPersistence">The reservationPersistence<see cref="IReservationPersistence"/></param>
        /// <param name="masterService">The masterService<see cref="IMasterService"/></param>
        /// <param name="log">The log<see cref="ILogger"/></param>
        /// <param name="clientService">The clientService<see cref="IClientServices"/></param>
        /// <param name="emailService">The emailService<see cref="IEmailService"/></param>
        /// <param name="exchangeRateService">The exchangeRateService<see cref="IExchangeRateService"/></param>
        /// <param name="memoryCache">The memoryCache<see cref="IMemoryCache"/></param>
        /// <param name="searchServicerHelper">The searchServicerHelper<see cref="ISearchServicerHelper"/></param>
        /// <param name="optimizationService">The optimizationService<see cref="IOptimizationService"/></param>
        /// <param name="clientPersistence">The clientPersistence<see cref="IClientPersistance"/></param>
        /// <param name="dryRunOptimizationService">The dryRunOptimizationService<see cref="IDryRunOptimizationService"/></param>
        /// <param name="optimizationHelper">The optimizationHelper<see cref="IOptimizationHelper"/></param>
        /// <param name="configurationService">The configurationService<see cref="IConfigurationService"/></param>
        /// <param name="searchHelper">The searchHelper<see cref="ISearchHelper"/></param>
        /// <param name="giataService">The giataService<see cref="IGiataService"/></param>
        public SupplierSearchService
        (
              IIrixAdapter irixAdapter
            , IReservationPersistence reservationPersistence
            , IMasterService masterService
            , ILogger log
            , IClientServices clientService
            , IEmailService emailService
            , IExchangeRateService exchangeRateService
            , IMemoryCache memoryCache
            , ISearchServicerHelper searchServicerHelper
            , IOptimizationService optimizationService
            , IClientPersistance clientPersistence
            , IDryRunOptimizationService dryRunOptimizationService
            , IOptimizationHelper optimizationHelper
            , IConfigurationService configurationService
            , ISearchHelper searchHelper
            , IGiataService giataService
            , ILoggerMongoDb loggerMongoDb
            )
        {
            _irixAdapter = irixAdapter;
            _reservationPersistance = reservationPersistence;
            _masterService = masterService;
            _log = log;
            _clientServices = clientService;
            _emailService = emailService;
            _exchangeRateService = exchangeRateService;
            _memoryCache = memoryCache;
            _searchserviceHelper = searchServicerHelper;
            _optimizationService = optimizationService;
            _clientPersistance = clientPersistence;
            _optimizationHelper = optimizationHelper;
            _dryRunOptimizationService = dryRunOptimizationService;
            _configuration = configurationService;
            _searchHelper = searchHelper;
            _giataService = giataService;
            _loggerMongoDb = loggerMongoDb;
            try
            {
                _boardMappings = new List<BoardMapping>();//_masterService.GetAllBoardMappingsAsync()?.GetAwaiter().GetResult();
                _giataMappingRoomDetails = new List<GiataMappingRoomDetails>();// _masterService.GetAllRoomDetailsAsync()?.GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = _className,
                    Params = $"{_className}, Constructor"
                };
                _log.Error(irixErrorEntity, ex);
            }
        }

        /// <summary>
        /// The _1_PrebookAndOptimize_MultiSupplier used in creation of prebook, optimize manually, and optimize automatically.
        /// (if automatic is allowed from repricer configuration for the client.)
        /// </summary>
        /// <param name="repricerId">The reservationId<see cref="int"/></param>
        /// <param name="reservationId">The ReservationId<see cref="int?"/></param>
        /// <param name="isUpdateDB">The isUpdateDB<see cref="bool"/></param>
        /// <param name="isMultiSupplier">The isMultiSupplier<see cref="bool"/></param>
        /// <returns>The <see cref="Task{PreBookResults}"/></returns>
        public async Task<PreBookResults> _1_PrebookAndOptimize_MultiSupplier(int repricerId, int? reservationId = 0, bool isUpdateDB = true, bool isMultiSupplier = true, string supplierName = null, bool isOptimizeTriggeredManually = false, int totalItems = 0, int currentItem = 0)
        {
            #region init

            List<int> reservationIds = new List<int>();
            List<int> reservationIdsInput = new List<int>();
            var _method = nameof(_1_PrebookAndOptimize_MultiSupplier);
            var itemJobNameMAIN = $"{repricerId.ToString("000")}_7_MULTI_SUPPLIER";
            var startTimeItemMAIN = DateTime.UtcNow;
            var preBookResponseResults = new ConcurrentBag<PreBookResponseResult>();
            var resturnResult = new PreBookResults();
            var repricerName = string.Empty;
            resturnResult.PreBookResponseResults = new List<PreBookResponseResult>();
            bool isAnyOptimized = false;
            bool isCurrentPrebookOptimized = false;
            var method = $"_7_MULTI_SUPPLIER";
            isMultiSupplier = true;
            var repricerClientDetail = _clientServices.GetRePricerDetail(repricerId);

            #endregion init

            try
            {
                Guid newGuidmain = Guid.NewGuid();
                string steptoken = newGuidmain.ToString();
                List<ReservationPrebookCount> reservationids_maxcount = null;

                #region Get Dashboard Items

                var reservationRequestReport = new RepricerReportRequest
                {
                    RepricerId = repricerId,
                    ReportType = "Prebook"
                };
                if (reservationId > 0)
                {
                    reservationRequestReport = new RepricerReportRequest
                    {
                        RepricerId = repricerId,
                        ReservationId = reservationId.ToString()
                    };
                }
                List<DashboardReportResponseRow> activeTabDashboard = new List<DashboardReportResponseRow>();

                if (isOptimizeTriggeredManually)
                {
                    activeTabDashboard = _masterService.GetReservationReport(reservationRequestReport)?.Data;
                }

                #endregion Get Dashboard Items

                #region Load initial data required for processing

                var watchCriteria = Stopwatch.StartNew();

                var repricerSchedules = _clientServices.GetClientScheduler()?.FirstOrDefault(x => x.RepricerId == repricerId);
                var extraClientConfig = _clientServices.GetClientEmail(repricerId);
                repricerName = repricerSchedules.RepricerName;

                if (reservationId == 0)
                {
                    _log.InfoV1(repricerId, _className, itemJobNameMAIN, repricerName, startTimeItemMAIN, true, "start");
                }
                if ((repricerSchedules?.IsMultiSupplierEnabled ?? false) == false
                    || (repricerSchedules?.IsJobsEnable ?? false) == false
                    || (repricerSchedules?.IsActive ?? false) == false
                    || (string.IsNullOrWhiteSpace(supplierName) && isOptimizeTriggeredManually)
                )
                {
                    var preBookResponseResult = new PreBookResponseResult
                    {
                        RepricerId = repricerId,
                        ReservationId = reservationId ?? 0,
                        Repricername = extraClientConfig?.RepricerUserName
                    };

                    preBookResponseResult = UpdateErrorAndReason(repricerId, 0, preBookResponseResult
                                                                                , OptimizationStatusEnum.MultiSupplierNotSETforTheClient
                                                                                , ReasonCode.MultiSupplierNotSETforTheClient);
                    preBookResponseResults.Add(preBookResponseResult);
                    resturnResult.PreBookResponseResults = preBookResponseResults.ToList();
                    return resturnResult;
                }

                var searchCriterias = await _reservationPersistance.GetReservationsAsync(repricerId, reservationId ?? 0);

                if (searchCriterias?.Any() == false)
                {
                    var preBookResponseResult = new PreBookResponseResult
                    {
                        RepricerId = repricerId,
                        ReservationId = reservationId ?? 0,
                        Repricername = extraClientConfig?.RepricerUserName
                    };

                    preBookResponseResult = UpdateErrorAndReason(repricerId, 0, preBookResponseResult
                                                                                , OptimizationStatusEnum.NoMatchingOffers
                                                                                , ReasonCode.NoOfferFound);
                    preBookResponseResults.Add(preBookResponseResult);
                    resturnResult.PreBookResponseResults = preBookResponseResults.ToList();
                    return resturnResult;
                }

                var roomReservations = await _reservationPersistance.GetReservationsRoomAsync(repricerId);
                var reservationPrebookCounts = await _reservationPersistance.GetPrebookReservationIdsAsync(repricerId);
                var preBookCriteriasAll = await _reservationPersistance.GetPreBookCriteriaDBAll(repricerId, isMultiSupplier);
                if (searchCriterias != null && searchCriterias.Count > 0)
                {
                    var currentDate = DateTime.UtcNow.Date;
                    //searchCriterias = searchCriterias.Where
                    //(
                    //    x =>
                    //        x.CancellationDate.Date > currentDate
                    //).ToList();
                }
                if (searchCriterias == null || searchCriterias.Count == 0)
                {
                    var preBookResponseResult = new PreBookResponseResult
                    {
                        RepricerId = repricerId,
                        ReservationId = reservationId ?? 0,
                        Repricername = extraClientConfig?.RepricerUserName
                    };

                    preBookResponseResult = UpdateErrorAndReason(repricerId, 0, preBookResponseResult
                                                                                , OptimizationStatusEnum.MultiSupplierNotSETforTheClient
                                                                                , ReasonCode.MultiSupplierNotSETforTheClient);
                    preBookResponseResults.Add(preBookResponseResult);
                    resturnResult.PreBookResponseResults = preBookResponseResults.ToList();
                    return resturnResult;
                }

                var cancellationPolicies = await _reservationPersistance.GetCancellationPolicyReservationIdsAsync(repricerId);

                var allowedProviders = await _reservationPersistance.GetAllowedProviders(repricerId);

                var searchCriteriasCountB4SupplierCheck = searchCriterias?.Count ?? 0;
                searchCriterias = searchCriterias?
                                        .Where(searchcriteria => allowedProviders
                                        .Any(provider => provider.Equals(searchcriteria.supplierName, StringComparison.OrdinalIgnoreCase)))
                                        .ToList();
                var searchCriteriasCountAfterSupplierCheck = searchCriterias?.Count ?? 0;
                if (searchCriteriasCountB4SupplierCheck > 0 && searchCriteriasCountAfterSupplierCheck == 0)
                {
                    var reservationIdint = reservationId ?? 0;
                    var itemAttemptResult = new PreBookResponseResult
                    {
                        RepricerId = repricerId,
                        ReservationId = reservationIdint,
                        Repricername = extraClientConfig?.RepricerUserName
                    };
                    itemAttemptResult = UpdateErrorAndReason
                                   (
                                           repricerId
                                           , reservationIdint
                                           , itemAttemptResult
                                           , OptimizationStatusEnum.SupplierNotAllowed
                                           , ReasonCode.Repricer_SupplierNotAllowed
                                   );
                    if (!preBookResponseResults.Any(x => x.ReservationId == reservationIdint))
                    {
                        preBookResponseResults.Add(itemAttemptResult);
                    }
                }

                #endregion Load initial data required for processing

                #region

                #region Criteria Filter  based on inputs

                if (reservationId > 0)
                {
                    searchCriterias = searchCriterias?.Where(x => x.ReservationId == reservationId).ToList();
                    roomReservations = roomReservations?.Where(x => x.ReservationId == reservationId).ToList();
                    reservationPrebookCounts = reservationPrebookCounts?.Where(x => x.ReservationId == reservationId).ToList();
                    cancellationPolicies = cancellationPolicies?.Where(x => x.ReservationId == reservationId).ToList();
                }
                else if (reservationIdsInput.Count > 0)
                {
                    var searchCriteriasQ = from items in searchCriterias
                                           from input in reservationIdsInput
                                           where items.ReservationId == input
                                           select items;

                    var roomReservationsQ = from items in roomReservations
                                            from input in reservationIdsInput
                                            where items.ReservationId == input
                                            select items;

                    var reservationPrebookCountsQ = from items in reservationPrebookCounts
                                                    from input in reservationIdsInput
                                                    where items.ReservationId == input
                                                    select items;

                    var cancellationPoliciesQ = from items in cancellationPolicies
                                                from input in reservationIdsInput
                                                where items.ReservationId == input
                                                select items;

                    searchCriterias = searchCriteriasQ.ToList();
                    roomReservations = roomReservationsQ.ToList();
                    reservationPrebookCounts = reservationPrebookCountsQ.ToList();
                    cancellationPolicies = cancellationPoliciesQ.ToList();
                }

                if (searchCriterias != null && reservationPrebookCounts != null && extraClientConfig != null)
                {
                    reservationids_maxcount = reservationPrebookCounts
                               .GroupBy(r => r.ReservationId)
                               .Select(group => new ReservationPrebookCount
                               {
                                   ReservationId = group.Key,
                                   PreBookCount = group.Max(r => r.PreBookCount),
                                   CreateDate = group.FirstOrDefault(r => r.PreBookCount == group.Max(p => p.PreBookCount))?.CreateDate ?? DateTime.MinValue
                               })
                               .ToList();
                }

                int traveldaysmaxsearchindays = extraClientConfig?.traveldaysmaxsearchindays ?? 60;
                int traveldaysminsearchindays = extraClientConfig?.traveldaysminsearchindays ?? 10;

                var filteredReservations = searchCriterias?.OrderBy(x => x.ReservationId).ToList();

                #endregion Criteria Filter  based on inputs

                #region Custom StartFrom or skip Up to Id in case run process from in between

                if (filteredReservations?.Count > 1)
                {
                    /* SkipFrom if needed
                    if (repricerId == 8)
                    {
                        var startFromReservationID = 388459;
                        filteredReservations = filteredReservations?.Where(x => x.ReservationId >= startFromReservationID)?.ToList();
                        searchCriterias = filteredReservations?.Where(x => x.ReservationId >= startFromReservationID)?.ToList();
                    }

                    //*/
                }

                #endregion Custom StartFrom or skip Up to Id in case run process from in between

                watchCriteria.Stop();
                var elapsedTimeInSeconds = watchCriteria.Elapsed.TotalSeconds;
                if (isUpdateDB)
                {
                    LoggerPersistance.SearchSyncLogging(Constant.SearchSync, 0, 0, steptoken, elapsedTimeInSeconds, "ReservationFilter", repricerId);
                }

                cancellationPolicies = cancellationPolicies
                    ?.Where(cancellation =>
                        filteredReservations?.Any(reservation => reservation.ReservationId == cancellation.ReservationId)
                        == true
                        )
                    ?.ToList();

                #endregion Preparation

                #region Main Processing

                filteredReservations = filteredReservations?.OrderByDescending(x => x.ReservationId)?.ToList();
                filteredReservations = SerializeDeSerializeHelper.DeSerialize<List<ReservationMainModel>>(SerializeDeSerializeHelper.Serialize(filteredReservations));

                if (filteredReservations != null && filteredReservations.Count > 0)
                {
                    var counter = currentItem > 0 ? currentItem : 1;

                    var parallelOptions = UtilCommonConstants.GetParallelOptions(50);
                    if (reservationId > 0)
                    {
                        parallelOptions = new ParallelOptions { MaxDegreeOfParallelism = 1 };
                    }

                    var counterTotal = totalItems > 0 ? totalItems : filteredReservations.Count();
                    var startTime = DateTime.UtcNow;

                    await Parallel.ForEachAsync(filteredReservations, parallelOptions, async (reservation, CancellationToken) =>
                    {
                        var cancellationTokenSource = new CancellationTokenSource();
                        var cancellationToken = cancellationTokenSource.Token;
                        var reservationIdInLoop = reservation.ReservationId;

                        var itemAttemptResult = new PreBookResponseResult
                        {
                            RepricerId = repricerId,
                            ReservationId = reservationIdInLoop,
                            Repricername = extraClientConfig?.RepricerUserName
                        };
                        var offersFound = 0;
                        var offerInfoList = default(List<OfferInfo>);
                        bool isOptimized = false;
                        bool isPrebookCreated = false;
                        try
                        {
                            preBookResponseResults.Add(itemAttemptResult);
                        }
                        catch (Exception ex)
                        {
                        }

                        #region Request wise processing

                        var task = Task.Run(async () =>
                        {
                            SearchResponseFromAPI searchResponseIRIX = null;
                            try
                            {
                                // Checking if optimization is already running
                                var IsOptimizationAlreadyRunningForReservation = _searchserviceHelper.IsOptimizationAlreadyRunning(repricerId, reservationIdInLoop, _isSameSupplier, isRemove: false, isCheckAndSet: false);
                                var waitCounter = 0;

                                var cancellationpolicybyreservationid = _searchserviceHelper.GetMissingCancellationPolicyReservations(repricerId, reservationIdInLoop, cancellationPolicies, reservation);

                                if (cancellationpolicybyreservationid?.Any() == false)
                                {
                                    itemAttemptResult = UpdateErrorAndReason
                                    (
                                            repricerId
                                            , reservationIdInLoop
                                            , itemAttemptResult
                                            , OptimizationStatusEnum.Repricer_ValidCancellationPolicyNotFound
                                            , ReasonCode.Repricer_NoNewOfferOrMatchedCancellationPolicy
                                    );
                                    //_log.ConsoleLog(repricerId, _method, OptimizationStatusEnum.Repricer_ValidCancellationPolicyNotFound.ToString(), reservationIdInLoop.ToString(), itemAttemptResult);

                                    return;
                                }

                                while (IsOptimizationAlreadyRunningForReservation)
                                {
                                    itemAttemptResult = UpdateErrorAndReason
                                    (
                                            repricerId
                                            , reservationIdInLoop
                                            , itemAttemptResult
                                            , OptimizationStatusEnum.Repricer_AlreadyQueueRunning
                                            , ReasonCode.Repricer_AlreadyQueueRunning
                                            , "An optimization attempt is already under processing. Please wait for 5 minutes before attempting again!"
                                    );
                                    return;
                                    //waitCounter++;
                                    //await Task.Delay(TimeSpan.FromSeconds(10));

                                    //IsOptimizationAlreadyRunningForReservation = _searchServiceHelper.IsOptimizationAlreadyRunning(repricerId, reservationIdInLoop, false, false);
                                    //if (waitCounter > 1 && IsOptimizationAlreadyRunningForReservation)
                                    //{
                                    //    return; // Exit if optimization is still running after several retries
                                    //}
                                }

                                #region Logging start of processing

                                try
                                {
                                    /*
                                    counter++;
                                    startTime = DateTime.UtcNow;

                                    var logInfoEntry = new
                                    {
                                        RepricerId = repricerId,
                                        Counter = $"{counter}\\{counterTotal}) START",
                                        Minute = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2"),
                                        OffersFound = offersFound,
                                        reservation?.ReservationId,
                                        isPrebookCreated,
                                        isOptimized,
                                        Method = "GiataPrebookProcessing",
                                        StartTime = startTime,
                                        EndTime = DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss"),
                                        EndTimeUTC = DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss")
                                    };

                                    var msg = $"{SerializeDeSerializeHelper.Serialize(logInfoEntry)}";
                                    var irixErrorEntity = new IrixErrorEntity
                                    {
                                        ClassName = _className,
                                        MethodName = "MultiSupplier",
                                        RePricerId = repricerId,
                                        ReservationId = Convert.ToInt32(reservationId),
                                        Params = msg
                                    };
                                    _log.Info(msg, irixErrorEntity, true);
                                    **/
                                }
                                catch (Exception ex)
                                {
                                }

                                #endregion Logging start of processing

                                OptimizationStatusResponse optimizationStatus = null;
                                DryRunResponse dryRunResponse = null;
                                //check only if automatic or demo
                                if (repricerClientDetail?.IsOptimizationAllowed == true)
                                {
                                    if (_isMock)
                                    {
                                        optimizationStatus = new OptimizationStatusResponse
                                        {
                                            IsOptimizable = true
                                        };
                                    }
                                    else
                                    {
                                        optimizationStatus = _optimizationHelper.IsOptimizationStatus(repricerId, reservationIdInLoop, isOptimizeTriggeredManually, isOptimizeTriggeredManually, isMultiSupplier: isMultiSupplier);
                                        itemAttemptResult.OptimizableStatus = new OptimizationOptimizationBooking
                                        {
                                            Status = optimizationStatus?.Optimization?.Status ?? string.Empty
                                        };
                                        isOptimized = isAnyOptimized = itemAttemptResult.IsOptimized = optimizationStatus?.Optimization?.Status == "optimized";
                                    }
                                }
                                if (optimizationStatus?.IsOptimizable == false)
                                {
                                    var statusAPIMessage = optimizationStatus?.OptimizationRestrictions?.FirstOrDefault().Value ?? string.Empty;

                                    itemAttemptResult = UpdateErrorAndReason
                                                            (
                                                                    repricerId
                                                                    , reservationIdInLoop
                                                                    , itemAttemptResult
                                                                    , OptimizationStatusEnum.Repricer_OptimizationNotAllowed
                                                                    , ReasonCode.OptimizationNotAllowed
                                                                    , statusAPIMessage
                                    );
                                    bool isAlreadyoptimized = (optimizationStatus?.OptimizedBy > 0) == true;

                                    _log.UpdateOptimizationToQueue(OptimizationStatusEnum.Repricer_OptimizationNotAllowed.ToString(), reservationIdInLoop, repricerId, false, true, isAlreadyoptimized);
                                }

                                if (IsOptimizationAlreadyRunningForReservation == false && (extraClientConfig?.OptimizationType == OptimizationType.Demo
                                    || (repricerClientDetail.IsOptimizationAllowed && optimizationStatus?.IsOptimizable == true))
                                )
                                {
                                    _searchserviceHelper.IsOptimizationAlreadyRunning(repricerId, reservationIdInLoop, _isSameSupplier, isRemove: false, isCheckAndSet: true);
                                    var prebookrequest = new PackageRequest();
                                    var isReservationSupplierAllowed = allowedProviders.Contains(reservation.supplierName);
                                    var suppliersWithoutReservationSupplier = allowedProviders.Where(x => x.ToLower() != reservation.supplierName.ToLower()).ToList();
                                    var supplierCounter = 0;
                                    var supplierCounterTotal = suppliersWithoutReservationSupplier.Count;
                                    int batchSize = 5;
                                    var batches = Batch(suppliersWithoutReservationSupplier, batchSize);

                                    if (!string.IsNullOrWhiteSpace(supplierName) && isOptimizeTriggeredManually)
                                    {
                                        allowedProviders.Clear();
                                        allowedProviders.Add(supplierName);
                                    }

                                    supplierCounterTotal = allowedProviders.Count;
                                    var startTimeSupplierMain = DateTime.UtcNow;

                                    //##TODO Completed (Provider loop / Supplier Loop)
                                    foreach (var batch in batches)
                                    {
                                        var allowedSupplierName = string.Join(", ", batch);
                                        var startTimeSupplierItem = DateTime.UtcNow;
                                        isPrebookCreated = false;

                                        //if (reservation?.supplierName?.Trim()?.ToLower() != allowedSupplier.Trim()?.ToLower())
                                        {
                                            #region Supplier wise processing

                                            try
                                            {
                                                Guid newGuidSerach = Guid.NewGuid();
                                                string stepSearchtoken = newGuidSerach.ToString();

                                                int prebookcount = 0;
                                                var maxprebookcount = reservationids_maxcount?.FirstOrDefault(r => r.ReservationId == reservationIdInLoop);

                                                if (maxprebookcount != null)
                                                {
                                                    prebookcount = maxprebookcount.PreBookCount;
                                                }
                                                var watchCreateCriteria = Stopwatch.StartNew();

                                                if (isReservationSupplierAllowed)
                                                {
                                                    if (!string.IsNullOrEmpty(supplierName) && isOptimizeTriggeredManually)
                                                    {
                                                        searchResponseIRIX = _searchHelper.SearchSync(repricerId, reservation, isMultipleRoomSearch: true, isMultiPrebook: true, requestedSupplierName: supplierName, lookupSupplierBatch: null);
                                                    }
                                                    else
                                                    {
                                                        searchResponseIRIX = _searchHelper.SearchSync(repricerId, reservation, isMultipleRoomSearch: true, isMultiPrebook: true, requestedSupplierName: null, lookupSupplierBatch: batch);
                                                    }

                                                    var reservationsupplier = reservation.supplierName;
                                                    if (searchResponseIRIX == null)
                                                    {
                                                        itemAttemptResult = UpdateErrorAndReason(repricerId,
                                                                reservationIdInLoop,
                                                                itemAttemptResult,
                                                                OptimizationStatusEnum.Repricer_SearchSyncFailed,
                                                                ReasonCode.NoOfferFound);
                                                    }
                                                    if (searchResponseIRIX != null)
                                                    {
                                                        if (isUpdateDB)
                                                        {
                                                            _searchserviceHelper.InsertSearchSyncData(searchResponseIRIX.hotels, repricerId, reservationIdInLoop, extraClientConfig?.IsSearchSyncDataSave ?? false)?.GetAwaiter().GetResult();
                                                        }
                                                        var prebookcriteriaresult = _reservationPersistance.GetPreBookCriteria(reservationIdInLoop, repricerId, true)?.GetAwaiter().GetResult();
                                                        var prebookcriteria = prebookcriteriaresult?.PreBookCriteriaList;

                                                        var fisrtPrebookCriteria = prebookcriteria?.FirstOrDefault();

                                                        if (fisrtPrebookCriteria != null && string.IsNullOrEmpty(fisrtPrebookCriteria?.Currency))
                                                        {
                                                            fisrtPrebookCriteria.Currency = reservation?.CancellationPoliciesBySource?.FirstOrDefault()?.Currency;
                                                        }
                                                        if (fisrtPrebookCriteria == null || string.IsNullOrEmpty(fisrtPrebookCriteria?.Currency))
                                                        {
                                                            itemAttemptResult = UpdateErrorAndReason
                                                            (
                                                                    repricerId
                                                                    , reservationIdInLoop
                                                                    , itemAttemptResult
                                                                    , OptimizationStatusEnum.InvalidPrebookCriteria
                                                                    , ReasonCode.InvalidPrebookCriteria
                                                            );
                                                            return;
                                                        }

                                                        var reservationCriteriaJson = reservation?.CriteriaJson;

                                                        var hotelName = fisrtPrebookCriteria?.HotelName;
                                                        var Destinations = fisrtPrebookCriteria?.Destinations;

                                                        #region Insertion in MongoDb for Giata

                                                        var PropertyName = $"{hotelName}";

                                                        if (!string.IsNullOrWhiteSpace(Destinations))
                                                        {
                                                            PropertyName = $"{PropertyName},{Destinations}";
                                                        }

                                                        //if (!string.IsNullOrWhiteSpace(PropertyName))
                                                        //{
                                                        //    SaveRoomMappingRequestToMongo(searchResponseIRIX, reservationIdInLoop, repricerId, PropertyName, prebookcriteria);
                                                        //}

                                                        await FilterOffers(searchResponseIRIX, reservation.supplierName);

                                                        #endregion Insertion in MongoDb for Giata

                                                        if (prebookcriteria != null && prebookcriteria.Count() > 0)
                                                        {
                                                            foreach (var prebookcriteriaroom in prebookcriteria)
                                                            {
                                                                GiataRoomMapping giatadatafororiginalreservation = null;
                                                                giatadatafororiginalreservation = _giataService.GiataApiCall(repricerId, reservation.ReservationId.ToString(), hotelName, prebookcriteriaroom.RoomName, Destinations, reservationsupplier).GetAwaiter().GetResult();
                                                                prebookcriteriaroom.giataroommappingdetail = giatadatafororiginalreservation;
                                                                prebookcriteriaroom.RoomBoardGroupName = _masterService.GetBoardGroupName(prebookcriteriaroom.RoomBoard);
                                                                if (giatadatafororiginalreservation == null)
                                                                {
                                                                    itemAttemptResult = UpdateErrorAndReason(repricerId, reservationIdInLoop, itemAttemptResult
                                                                                            , OptimizationStatusEnum.GiataMappingNotFound
                                                                                            , ReasonCode.GiataReservationMappingNotFound);

                                                                    if (!string.IsNullOrWhiteSpace(PropertyName))
                                                                    {
                                                                        SaveRoomMappingRequestToMongo(searchResponseIRIX, reservationIdInLoop, repricerId, PropertyName, prebookcriteria);
                                                                    }
                                                                    break;
                                                                }
                                                                if (string.IsNullOrWhiteSpace(prebookcriteriaroom?.RoomBoardGroupName))
                                                                {
                                                                    itemAttemptResult = UpdateErrorAndReason(repricerId, reservationIdInLoop, itemAttemptResult
                                                                                            , OptimizationStatusEnum.RoomBoardMappingNotAvailable
                                                                                            , ReasonCode.RoomBoardMappingNotAvailable);
                                                                    break;
                                                                }
                                                            }
                                                        }

                                                        if (prebookcriteria != null && prebookcriteria.Count() > 0 && prebookcriteria.All(x => x.giataroommappingdetail != null && !string.IsNullOrEmpty(x?.giataroommappingdetail?.GroupName ?? string.Empty)))
                                                        {
                                                            isOptimized = isAnyOptimized = itemAttemptResult.IsOptimized = optimizationStatus?.Optimization?.Status == "optimized";

                                                            var isRoomFound = false;
                                                            List<BoardMatchEnum> roomBoardMatchEnums = new List<BoardMatchEnum>();
                                                            //                                            {
                                                            //                                                BoardMatchEnum.BoardMatch_GROUP,
                                                            //                                                BoardMatchEnum.BoardMatch_NO,
                                                            //                                            };
                                                            //if (isOptimizeTriggeredManually && !string.IsNullOrWhiteSpace(allowedSupplierName))
                                                            //{
                                                            roomBoardMatchEnums = new List<BoardMatchEnum> {
                                                                                BoardMatchEnum.BoardMatch_GROUP,
                                                                                            };
                                                            //}
                                                            List<OfferInfo> ProcesssedOfferInfo = new List<OfferInfo>();
                                                            foreach (var roomBoardMatchEnum in roomBoardMatchEnums)
                                                            {
                                                                try
                                                                {
                                                                    if (isPrebookCreated == false)
                                                                    {
                                                                        var offerSearchStartTime = DateTime.UtcNow;
                                                                        offerInfoList = _2_GetOfferInfoListAsync(searchResponseIRIX, prebookcriteria, repricerId, reservationIdInLoop, roomBoardMatchEnum, activeTabDashboard, isOptimizeTriggeredManually)?.GetAwaiter().GetResult();
                                                                        offersFound = offerInfoList?.Count ?? 0;
                                                                        var offerSearchElaspedTime = (DateTime.UtcNow - offerSearchStartTime).TotalSeconds;
                                                                        offerInfoList = offerInfoList?.Where(offerInfo => !ProcesssedOfferInfo.Contains(offerInfo))?.ToList();

                                                                        if (offerInfoList == null || offerInfoList.Count == 0)
                                                                        {
                                                                            itemAttemptResult = UpdateErrorAndReason(repricerId, reservationIdInLoop, itemAttemptResult
                                                                                                        , OptimizationStatusEnum.NoMatchingOffers
                                                                                                        , ReasonCode.NoOfferFound);
                                                                        }
                                                                        else
                                                                        {
                                                                            if (isOptimized == false && offerInfoList != null)//&& offerInfoList.Count() > 0)
                                                                            {
                                                                                foreach (var offerInfo in offerInfoList)
                                                                                {
                                                                                    try
                                                                                    {
                                                                                        if (!ProcesssedOfferInfo.Contains(offerInfo))
                                                                                        {
                                                                                            ProcesssedOfferInfo.Add(offerInfo);
                                                                                        }
                                                                                        string offerSupplierCode = offerInfo?.System?.Code;

                                                                                        if (!isOptimized && !isPrebookCreated && !string.IsNullOrWhiteSpace(offerSupplierCode))
                                                                                        {
                                                                                            var mainRoomInfoList = new List<SearchRoom_Package>();
                                                                                            var matchedRoomInfoList = new List<SearchRoom_Package>();
                                                                                            var packageInfo = new Package();
                                                                                            var selectedRoomTokens = new List<string>();

                                                                                            var roomSearchStartTime = DateTime.UtcNow;
                                                                                            isRoomFound = UpdateMatchingRoomOffer(
                                                                                                                mainRoomInfoList
                                                                                                                , matchedRoomInfoList
                                                                                                                , offerInfo
                                                                                                                , prebookcriteria
                                                                                                                , selectedRoomTokens
                                                                                                                , ref packageInfo
                                                                                                                , repricerId
                                                                                                                , reservationIdInLoop
                                                                                                                , activeTabDashboard
                                                                                                                , roomBoardMatchEnum
                                                                                                                );
                                                                                            var roomSearchElaspedTime = (DateTime.UtcNow - roomSearchStartTime).TotalSeconds;
                                                                                            //Does not reattempt prebook if already attempted in same day
                                                                                            if (!_searchserviceHelper.GetReservationEmailStatus(reservationIdInLoop, offerInfo.id, repricerId))
                                                                                            {
                                                                                                if (prebookcriteria != null
                                                                                                    && prebookcriteria.Count > 0
                                                                                                    && matchedRoomInfoList?.Count > 0
                                                                                                    && packageInfo != null
                                                                                                    && packageInfo?.PackageRooms?.Length > 0
                                                                                                    && selectedRoomTokens.Count > 0
                                                                                                    && prebookcriteria?.Count == selectedRoomTokens?.Count
                                                                                                    )
                                                                                                {
                                                                                                    var watchroomprice = Stopwatch.StartNew();

                                                                                                    decimal pricethresholdInOriginalReservationCurrency = extraClientConfig.PriceDifferenceValue;

                                                                                                    var packageinfo = packageInfo;

                                                                                                    double? hotelPrice = 0.0;
                                                                                                    double? RoomlevelPrice = 0.0;
                                                                                                    string? hotelCurrency;
                                                                                                    string? RoomLevelCurrency = null;
                                                                                                    decimal hotellevelfactor = 0.0m;
                                                                                                    var isRoomLevelPriceMatched = true;
                                                                                                    var packagelevelprice = packageinfo.Price?.components?.supplier?.value;
                                                                                                    var packagelevelcurrency = packageinfo.Price?.components?.supplier?.currency;
                                                                                                    var packagelevelfactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, packagelevelcurrency, prebookcriteriaresult.Currency));

                                                                                                    if (matchedRoomInfoList.Any(room => room.SearchRoomPrice == null))
                                                                                                    {
                                                                                                        isRoomLevelPriceMatched = false;
                                                                                                        hotelPrice = packagelevelprice;
                                                                                                        hotelCurrency = packagelevelcurrency;
                                                                                                        hotellevelfactor = packagelevelfactor;
                                                                                                    }
                                                                                                    else
                                                                                                    {
                                                                                                        RoomlevelPrice = matchedRoomInfoList.Sum(room => room.SearchRoomPrice ?? 0.0);
                                                                                                        RoomLevelCurrency = matchedRoomInfoList[0].SearchRoomCurrency;
                                                                                                        var RoomlevelFactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, RoomLevelCurrency, prebookcriteriaresult.Currency));
                                                                                                        hotelPrice = RoomlevelPrice;
                                                                                                        hotelCurrency = RoomLevelCurrency;
                                                                                                        hotellevelfactor = RoomlevelFactor;
                                                                                                    }

                                                                                                    string roomLevelInfo = RoomlevelPrice.ToString() + " " + (RoomLevelCurrency ?? string.Empty);
                                                                                                    string packageLevelInfo = packagelevelprice.ToString() + " " + (packagelevelcurrency ?? string.Empty);

                                                                                                    watchroomprice.Stop();
                                                                                                    var elapsedTimeprice = watchroomprice.Elapsed.TotalSeconds;
                                                                                                    if (isUpdateDB)
                                                                                                    {
                                                                                                        LoggerPersistance.SearchSyncLogging(Constant.SearchSync, reservationIdInLoop, 7, stepSearchtoken, elapsedTimeprice, "pricefilter", repricerId);
                                                                                                    }

                                                                                                    if (hotelPrice > 0)
                                                                                                    {
                                                                                                        if ((extraClientConfig?.PriceDifferenceValue) != 0)
                                                                                                        {
                                                                                                            var ClientFactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, extraClientConfig?.pricedifferencecurrency, prebookcriteriaresult.Currency));
                                                                                                            if (ClientFactor != 0)
                                                                                                            {
                                                                                                                pricethresholdInOriginalReservationCurrency = _searchserviceHelper.RoundToDecimalPlaces(ClientFactor * extraClientConfig.PriceDifferenceValue);
                                                                                                            }
                                                                                                        }
                                                                                                        var originalReservationPrice = prebookcriteriaresult.IssueNet;
                                                                                                        var packageToken = packageinfo.PackageToken;
                                                                                                        prebookrequest = _searchserviceHelper.CreatePreBookCriteria(packageToken, selectedRoomTokens?.ToList());

                                                                                                        var searchsyncprice = _searchserviceHelper.RoundToDecimalPlaces(System.Convert.ToDecimal(hotelPrice) * hotellevelfactor);
                                                                                                        var profit = _searchserviceHelper.RoundToDecimalPlaces(originalReservationPrice - searchsyncprice);

                                                                                                        var profitperc = _searchserviceHelper.RoundToDecimalPlaces((profit / originalReservationPrice) * 100);

                                                                                                        if (searchsyncprice > 0 && prebookcriteriaresult.IssueNet > searchsyncprice)
                                                                                                        {
                                                                                                            Guid newGuid = Guid.NewGuid();
                                                                                                            string token = newGuid.ToString();

                                                                                                            if (isUpdateDB)
                                                                                                            {
                                                                                                                _searchserviceHelper.InsertPreBookClientConfiguration(extraClientConfig, repricerId, reservationIdInLoop, token);
                                                                                                            }

                                                                                                            #region Update MappingId in Caching and Db and MongoDb(async)

                                                                                                            var roomlistforgiata = new List<string?>();

                                                                                                            bool isSaveMappingIdToDbAndUpdateCache = false;

                                                                                                            if (prebookcriteriaresult != null && prebookcriteriaresult.PreBookCriteriaList.Any() && matchedRoomInfoList.Any())
                                                                                                            {
                                                                                                                roomlistforgiata = matchedRoomInfoList.Select(x => x.RoomName)
                                                                                                                                                        .Concat(prebookcriteriaresult.PreBookCriteriaList.Select(x => x.RoomName))
                                                                                                                                                        .Distinct()
                                                                                                                                                        .ToList();

                                                                                                                if (prebookcriteriaresult.PreBookCriteriaList.Any(x => x?.giataroommappingdetail?.MappingId == 0))
                                                                                                                {
                                                                                                                    isSaveMappingIdToDbAndUpdateCache = true;
                                                                                                                }

                                                                                                                if (matchedRoomInfoList.Any(x => x?.GiataMappingRoomDetail?.MappingId == 0))
                                                                                                                {
                                                                                                                    isSaveMappingIdToDbAndUpdateCache = true;
                                                                                                                }

                                                                                                                if (isSaveMappingIdToDbAndUpdateCache && roomlistforgiata != null && roomlistforgiata.Any() == true)
                                                                                                                {
                                                                                                                    await SaveMappingIdToDbAndUpdateCache(roomlistforgiata, PropertyName);
                                                                                                                }
                                                                                                            }

                                                                                                            #endregion Update MappingId in Caching and Db and MongoDb(async)

                                                                                                            var CurrencyFactorTOSAVEINDB = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, prebookcriteriaresult.Currency, CommonConstant.EUR_DefaultTo));
                                                                                                            var roomsdetail = _searchserviceHelper.GetRoomResult(prebookcriteriaresult, matchedRoomInfoList, reservation, offerInfo.System);
                                                                                                            var roomsdetailJson = SerializeDeSerializeHelper.Serialize(roomsdetail);
                                                                                                            var searchsyncjson = SerializeDeSerializeHelper.Serialize(searchResponseIRIX);

                                                                                                            var currencyPrebook = prebookcriteria?.FirstOrDefault()?.Currency ?? "Unknown";

                                                                                                            Task task = Task.Run(() =>
                                                                                                            {
                                                                                                                var roomResult = SerializeDeSerializeHelper.DeSerialize<RoomResult>(roomsdetailJson);
                                                                                                                if (isUpdateDB)
                                                                                                                {
                                                                                                                    _searchserviceHelper.InsertIntoPrebooklog(
                                                                                                                            reservationIdInLoop,
                                                                                                                            repricerId,
                                                                                                                            roomResult,
                                                                                                                            hotelName,
                                                                                                                            profitperc,
                                                                                                                            profit,
                                                                                                                            searchsyncprice,
                                                                                                                            currencyPrebook,
                                                                                                                            reservationCriteriaJson, searchsyncjson, CurrencyFactorTOSAVEINDB
                                                                                                                        ).GetAwaiter().GetResult();
                                                                                                                }
                                                                                                            });

                                                                                                            var isPriceThreshold = false;
                                                                                                            if (extraClientConfig?.IsUsePercentage == true
                                                                                                                && profitperc >= extraClientConfig.PriceDifferencePercentage
                                                                                                                )
                                                                                                            {
                                                                                                                isPriceThreshold = true;
                                                                                                            }
                                                                                                            else
                                                                                                            {
                                                                                                                if (extraClientConfig?.IsUsePercentage != true
                                                                                                                    && profit >= pricethresholdInOriginalReservationCurrency
                                                                                                                    )
                                                                                                                {
                                                                                                                    isPriceThreshold = true;
                                                                                                                    if (!isPriceThreshold)
                                                                                                                    {
                                                                                                                        itemAttemptResult.Status = new Dictionary<int, string>
                                                                                                                        {
                                                                                                                            {
                                                                                                                                reservationIdInLoop, OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed.ToString()
                                                                                                                            }
                                                                                                                        };
                                                                                                                    }
                                                                                                                }
                                                                                                            }
                                                                                                            OptimizeBookingReq optimizeBookingReq = new OptimizeBookingReq { RePricerID = repricerId }; // Instantiate RoomPackageResponse object

                                                                                                            if (isPriceThreshold || extraClientConfig.IsCreatePrebookFoPriceEdgeCase)
                                                                                                            {
                                                                                                                var offerIdForRoomInfo = offerInfo.id;

                                                                                                                var srk = searchResponseIRIX?.srk;
                                                                                                                var prebooktokens = searchResponseIRIX?.tokens.results;
                                                                                                                var hotelsIndex = searchResponseIRIX?.hotels[0].index;
                                                                                                                var offerIndex = offerIdForRoomInfo;

                                                                                                                itemAttemptResult.IsSearchSucess = true;

                                                                                                                var prebook = _irixAdapter.Prebookresponse(reservationIdInLoop, prebookrequest, constants.prebookresp, repricerId, prebooktokens, srk, offerIndex, hotelsIndex, stepSearchtoken, isMultiSupplier);

                                                                                                                bool isPrebookPriceValid = false;
                                                                                                                if (prebook != null)
                                                                                                                {
                                                                                                                    try
                                                                                                                    {
                                                                                                                        //IsPrebook true when PreBook Created
                                                                                                                        itemAttemptResult.IsPrebookSucess = true;
                                                                                                                        var prebookPrice = System.Convert.ToDecimal(prebook?.package?.price?.components?.supplier?.value);
                                                                                                                        var prebookCurrecnyFactor = 1.0M;
                                                                                                                        if (prebookPrice > 0)
                                                                                                                        {
                                                                                                                            currencyPrebook = prebook?.package?.price?.components?.supplier?.currency;
                                                                                                                            prebookCurrecnyFactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, currencyPrebook, prebookcriteriaresult.Currency));
                                                                                                                            prebookPrice = _searchserviceHelper.RoundToDecimalPlaces(System.Convert.ToDecimal(prebookPrice) * prebookCurrecnyFactor);
                                                                                                                            profit = _searchserviceHelper.RoundToDecimalPlaces(originalReservationPrice - prebookPrice);
                                                                                                                            profitperc = _searchserviceHelper.RoundToDecimalPlaces((profit / originalReservationPrice) * 100);
                                                                                                                            searchsyncprice = prebookPrice;

                                                                                                                            if (extraClientConfig?.IsUsePercentage == true
                                                                                                                                && profitperc >= extraClientConfig.PriceDifferencePercentage
                                                                                                                            )
                                                                                                                            {
                                                                                                                                isPriceThreshold = true;
                                                                                                                            }
                                                                                                                            else
                                                                                                                            {
                                                                                                                                if (extraClientConfig?.IsUsePercentage != true
                                                                                                                                    && profit >= pricethresholdInOriginalReservationCurrency
                                                                                                                                    )
                                                                                                                                {
                                                                                                                                    isPriceThreshold = true;
                                                                                                                                }
                                                                                                                            }

                                                                                                                            if (!isPriceThreshold)
                                                                                                                            {
                                                                                                                                itemAttemptResult = UpdateErrorAndReason(repricerId, reservationIdInLoop, itemAttemptResult
                                                                                                                                                    , OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed
                                                                                                                                                    , ReasonCode.Repricer_PriceThresholdCheckFailed);
                                                                                                                            }
                                                                                                                        }
                                                                                                                        else
                                                                                                                        {
                                                                                                                            prebookPrice = searchsyncprice;
                                                                                                                        }
                                                                                                                        isPrebookPriceValid = isPriceThreshold;

                                                                                                                        var watchcancellation = Stopwatch.StartNew();

                                                                                                                                        var cancellationresult = _searchserviceHelper.CancellationCheck(repricerId, cancellationpolicybyreservationid, prebook.cancellationPolicy, prebookcriteriaresult, prebookPrice, reservation);
                                                                                                                                        var prebookjson = SerializeDeSerializeHelper.Serialize(prebook);
                                                                                                                                        var cancellationresultJson = SerializeDeSerializeHelper.Serialize(cancellationresult);

                                                                                                                        var AppSettingKeys_isUseSendGrid = ConfigurationManagerHelper.GetValuefromAppSettings(constants.AppSettingKeys_isUseSendGrid);
                                                                                                                        int AppSettingKeys_isUseSendGridValue = !string.IsNullOrEmpty(AppSettingKeys_isUseSendGrid)
                                                                                                                            ? Convert.ToInt32(AppSettingKeys_isUseSendGrid)
                                                                                                                            : 0;

                                                                                                                        optimizeBookingReq.srk = srk;
                                                                                                                        optimizeBookingReq.hotelIndex = hotelsIndex;
                                                                                                                        optimizeBookingReq.offerIndex = offerIndex;
                                                                                                                        optimizeBookingReq.AvailabilityToken = prebook.availabilityToken;
                                                                                                                        optimizeBookingReq.token = prebooktokens;
                                                                                                                        optimizeBookingReq.ClientRef = token;

                                                                                                                        var watchPreBook = Stopwatch.StartNew();

                                                                                                                        var cancellationfactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, cancellationresult?.Currency, prebookcriteriaresult?.Currency));
                                                                                                                        decimal cancellationamount = 0;

                                                                                                                        if (cancellationresult?.CancellationCharge != 0)
                                                                                                                        {
                                                                                                                            cancellationamount = _searchserviceHelper.RoundToDecimalPlaces(cancellationresult?.CancellationCharge ?? 0 * cancellationfactor);
                                                                                                                        }

                                                                                                                        var profitAfterCancellation = profit - cancellationamount;

                                                                                                                        if (profitAfterCancellation > 0 && isPriceThreshold)
                                                                                                                        {
                                                                                                                            var profitPercentageAfterCancellation = _searchserviceHelper.RoundToDecimalPlaces((profitAfterCancellation / originalReservationPrice) * 100);

                                                                                                                            if (extraClientConfig?.IsUsePercentage == true
                                                                                                                                && profitPercentageAfterCancellation >= extraClientConfig.PriceDifferencePercentage
                                                                                                                            )
                                                                                                                            {
                                                                                                                                isPriceThreshold = true;
                                                                                                                            }
                                                                                                                            else if (extraClientConfig?.IsUsePercentage != true
                                                                                                                                    && profitAfterCancellation >= pricethresholdInOriginalReservationCurrency
                                                                                                                            )
                                                                                                                            {
                                                                                                                                isPriceThreshold = true;
                                                                                                                            }
                                                                                                                            else
                                                                                                                            {
                                                                                                                                isPriceThreshold = false;
                                                                                                                            }
                                                                                                                        }
                                                                                                                        else
                                                                                                                        {
                                                                                                                            itemAttemptResult = UpdateErrorAndReason(repricerId, reservationIdInLoop, itemAttemptResult
                                                                                                                                                    , OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed
                                                                                                                                                    , ReasonCode.Repricer_PriceThresholdCheckFailed);
                                                                                                                        }

                                                                                                                        if (cancellationresult != null
                                                                                                                            && (isPrebookPriceValid || extraClientConfig.IsCreatePrebookFoPriceEdgeCase)
                                                                                                                            && (cancellationresult.cPStatus == CPBucketStatus.loose.ToString()
                                                                                                                            || cancellationresult.cPStatus == CPBucketStatus.tightWithBuffer.ToString()
                                                                                                                            || cancellationresult.cPStatus == CPBucketStatus.CancellationChargesApplicable.ToString()
                                                                                                                            )
                                                                                                                        )
                                                                                                                        {
                                                                                                                            if (isPriceThreshold)
                                                                                                                            {
                                                                                                                                isPrebookCreated = true;
                                                                                                                            }
                                                                                                                            RoomResult roomResult = null;
                                                                                                                            CancellationPolicyResult cpResult = null;
                                                                                                                            var clientConfig_DaysDifferenceInPreBookCreation = 0;
                                                                                                                            try
                                                                                                                            {
                                                                                                                                watchcancellation.Stop();
                                                                                                                                var elapsedTimecancellation = 0.0;
                                                                                                                                elapsedTimecancellation = watchcancellation.Elapsed.TotalSeconds;
                                                                                                                                cpResult = SerializeDeSerializeHelper.DeSerialize<CancellationPolicyResult>(cancellationresultJson);
                                                                                                                                roomResult = SerializeDeSerializeHelper.DeSerialize<RoomResult>(roomsdetailJson);
                                                                                                                                clientConfig_DaysDifferenceInPreBookCreation = extraClientConfig.ClientConfig_DaysDifferenceInPreBookCreation;
                                                                                                                            }
                                                                                                                            catch (Exception ex)
                                                                                                                            {
                                                                                                                                var irixErrorEntity = new IrixErrorEntity
                                                                                                                                {
                                                                                                                                    ClassName = _className,
                                                                                                                                    MethodName = _method,
                                                                                                                                    Params = $"RePricerId: {repricerId}, ReservationId: {reservationIdInLoop}"
                                                                                                                                };
                                                                                                                                _log.Error(irixErrorEntity, ex);
                                                                                                                            }

                                                                                                                            OptimizationBookingResponse optimizationBookingResponse = null;
                                                                                                                            itemAttemptResult.IsOptimized = isOptimized;
                                                                                                                            isCurrentPrebookOptimized = false;
                                                                                                                            if (_isMock == false)
                                                                                                                            {
                                                                                                                                if (itemAttemptResult.IsPrebookSucess == true
                                                                                                                                    && isUpdateDB == true
                                                                                                                                    && cancellationresult?.cPStatus?.ToLower() == CPBucketStatus.loose.ToString()
                                                                                                                                )
                                                                                                                                {
                                                                                                                                    dryRunResponse = _dryRunOptimizationService._2_DryRunOptimizationApiIRIX(optimizeBookingReq, reservationIdInLoop, isUpdateDB: isOptimizeTriggeredManually, isCacheRefresh: isOptimizeTriggeredManually, isMultiSupplier: true);

                                                                                                                                    itemAttemptResult.IsDryRunExecuted = true;
                                                                                                                                    itemAttemptResult.DryRunResponse = dryRunResponse;
                                                                                                                                    if (dryRunResponse?.ExpectedGain?.Value > 0)
                                                                                                                                    {
                                                                                                                                        isPriceThreshold = _searchserviceHelper.IsPriceThreshold
                                                                                                                                        (
                                                                                                                                            originalReservationPrice
                                                                                                                                            , dryRunResponse.ExpectedGain.Value
                                                                                                                                            , repricerClientDetail
                                                                                                                                            , prebookcriteriaresult
                                                                                                                                            , reservationIdInLoop
                                                                                                                                            , itemAttemptResult
                                                                                                                                        );
                                                                                                                                        if (isPriceThreshold)
                                                                                                                                        {
                                                                                                                                            var messages = new Dictionary<string, Dictionary<string, string>>();
                                                                                                                                            messages["ExpectedGain"] = new Dictionary<string, string>
                                                                                                                                            {
                                                                                                                                                    { "AboveThreshold", $"ExpectedGain = {dryRunResponse.ExpectedGain.Value} {dryRunResponse.ExpectedGain.Currency}" },
                                                                                                                                            };

                                                                                                                                            var messageJson = SerializeDeSerializeHelper.Serialize(messages);
                                                                                                                                            var optimizationReservationResponse = new OptimizationBookingResponse
                                                                                                                                            {
                                                                                                                                                id = reservationIdInLoop,
                                                                                                                                                IsDryRun = true,
                                                                                                                                                Status = (int)OptimizationStatusEnum.DryRunOptimizationSucess_ExpectedGainAboveThreshold,
                                                                                                                                                Messages = messageJson,
                                                                                                                                                Title = "Expected gain above threshold",
                                                                                                                                                Detail = $"ExpectedGain : {dryRunResponse.ExpectedGain.Value} {dryRunResponse.ExpectedGain.Currency}"
                                                                                                                                            };

                                                                                                                                            try
                                                                                                                                            {
                                                                                                                                                Console.ForegroundColor = ConsoleColor.DarkRed;
                                                                                                                                                Console.WriteLine($"\n*********************************************( EXPECTED_GAIN_FOUND )**********************************************************");
                                                                                                                                                Console.ForegroundColor = ConsoleColor.Green;
                                                                                                                                                Console.WriteLine($"RepricerId    \t{repricerId}");
                                                                                                                                                Console.WriteLine($"ReservationId \t{reservationIdInLoop}");
                                                                                                                                                Console.WriteLine($"Optimization  \tMULTI_SUPPLIER  ");
                                                                                                                                                Console.WriteLine($"SupplierName  \t{offerSupplierCode}  ");
                                                                                                                                                Console.WriteLine($"Counter       \t({counter}\\{counterTotal})");
                                                                                                                                                Console.WriteLine($"DryRunResult  \t{dryRunResponse.ExpectedGain.Value} {dryRunResponse.ExpectedGain.Currency}");
                                                                                                                                                Console.WriteLine($"DateTimeLocal \t{DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss")}");
                                                                                                                                                Console.ForegroundColor = ConsoleColor.DarkRed;
                                                                                                                                                Console.WriteLine($"*********************************************( EXPECTED_GAIN_FOUND )**********************************************************\n");
                                                                                                                                                Console.ResetColor();

                                                                                                                                                itemAttemptResult.IsExpectedGainAboveThreshold = true;
                                                                                                                                                itemAttemptResult.Status = new Dictionary<int, string>
                                                                                                                                                {
                                                                                                                                                    { reservationIdInLoop, OptimizationStatusEnum.DryRunOptimizationSucess_ExpectedGainAboveThreshold.ToString() }
                                                                                                                                                };
                                                                                                                                                if (itemAttemptResult.OptimizableStatus == null)
                                                                                                                                                {
                                                                                                                                                    itemAttemptResult.OptimizableStatus = new OptimizationOptimizationBooking
                                                                                                                                                    {
                                                                                                                                                        Status = OptimizationStatusEnum.DryRunOptimizationSucess_ExpectedGainAboveThreshold.ToString()
                                                                                                                                                    };
                                                                                                                                                }
                                                                                                                                                else
                                                                                                                                                {
                                                                                                                                                    itemAttemptResult.OptimizableStatus.Status = OptimizationStatusEnum.DryRunOptimizationSucess_ExpectedGainAboveThreshold.ToString();
                                                                                                                                                }
                                                                                                                                            }
                                                                                                                                            catch
                                                                                                                                            {
                                                                                                                                            }

                                                                                                                                            _optimizationHelper.UpdateBookingActionTakenInDB(repricerId, reservationIdInLoop, optimizationReservationResponse
                                                                                                                                            , null, true, false);

                                                                                                                                            try
                                                                                                                                            {
                                                                                                                                                var isemailalreadysent = _searchserviceHelper.IsEmailAlreadySent(repricerId, reservationIdInLoop);
                                                                                                                                                if (!isemailalreadysent)
                                                                                                                                                {
                                                                                                                                                    var dryrunemail = new DryRunEmail
                                                                                                                                                    {
                                                                                                                                                        RepricerID = repricerId,
                                                                                                                                                        RepricerName = repricerClientDetail.RepricerUserName,
                                                                                                                                                        ReservationRoomName = roomResult?.ReservationRoomName,
                                                                                                                                                        PrebookRoomName = roomResult?.PreBookRoomName,
                                                                                                                                                        ReservationSupplier = reservationsupplier,
                                                                                                                                                        PrebookSupplier = roomResult?.PrebookProviders ?? string.Empty,
                                                                                                                                                        ReservationID = reservationIdInLoop,
                                                                                                                                                        ReservationRoomInfo = roomResult?.ReservationRoomInfo,
                                                                                                                                                        PreBookRoomInfo = roomResult?.PreBookRoomInfo,
                                                                                                                                                        ReservationDestination = Destinations,
                                                                                                                                                        PreBookDestination = Destinations,
                                                                                                                                                        ReservationHotelName = hotelName,
                                                                                                                                                        PreBookHotelName = hotelName,
                                                                                                                                                        ExpectedGain = dryRunResponse?.ExpectedGain?.Value,
                                                                                                                                                        Currency = dryRunResponse?.ExpectedGain?.Currency
                                                                                                                                                    };
                                                                                                                                                    if (!isOptimizeTriggeredManually && extraClientConfig?.OptimizationType == OptimizationType.Automatic)
                                                                                                                                                    {
                                                                                                                                                        var taskEmail = Task.Run(() => _emailService.SendEmail(dryrunemail, repricerClientDetail));
                                                                                                                                                    }
                                                                                                                                                }
                                                                                                                                            }
                                                                                                                                            catch
                                                                                                                                            {
                                                                                                                                            }
                                                                                                                                        }
                                                                                                                                        isAnyOptimized = itemAttemptResult.IsOptimizable = isPriceThreshold = itemAttemptResult.IsExpectedGainAboveThreshold;
                                                                                                                                    }
                                                                                                                                    else
                                                                                                                                    {
                                                                                                                                        itemAttemptResult.IsOptimizable = false;
                                                                                                                                    }
                                                                                                                                }

                                                                                                                                //Step 2) Optimize booking by calling IRIX API for valid reservations
                                                                                                                                if (extraClientConfig?.OptimizationType != null
                                                                                                                                //&& extraClientConfig?.OptimizationType == OptimizationType.Automatic// removing this condition as of now multi supplier will not work with automation
                                                                                                                                && cancellationresult?.cPStatus?.ToLower() == CPBucketStatus.loose.ToString()
                                                                                                                                && isPrebookPriceValid
                                                                                                                                && optimizationStatus?.IsOptimizable == true
                                                                                                                                && isUpdateDB == true
                                                                                                                                && itemAttemptResult.IsPrebookSucess == true
                                                                                                                                && isOptimized == false
                                                                                                                                && isOptimizeTriggeredManually == true
                                                                                                                                )
                                                                                                                                {
                                                                                                                                    #region Logging end of item

                                                                                                                                    try
                                                                                                                                    {
                                                                                                                                        var logInfoEntry = new
                                                                                                                                        {
                                                                                                                                            RepricerId = repricerId,
                                                                                                                                            Message = "Entered into OptimizeAtIrix end block",
                                                                                                                                            Minute = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2"),
                                                                                                                                            isMultiSupplier = true,
                                                                                                                                            OffersFound = offersFound,
                                                                                                                                            reservation?.ReservationId,
                                                                                                                                            Params = new
                                                                                                                                            {
                                                                                                                                                repricerId,
                                                                                                                                                reservationId,
                                                                                                                                                isUpdateDB,
                                                                                                                                                isMultiSupplier,
                                                                                                                                                supplierName = allowedSupplierName,
                                                                                                                                                isOptimizeTriggeredManually,
                                                                                                                                                totalItems,
                                                                                                                                                currentItem,
                                                                                                                                            },
                                                                                                                                            isPrebookCreated,
                                                                                                                                            isOptimized,
                                                                                                                                            Method = method,
                                                                                                                                            StartTime = startTime.ToString("yyyy-MMM-dd HH:mm:ss"),

                                                                                                                                            EndTime = DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss"),
                                                                                                                                            EndTimeUTC = DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss")
                                                                                                                                        };

                                                                                                                                        var msg = $"{SerializeDeSerializeHelper.Serialize(logInfoEntry)}";
                                                                                                                                        var irixErrorEntity = new IrixErrorEntity
                                                                                                                                        {
                                                                                                                                            ClassName = "OPTIMIZE_TRIGGERED",
                                                                                                                                            MethodName = method,
                                                                                                                                            RePricerId = repricerId,
                                                                                                                                            ReservationId = Convert.ToInt32(reservation?.ReservationId),
                                                                                                                                            Params = msg
                                                                                                                                        };
                                                                                                                                        _log.Info(msg, irixErrorEntity, true);
                                                                                                                                    }
                                                                                                                                    catch (Exception ex)
                                                                                                                                    {
                                                                                                                                    }

                                                                                                                                    #endregion Logging end of item

                                                                                                                                    //Already calling in upper block
                                                                                                                                    //dryRunResponse = _dryRunOptimizationService._2_DryRunOptimizationApiIRIX(optimizeBookingReq, reservationIdInLoop, isOptimizeTriggeredManually, isOptimizeTriggeredManually, isMultiSupplier);

                                                                                                                                    if (dryRunResponse?.ExpectedGain?.Value > 0)
                                                                                                                                    {
                                                                                                                                        isPriceThreshold = _searchserviceHelper.IsPriceThreshold
                                                                                                                                        (
                                                                                                                                            originalReservationPrice
                                                                                                                                            , dryRunResponse.ExpectedGain.Value
                                                                                                                                            , repricerClientDetail
                                                                                                                                            , prebookcriteriaresult
                                                                                                                                            , reservationIdInLoop
                                                                                                                                            , itemAttemptResult
                                                                                                                                        );
                                                                                                                                        if (isPriceThreshold)
                                                                                                                                        {
                                                                                                                                            itemAttemptResult.IsOptimizable = true;

                                                                                                                                            //Call IRIX Optimize booking API
                                                                                                                                            optimizationBookingResponse = null;
                                                                                                                                            isOptimized = true;

                                                                                                                                            //Uncomment to optimize isOptimizeTriggeredManually // roomResult
                                                                                                                                            if (isOptimizeTriggeredManually == true && !string.IsNullOrWhiteSpace(supplierName))
                                                                                                                                            {
                                                                                                                                                optimizationBookingResponse = _optimizationService._3_OptimizeBookingApiIRIX(optimizeBookingReq, reservationIdInLoop, isOptimizeTriggeredManually, isMultiSupplier);
                                                                                                                                            }

                                                                                                                                            if (optimizationBookingResponse != null &&
                                                                                                                                                (optimizationBookingResponse?.NewReservation?.Id > 0
                                                                                                                                                || optimizationBookingResponse?.optimizedBy > 0)
                                                                                                                                                )
                                                                                                                                            {
                                                                                                                                                isOptimized = isAnyOptimized = itemAttemptResult.IsOptimized = true;
                                                                                                                                                itemAttemptResult.OptimizableStatus = optimizationBookingResponse?.Optimization;
                                                                                                                                                isCurrentPrebookOptimized = true;
                                                                                                                                                _log.UpdateOptimizationToQueue(OptimizationStatusEnum.optimized.ToString(), reservationIdInLoop, repricerId, false, true, isOptimized);
                                                                                                                                            }
                                                                                                                                            else
                                                                                                                                            {
                                                                                                                                                itemAttemptResult.IsOptimized = isOptimized = isCurrentPrebookOptimized = false;
                                                                                                                                                itemAttemptResult = UpdateErrorAndReason(repricerId,
                                                                                                                                                                            reservationIdInLoop,
                                                                                                                                                                            itemAttemptResult,
                                                                                                                                                                            OptimizationStatusEnum.Repricer_OptimizationAttemptFailedAtLastAPIStep,
                                                                                                                                                                            ReasonCode.Repricer_OptimizationAttemptFailedAtLastAPIStep);
                                                                                                                                            }
                                                                                                                                        }
                                                                                                                                        else
                                                                                                                                        {
                                                                                                                                            itemAttemptResult = UpdateErrorAndReason(repricerId,
                                                                                                                                                                    reservationIdInLoop,
                                                                                                                                                                    itemAttemptResult,
                                                                                                                                                                    OptimizationStatusEnum.DryRunOptimizationFailed_LoweredExpectedGain,
                                                                                                                                                                    ReasonCode.DryRunOptimizationFailed_LoweredExpectedGain);
                                                                                                                                        }
                                                                                                                                    }
                                                                                                                                    else
                                                                                                                                    {
                                                                                                                                        itemAttemptResult = UpdateErrorAndReason(repricerId,
                                                                                                                                                                    reservationIdInLoop,
                                                                                                                                                                    itemAttemptResult,
                                                                                                                                                                    OptimizationStatusEnum.DryRunOptimizationFailed,
                                                                                                                                                                    ReasonCode.DryRunOptimizationFailed);
                                                                                                                                        //if (isOptimizeTriggeredManually == true)
                                                                                                                                        //{
                                                                                                                                        //    _log.AddOptimizationToQueue(OptimizationStatusEnum.DryRunOptimizationFailed.ToString(), reservationIdInLoop, repricerId,true,false);
                                                                                                                                        //}
                                                                                                                                    }
                                                                                                                                }
                                                                                                                                else
                                                                                                                                {
                                                                                                                                    try
                                                                                                                                    {
                                                                                                                                        if (!isPrebookPriceValid)
                                                                                                                                        {
                                                                                                                                            itemAttemptResult = UpdateErrorAndReason(repricerId,
                                                                                                                                                                    reservationIdInLoop,
                                                                                                                                                                    itemAttemptResult,
                                                                                                                                                                    OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed,
                                                                                                                                                                    ReasonCode.Repricer_PriceThresholdCheckFailed);
                                                                                                                                        }
                                                                                                                                        else
                                                                                                                                        {
                                                                                                                                            itemAttemptResult = UpdateErrorAndReason(repricerId,
                                                                                                                                                                    reservationIdInLoop,
                                                                                                                                                                    itemAttemptResult,
                                                                                                                                                                    OptimizationStatusEnum.Repricer_OptimizationNotAllowed,
                                                                                                                                                                    ReasonCode.Repricer_OptimizationNotAllowed);
                                                                                                                                        }
                                                                                                                                    }
                                                                                                                                    catch
                                                                                                                                    {
                                                                                                                                    }
                                                                                                                                }
                                                                                                                            }

                                                                                                                            Task.Run(() =>
                                                                                                                            {
                                                                                                                                try
                                                                                                                                {
                                                                                                                                    if (isUpdateDB || isCurrentPrebookOptimized)
                                                                                                                                    {
                                                                                                                                        LoggerPersistance.SearchSyncLogging(Constant.SearchSync, reservationIdInLoop, 11, stepSearchtoken, watchcancellation.Elapsed.TotalSeconds, "cancellationfilter", repricerId);

                                                                                                                                        var prebookAvailabilityToken = prebook?.availabilityToken ?? string.Empty;

                                                                                                                                        #region Logging end of item

                                                                                                                                        try
                                                                                                                                        {
                                                                                                                                            var message = $" PREBOOK_CREATE_SUCCESS\t\t : \t MULTI_SUPPLIER" +
                                                                                                                                            $"\n\t\t\t Counter \t\t\t : \t ({counter}\\{counterTotal})" +
                                                                                                                                            $"\n\t\t\t RepricerId \t\t\t : \t {repricerId}" +
                                                                                                                                            $"\n\t\t\t ReservationId \t\t\t : \t {reservation?.ReservationId}" +
                                                                                                                                            $"\n\t\t\t RESERVATION_Supplier \t\t : \t {reservation?.supplierName}" +
                                                                                                                                            $"\n\t\t\t PREBOOK_Supplier \t\t : \t {offerSupplierCode}" +
                                                                                                                                            $"\n\t\t\t BATCH_Supplier \t\t : \t ({allowedSupplierName})" +
                                                                                                                                            $"\n\t\t\t CP_Status \t\t\t : \t {cancellationresult?.cPStatus}" +
                                                                                                                                            $"\n\t\t\t IsDryRunExecuted \t\t : \t {itemAttemptResult.IsDryRunExecuted}" +
                                                                                                                                            $"\n\t\t\t ExpectedGain \t\t\t : \t {dryRunResponse?.ExpectedGain?.Value} {dryRunResponse?.ExpectedGain?.Currency}";
                                                                                                                                            var color = dryRunResponse?.ExpectedGain?.Value > 0 ? ConsoleColor.Green : ConsoleColor.Magenta;
                                                                                                                                            _log.Info(message, null, false, true, color);
                                                                                                                                        }
                                                                                                                                        catch (Exception ex)
                                                                                                                                        {
                                                                                                                                        }

                                                                                                                                        #endregion Logging end of item

                                                                                                                                        _reservationPersistance.InsertPreBookReservation(
                                                                                                                                            prebookAvailabilityToken,
                                                                                                                                            reservationIdInLoop,
                                                                                                                                            repricerId,
                                                                                                                                            AppSettingKeys_isUseSendGridValue,
                                                                                                                                            searchsyncprice,
                                                                                                                                            prebookcount + 1,
                                                                                                                                            string.Empty, //emailTemplateEmailTemplate,
                                                                                                                                            prebookjson,
                                                                                                                                            string.Empty, //emailTemplateReservationTemplate,
                                                                                                                                            string.Empty, //emailTemplatePreBookTemplate,
                                                                                                                                            profit,
                                                                                                                                            profitAfterCancellation,
                                                                                                                                            cpResult,
                                                                                                                                            token,
                                                                                                                                            roomResult,
                                                                                                                                            searchsyncjson, CurrencyFactorTOSAVEINDB, clientConfig_DaysDifferenceInPreBookCreation, stepSearchtoken, isCurrentPrebookOptimized
                                                                                                                                        );

                                                                                                                                        itemAttemptResult.IsPrebookSucess = true;

                                                                                                                                        if (isCurrentPrebookOptimized || isAnyOptimized || itemAttemptResult.IsExpectedGainAboveThreshold)
                                                                                                                                        {
                                                                                                                                            _masterService.RefreshDbAndCachedReport(repricerId, reservationIdInLoop, false);
                                                                                                                                        }
                                                                                                                                    }
                                                                                                                                }
                                                                                                                                catch (Exception ex)
                                                                                                                                {
                                                                                                                                    var irixErrorEntity = new IrixErrorEntity
                                                                                                                                    {
                                                                                                                                        ClassName = _className,
                                                                                                                                        MethodName = _method,
                                                                                                                                        Params = $"RePricerId: {repricerId}, ReservationId: {reservationIdInLoop}"
                                                                                                                                    };
                                                                                                                                    _log.Error(irixErrorEntity, ex);
                                                                                                                                    itemAttemptResult.IsPrebookSucess = false;
                                                                                                                                }
                                                                                                                            })?.Wait();
                                                                                                                        }
                                                                                                                        else
                                                                                                                        {
                                                                                                                            try
                                                                                                                            {
                                                                                                                                if (cancellationresult?.cPStatus?.ToLower() == "tight")
                                                                                                                                {
                                                                                                                                    itemAttemptResult = UpdateErrorAndReason(repricerId,
                                                                                                                                                                reservationIdInLoop,
                                                                                                                                                                itemAttemptResult,
                                                                                                                                                                OptimizationStatusEnum.Repricer_TightCancellationPolicy,
                                                                                                                                                                ReasonCode.Repricer_TightCancellationPolicy);
                                                                                                                                }
                                                                                                                                else if (!isPrebookPriceValid)
                                                                                                                                {
                                                                                                                                    itemAttemptResult = UpdateErrorAndReason(repricerId,
                                                                                                                                                            reservationIdInLoop,
                                                                                                                                                            itemAttemptResult,
                                                                                                                                                            OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed,
                                                                                                                                                            ReasonCode.Repricer_PriceThresholdCheckFailed);
                                                                                                                                }
                                                                                                                                else
                                                                                                                                {
                                                                                                                                    itemAttemptResult = UpdateErrorAndReason(repricerId,
                                                                                                                                                            reservationIdInLoop,
                                                                                                                                                            itemAttemptResult,
                                                                                                                                                            OptimizationStatusEnum.Repricer_NoNewOfferOrMatchedCancellationPolicy,
                                                                                                                                                            ReasonCode.Repricer_NoNewOfferOrMatchedCancellationPolicy);
                                                                                                                                }
                                                                                                                            }
                                                                                                                            catch
                                                                                                                            {
                                                                                                                            }

#pragma warning disable CA2016 // Forward the 'CancellationToken' parameter to methods
                                                                                                                            var taskInsertPreBookReservationLog = Task.Run(() =>
                                                                                                                            {
                                                                                                                                if (!isOptimized || !isPrebookCreated)
                                                                                                                                {
                                                                                                                                    if (isUpdateDB)
                                                                                                                                    {
                                                                                                                                        var roomResult = SerializeDeSerializeHelper.DeSerialize<RoomResult>(roomsdetailJson);
                                                                                                                                        var cpResult = SerializeDeSerializeHelper.DeSerialize<CancellationPolicyResult>(cancellationresultJson);

                                                                                                                                        _reservationPersistance.InsertPreBookReservationLog(prebook.availabilityToken,
                                                                                                                                                reservationIdInLoop,
                                                                                                                                                repricerId,
                                                                                                                                                AppSettingKeys_isUseSendGridValue,
                                                                                                                                                searchsyncprice,
                                                                                                                                                prebookjson,
                                                                                                                                                profit,
                                                                                                                                                profitAfterCancellation,
                                                                                                                                                cpResult,
                                                                                                                                                token,
                                                                                                                                                roomResult,
                                                                                                                                                searchsyncjson, CurrencyFactorTOSAVEINDB, stepSearchtoken);
                                                                                                                                    }
                                                                                                                                }
                                                                                                                            });
#pragma warning restore CA2016 // Forward the 'CancellationToken' parameter to methods
                                                                                                                        }
                                                                                                                        if (!reservationIds.Contains(reservationIdInLoop))
                                                                                                                        {
                                                                                                                            RedisCacheHelper.ProcessReservation(reservationIdInLoop);
                                                                                                                            reservationIds.Add(reservationIdInLoop);
                                                                                                                        }
                                                                                                                    }
                                                                                                                    catch (Exception ex)
                                                                                                                    {
                                                                                                                        var irixErrorEntity = new IrixErrorEntity
                                                                                                                        {
                                                                                                                            ClassName = _className,
                                                                                                                            MethodName = _method,
                                                                                                                            Params = $"RePricerId: {repricerId}, ReservationId: {reservationIdInLoop}"
                                                                                                                        };
                                                                                                                        _log.Error(irixErrorEntity, ex);
                                                                                                                    }
                                                                                                                }
                                                                                                                else
                                                                                                                {
                                                                                                                    itemAttemptResult = UpdateErrorAndReason(repricerId, reservationIdInLoop, itemAttemptResult
                                                                                                                                            , OptimizationStatusEnum.PrebookAPICallFailed
                                                                                                                                            , ReasonCode.PrebookAPICallFailed);
                                                                                                                    itemAttemptResult.IsSearchSucess = false;

                                                                                                                    //if(isOptimizeTriggeredManually == true)
                                                                                                                    //{
                                                                                                                    //    _log.AddOptimizationToQueue(OptimizationStatusEnum.PrebookAPICallFailed.ToString(), reservationIdInLoop, repricerId, true);

                                                                                                                    //}
                                                                                                                }
                                                                                                            }
                                                                                                        }
                                                                                                        else
                                                                                                        {
                                                                                                            itemAttemptResult = UpdateErrorAndReason(repricerId, reservationIdInLoop, itemAttemptResult
                                                                                                            , OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed
                                                                                                            , ReasonCode.Repricer_PriceThresholdCheckFailed);
                                                                                                        }
                                                                                                    }
                                                                                                    else
                                                                                                    {
                                                                                                        itemAttemptResult = UpdateErrorAndReason(repricerId, reservationIdInLoop, itemAttemptResult
                                                                                                            , OptimizationStatusEnum.Repricer_PriceThresholdCheckFailed
                                                                                                            , ReasonCode.Repricer_PriceThresholdCheckFailed);
                                                                                                    }
                                                                                                }
                                                                                                else
                                                                                                {
                                                                                                    itemAttemptResult = UpdateErrorAndReason(repricerId, reservationIdInLoop, itemAttemptResult
                                                                                                        , OptimizationStatusEnum.NoMatchingOffers
                                                                                                        , ReasonCode.NoOfferFound);
                                                                                                    //if (isOptimizeTriggeredManually == true)
                                                                                                    //{
                                                                                                    //    _log.AddOptimizationToQueue(OptimizationStatusEnum.PrebookAPICallFailed.ToString(), reservationIdInLoop, repricerId, false);

                                                                                                    //}
                                                                                                }
                                                                                            }
                                                                                        }
                                                                                    }
                                                                                    catch (Exception ex)
                                                                                    {
                                                                                        var irixErrorEntity = new IrixErrorEntity
                                                                                        {
                                                                                            ClassName = _className,
                                                                                            MethodName = $"{_1_PrebookAndOptimize_MultiSupplier}-loop-offerInfoList",
                                                                                        };
                                                                                        _log.Error(irixErrorEntity, ex);
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                        if (isOptimized || isPrebookCreated)
                                                                        {
                                                                            if (!isOptimizeTriggeredManually && itemAttemptResult.IsExpectedGainAboveThreshold)
                                                                            {
                                                                                return;
                                                                            }
                                                                            else
                                                                            {
                                                                                break;
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                                catch (Exception ex)
                                                                {
                                                                    var irixErrorEntity = new IrixErrorEntity
                                                                    {
                                                                        ClassName = _className,
                                                                        MethodName = "ProcesssedOfferInfo",
                                                                        RePricerId = repricerId,
                                                                        ReservationId = reservationIdInLoop,
                                                                    };
                                                                    _log.Error(irixErrorEntity, ex);
                                                                }
                                                            }
                                                        }
                                                        else
                                                        {
                                                            itemAttemptResult = UpdateErrorAndReason(repricerId, reservationIdInLoop, itemAttemptResult
                                                                , OptimizationStatusEnum.InvalidPrebookCriteria
                                                                , ReasonCode.InvalidPrebookCriteria);
                                                        }
                                                    }
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                var irixErrorEntity = new IrixErrorEntity
                                                {
                                                    ClassName = _className,
                                                    MethodName = "InnerCreateSearch",
                                                    RePricerId = repricerId,
                                                    ReservationId = reservationIdInLoop,

                                                    Params = $"RePricerId: {repricerId}, ReservationId: {reservationIdInLoop}"
                                                };
                                                _log.Error(irixErrorEntity, ex);
                                            }

                                            #endregion Supplier wise processing

                                            #region Logging end of item

                                            try
                                            {
                                                supplierCounter += batch.Count;
                                                //if (supplierCounter == batches.Count() || supplierCounter == batchSize)
                                                {
                                                    var msg = $"Prebook Search END  \tSupplierName:  {allowedSupplierName}\n({supplierCounter}\\{supplierCounterTotal})  ({(DateTime.UtcNow - startTimeSupplierItem).TotalMinutes.ToString("F2")}\\{(DateTime.UtcNow - startTimeSupplierMain).TotalMinutes.ToString("F2")})\tRepricerId: {repricerId}\t\tReservationId: {reservation?.ReservationId}\t\tMULTI_SUPPLIER";
                                                    //## Uncomment for DEBUGGING
                                                    //_log.Info(msg, null, false, true, ConsoleColor.DarkMagenta);
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                            }

                                            #endregion Logging end of item
                                        }

                                        if (isOptimized)
                                        {
                                            break;
                                        }
                                    }
                                }
                                else
                                {
                                    if (IsOptimizationAlreadyRunningForReservation)
                                    {
                                        itemAttemptResult = UpdateErrorAndReason(repricerId, reservationIdInLoop, itemAttemptResult
                                                                        , OptimizationStatusEnum.Repricer_AlreadyQueueRunning
                                                                        , ReasonCode.Repricer_AlreadyQueueRunning);
                                    }
                                    else
                                    {
                                        itemAttemptResult = UpdateErrorAndReason(repricerId, reservationIdInLoop, itemAttemptResult
                                                                        , OptimizationStatusEnum.Repricer_NotOptimizable
                                                                        , ReasonCode.OptimizationNotAllowed);
                                    }
                                }
                                _searchserviceHelper.IsOptimizationAlreadyRunning(repricerId, reservationIdInLoop, _isSameSupplier, isRemove: true, isCheckAndSet: false);
                            }
                            catch (Exception ex)
                            {
                                _searchserviceHelper.IsOptimizationAlreadyRunning(repricerId, reservationIdInLoop, _isSameSupplier, isRemove: true, isCheckAndSet: false);
                                var irixErrorEntity = new IrixErrorEntity
                                {
                                    ClassName = _className,

                                    RePricerId = repricerId,
                                    ReservationId = Convert.ToInt32(reservationId),
                                    MethodName = _method,
                                    Params = $"RePricerId: {repricerId}, ReservationId: {reservationId}"
                                };
                                _log.Error(irixErrorEntity, ex);

                                itemAttemptResult = UpdateErrorAndReason(repricerId, reservationIdInLoop, itemAttemptResult
                                                        , OptimizationStatusEnum.Repricer_AlreadyQueueRunning
                                                        , ReasonCode.Repricer_AlreadyQueueRunning);
                            }
                            finally
                            {
                                var searchkey = $"{ServiceAdapterConstants.SearchSync}_{reservationId}";
                                var prebookkey = $"{ServiceAdapterConstants.PreBook}_{reservationId}";
                                RedisCacheHelper.Remove(searchkey);
                                RedisCacheHelper.Remove(prebookkey);
                            }
                            //}

                            try
                            {
                                if (itemAttemptResult.IsPrebookSucess || itemAttemptResult.IsExpectedGainAboveThreshold || itemAttemptResult.IsOptimized)
                                {
                                    var filteredResults = preBookResponseResults.Where(x => x.ReservationId != itemAttemptResult.ReservationId).ToList();
                                    preBookResponseResults = new ConcurrentBag<PreBookResponseResult>(filteredResults);

                                    if (itemAttemptResult.IsExpectedGainAboveThreshold)
                                    {
                                        if (itemAttemptResult.OptimizableStatus == null)
                                        {
                                            itemAttemptResult.OptimizableStatus = new OptimizationOptimizationBooking
                                            {
                                                Status = OptimizationStatusEnum.DryRunOptimizationSucess_ExpectedGainAboveThreshold.ToString()
                                            };
                                        }
                                        else
                                        {
                                            itemAttemptResult.OptimizableStatus.Status = OptimizationStatusEnum.DryRunOptimizationSucess_ExpectedGainAboveThreshold.ToString();
                                        }
                                    }
                                    preBookResponseResults.Add(itemAttemptResult);
                                }
                                if (!preBookResponseResults.Any(x => x.ReservationId == reservationIdInLoop))
                                {
                                    preBookResponseResults.Add(itemAttemptResult);
                                }
                                //}
                            }
                            catch (Exception ex)
                            {
                            }

                            #region Logging end of item

                            try
                            {
                                var logInfoEntry = new LogParams
                                {
                                    RepricerId = repricerId,
                                    Counter = counter,
                                    CounterTotal = counterTotal,
                                    StartTime = startTime,
                                    OffersFound = offersFound,
                                    IsMultiSupplier = isMultiSupplier,
                                    ReservationId = reservation?.ReservationId ?? 0,
                                    IsUpdateDB = isUpdateDB,
                                    SupplierName = supplierName,
                                    IsOptimizeTriggeredManually = isOptimizeTriggeredManually,
                                    TotalItems = totalItems,
                                    CurrentItem = currentItem,
                                    IsPrebookCreated = isPrebookCreated,
                                    IsOptimized = isOptimized,
                                    Method = method ?? string.Empty,
                                    Params = new
                                    {
                                        isUpdateDB,
                                        isMultiSupplier = false,
                                        supplierName = supplierName,
                                        isOptimizeTriggeredManually = false,
                                        totalItems,
                                        currentItem
                                    },
                                };

                                _log.LogDynamic(logInfoEntry);
                            }
                            catch (Exception ex)
                            {
                            }

                            #endregion Logging end of item
                        }, cancellationToken);

                        #endregion Request wise processing

                        if (!isOptimizeTriggeredManually && reservationId > 0)
                        {
                            var timeoutTask = Task.Delay((5 * 60 * 1000), cancellationToken);

                            var completedTask = await Task.WhenAny(task, timeoutTask);
                        }
                        else if (!isOptimizeTriggeredManually)
                        {
                            var timeoutTask = Task.Delay((1 * 60 * 1000), cancellationToken);

                            var completedTask = await Task.WhenAny(task, timeoutTask);
                        }
                        else
                        {
                            await Task.WhenAll(task);
                        }
                        lock (_consoleLock)
                        {
                            counter++;
                        }
                        if (repricerId == 29 || repricerId == 36)
                        {
                            Task.Delay(TimeSpan.FromSeconds(5))?.GetAwaiter().GetResult();
                        }
                        else
                        {
                            Task.Delay(TimeSpan.FromSeconds(1))?.GetAwaiter().GetResult();
                        }
                    }
                    );
                    //}
                }

                #endregion Main Processing
            }
            catch (Exception ex)
            {
                #region Exception Logging

                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = "MultiSupplier",

                    Params = $"RePricerId: {repricerId}, ReservationId: {reservationId}"
                };
                _log.Error(irixErrorEntity, ex);
                try
                {
                    //lock (_lock_prebookresult)
                    //{
                    if (resturnResult == null)
                    {
                        resturnResult = new PreBookResults();
                    }
                    if (resturnResult.Error == null)
                    {
                        resturnResult.Error = new Error();
                    }
                    resturnResult.Error.Message = $"{ex.Message}\n{ex.StackTrace}";
                    resturnResult.Error.StatusCode = System.Net.HttpStatusCode.InternalServerError;
                    //}
                }
                catch (Exception ex1)
                {
                }

                #endregion Exception Logging

                //throw;
            }

            #region Finalize

            if (isUpdateDB)
            {
#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
                var itemJobName = $"{repricerId.ToString("000")}_8_2_UPDATE_RRD_MULTI_Supplier".ToUpper();
                try
                {
                    var startTimeItem = DateTime.UtcNow;
                    if (reservationId > 0 && isAnyOptimized)
                    {
                        itemJobName = $"{repricerId.ToString("000")}_{reservationId}_8_2_UPDATE_RRD_MULTI_Supplier".ToUpper();
                        _reservationPersistance.InsertReportinTable(repricerId, reservationId ?? 0);
                        _log.InfoV1(repricerId, _className, itemJobName, $"InsertReportinTable", startTimeItem, true, "end");

                        if (repricerClientDetail.OptimizationType == OptimizationType.Automatic || isOptimizeTriggeredManually)
                        {
                            var refreshTask = Task.Run(() =>
                            {
                                _masterService.RefreshCache(repricerId);
                            });
                        }
                        //else
                        //{
                        //    _masterService.RefreshCache(repricerId);
                        //}
                    }
                    //else if (reservationId == 0)
                    //{
                    //    _log.InfoV1(repricerId, _className, itemJobName, $"InsertReportinTable", startTimeItem, true, "start");
                    //    _reservationPersistence.InsertReportinTable(repricerId);
                    //    _log.InfoV1(repricerId, _className, itemJobName, $"InsertReportinTable", startTimeItem, true, "end");
                    //}

                    //_masterService.RefreshCache(repricerId);
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = itemJobName,
                        RePricerId = repricerId,
                        Params = itemJobName
                    };
                    try
                    {
                        //lock (_lock_prebookresult)
                        //{
                        if (resturnResult == null)
                        {
                            resturnResult = new PreBookResults();
                        }
                        if (resturnResult.Error == null)
                        {
                            resturnResult.Error = new Error();
                        }
                        resturnResult.Error.Message = $"{ex.Message}\n{ex.StackTrace}";
                        resturnResult.Error.StatusCode = System.Net.HttpStatusCode.InternalServerError;
                        //}
                    }
                    catch (Exception ex1)
                    {
                    }
                }

#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
            }

            if (preBookResponseResults != null && preBookResponseResults.Count > 0)
            {
                resturnResult.PreBookResponseResults = preBookResponseResults.ToList();
            }
            if (reservationId == 0)
            {
                _log.InfoV1(repricerId, _className, itemJobNameMAIN, repricerName, startTimeItemMAIN, true, "end");
            }

            #endregion Finalize

            return resturnResult;
        }

        /// <summary>
        /// The UpdateGIATAAndRoomPackageDescription
        /// </summary>
        /// <param name="Repriceruserid">The reservationId<see cref="int"/></param>
        /// <param name="reservationId">The reservationId<see cref="int"/></param>
        /// <param name="isUpdateDB">The isUpdateDB<see cref="bool"/></param>
        public void UpdateGIATAAndRoomPackageDescription(int Repriceruserid, int reservationId = 0, bool isUpdateDB = true)
        {
            var searchcriterias = _reservationPersistance.GetReservationsAsync(Repriceruserid, reservationId).GetAwaiter().GetResult();
            var allowedProviders = _reservationPersistance.GetAllowedProviders(Repriceruserid).GetAwaiter().GetResult();
            searchcriterias = searchcriterias
                                .Where(searchcriteria => allowedProviders
                                .Any(provider => provider.Equals(searchcriteria.supplierName, StringComparison.OrdinalIgnoreCase)))
                                .ToList();
            if (searchcriterias?.Count > 0 && allowedProviders.Count() > 0)
            {
                foreach (var searchcriteria in searchcriterias)
                {
                    var reservationIdInLoop = searchcriteria.ReservationId;
                    var reservationsupplier = searchcriteria.supplierName;

                    if (allowedProviders.Contains(searchcriteria.supplierName))
                    {
                        var searchresult = _searchHelper.SearchSync(Repriceruserid, searchcriteria, true);

                        var prebookcriteriaresult = (_reservationPersistance?.GetPreBookCriteria(reservationIdInLoop, Repriceruserid, true))?.GetAwaiter().GetResult();
                        var prebookcriteria = prebookcriteriaresult?.PreBookCriteriaList;
                        var fisrtPrebookCriteria = prebookcriteria?.FirstOrDefault();

                        var hotelName = fisrtPrebookCriteria?.HotelName;
                        var Destinations = fisrtPrebookCriteria?.Destinations;
                        if (prebookcriteria != null && prebookcriteria.Count() > 0)
                        {
                            foreach (var prebookcriteriaroom in prebookcriteria)
                            {
                                GiataRoomMapping giatadatafororiginalreservation = null;
                                giatadatafororiginalreservation = _giataService.GiataApiCall(Repriceruserid, reservationIdInLoop.ToString(), hotelName, prebookcriteriaroom.RoomName, Destinations, reservationsupplier).GetAwaiter().GetResult();
                                prebookcriteriaroom.giataroommappingdetail = giatadatafororiginalreservation;
                            }
                        }
                        if (searchresult != null && searchresult.hotels.Count() > 0 && prebookcriteria.Any(x => x.giataroommappingdetail != null))
                        {
                            var srk = searchresult?.srk;
                            var hotelindex = searchresult?.hotels[0].index;
                            var tokens = searchresult?.tokens.results;
                            if (prebookcriteria != null && prebookcriteria.Count() > 0)
                            {
                                var offerInfoList = searchresult?.hotels
                                                                  ?.SelectMany(hotel => hotel.offers)
                                                                  ?.Where(offer =>
                                                                      prebookcriteria.All(prebookcriteria =>
                                                                          offer?.rooms?.Any(room =>
                                                                              room != null
                                                                          ) == true)
                                                                  )
                                                                  ?.ToList();

                                if (offerInfoList != null && offerInfoList?.Count > 0)
                                {
                                    foreach (var offerInfo in offerInfoList)
                                    {
                                        var suppliername = offerInfo.System.Code;
                                        var isReservationSupplierOffer = offerInfo.System.Code.Contains(searchcriteria.supplierName);

                                        if (prebookcriteria != null)
                                        {
                                            foreach (var prebookcriterias in prebookcriteria)
                                            {
                                                bool isReservationStatus = false;

                                                if (prebookcriterias != null)
                                                {
                                                    var rooms = new List<SearchRoom>();
                                                    if (isReservationSupplierOffer)
                                                    {
                                                        rooms = offerInfo?.rooms?.Where
                                                          (
                                                                                          room =>

                                                                                          room != null &&  // Add logInfoEntry null check for room
                                                                                          room.name?.ToLower() == prebookcriterias.RoomName?.ToLower()
                                                                                          && (
                                                                                              ((room?.board?.ToLower()?.Trim() ?? string.Empty)?
                                                                                              .Replace(System.Environment.NewLine, " ")?
                                                                                              .Replace("\n", " ")
                                                                                              )
                                                                                              ==
                                                                                              ((prebookcriterias?.RoomBoard?.ToLower()?.Trim() ?? string.Empty)?
                                                                                              .Replace(System.Environment.NewLine, " ")?
                                                                                              .Replace("\n", " "))
                                                                                              )
                                                                                          && (
                                                                                                  ((room?.info?.ToLower()?.Trim() ?? string.Empty)?
                                                                                                  .Replace(System.Environment.NewLine, " ")?
                                                                                                  .Replace("\n", " "))
                                                                                                  ==
                                                                                                   ((prebookcriterias?.RoomInfo?.ToLower()?.Trim() ?? string.Empty)?
                                                                                                  .Replace(System.Environment.NewLine, " ")?
                                                                                                  .Replace("\n", " "))
                                                                                              )
                                                                                      )
                                                                                      ?.ToList();
                                                    }
                                                    else
                                                    {
                                                        rooms = offerInfo?.rooms
                                                                                      ?.Where(room =>
                                                                                          room != null
                                                                                      )
                                                                                      ?.ToList();
                                                        foreach (var room in rooms)
                                                        {
                                                            GiataRoomMapping giatadataforSearchreservation = null;

                                                            giatadataforSearchreservation = _giataService.GiataApiCall(Repriceruserid, reservationIdInLoop.ToString(), hotelName, room.name, Destinations, suppliername).GetAwaiter().GetResult();
                                                        }
                                                    }

                                                    if (rooms != null && rooms.Count() > 0)
                                                    {
                                                        var roomindex = rooms?.Select(item => item.index.ToString())
                                                                        ?.ToList();

                                                        var roomindexs = string.Join(",", roomindex);

                                                        var resp = _irixAdapter.GetMultipleRoomsDetails(searchcriteria.ReservationId, tokens, Repriceruserid, srk, offerInfo.id, hotelindex, roomindexs);
                                                        var res = MultiSupplierRoomLists(searchcriteria.ReservationId, Repriceruserid, resp, rooms, offerInfo.System.Alias);
                                                        if (res != null && res.Count() > 0)
                                                        {
                                                            if (isReservationSupplierOffer && isReservationStatus == false)
                                                            {
                                                                res = res.OrderBy(x => x.IssueNetPrice).ToList();
                                                                _reservationPersistance.InsertOrUpdateMultiSupplierReservationRoom(res?.FirstOrDefault());
                                                                isReservationStatus = true;
                                                            }
                                                            else if (isReservationSupplierOffer == false)
                                                            {
                                                                foreach (var result in res)
                                                                {
                                                                    _reservationPersistance.InsertOrUpdateMultiSupplierSearchRoom(result);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            RedisCacheHelper.DeleteKeysContaining("GetMultiSupplierResponsesAsync_");
        }

        /// <summary>
        /// The MultiSupplierRoomLists
        /// </summary>
        /// <param name="ReservationId">The ReservationId<see cref="int"/></param>
        /// <param name="RepricerId">The RepricerId<see cref="int"/></param>
        /// <param name="multipleRoomsResponse">The multipleRoomsResponse<see cref="MultipleRoomsResponse"/></param>
        /// <param name="rooms">The rooms<see cref="List{SearchRoom}"/></param>
        /// <param name="Supplier">The Supplier<see cref="string"/></param>
        /// <returns>The <see cref="List{MultiSupplierRoomList}"/></returns>
        public List<MultiSupplierRoomList> MultiSupplierRoomLists(int ReservationId, int RepricerId, MultipleRoomsResponse multipleRoomsResponse, List<SearchRoom> rooms, string Supplier)
        {
            var multiSupplierRoomList = new List<MultiSupplierRoomList>();

            try
            {
                if (multipleRoomsResponse.Rooms != null)
                {
                    foreach (var multipleRoom in multipleRoomsResponse.Rooms)
                    {
                        try
                        {
                            var searchRoom = rooms.FirstOrDefault(r => r.index == multipleRoom.index.ToString());

                            if (searchRoom != null)
                            {
                                var multiSupplierRoom = new MultiSupplierRoomList
                                {
                                    RepricerId = RepricerId,
                                    ReservationId = ReservationId,
                                    RoomName = searchRoom.name,
                                    RoomBoard = searchRoom.board,
                                    RoomInfo = searchRoom.info,
                                    RoomCode = searchRoom.index,
                                    Descriptions = multipleRoom.Description,
                                    FacilitiesDescription = multipleRoom.FacilitiesDescription,
                                    RoomStatus = searchRoom.status,
                                    RoomBoardBasis = searchRoom.boardBasis,
                                    IssueNetPrice = searchRoom.price.components.supplier.value,
                                    IssueCurrency = searchRoom.price.components.supplier.currency,
                                    CancellationDate = searchRoom.cancellationDeadline?.date.ToString(constants.datetypeDB),
                                    CancellationCurrency = searchRoom.cancellationDeadline?.charge?.currency,
                                    CancellationCharge = searchRoom?.cancellationDeadline?.charge?.value,
                                    RoomRateTags = searchRoom?.rateTags != null ? string.Join(", ", searchRoom.rateTags) : string.Empty,
                                    SupplierName = Supplier,
                                    Images = multipleRoom.Images?.Select(i => i?.Url).ToList()
                                };

                                multiSupplierRoomList.Add(multiSupplierRoom);
                            }
                        }
                        catch (Exception ex)
                        {
                        }
                    }
                    if (multiSupplierRoomList != null && multiSupplierRoomList.Count() > 0)
                    {
                        multiSupplierRoomList = multiSupplierRoomList?
                                               .GroupBy(r => new { r.RoomName, r.RoomBoard, r.RoomInfo })?
                                               .Select(g => g.OrderBy(r => r.IssueNetPrice).FirstOrDefault())?
                                               .ToList();
                    }
                }
            }
            catch (Exception ex)
            {
            }

            return multiSupplierRoomList;
        }

        /// <summary>
        /// The GetMultiSupplierResponsesAsync
        /// </summary>
        /// <param name="rePricerId">The reservationId<see cref="int"/></param>
        /// <param name="reservationId">The reservationId<see cref="int"/></param>
        /// <param name="PageSize">The PageSize<see cref="int"/></param>
        /// <param name="PageNumber">The PageNumber<see cref="int"/></param>
        /// <param name="iscaching">The iscaching<see cref="bool"/></param>
        /// <returns>The <see cref="Task{MultiSupplierDetail}"/></returns>
        public async Task<MultiSupplierDetail> GetMultiSupplierResponsesAsync(int rePricerId, int reservationId = 0, int PageSize = 20, int PageNumber = 1, bool iscaching = true)
        {
            var multisupplierresponses = new List<MultiSupplierResponse>();
            var multiSupplierDetail = new MultiSupplierDetail();

            try
            {
                var key = $"GetMultiSupplierResponsesAsync_{reservationId}_{rePricerId}_{PageSize}_{PageNumber}";
                if (RedisCacheHelper.KeyExists(key) && iscaching)
                {
                    multiSupplierDetail = RedisCacheHelper.Get<MultiSupplierDetail>(key);
                }
                if (multiSupplierDetail != null && multiSupplierDetail.pageSize > 0)
                {
                    return multiSupplierDetail;
                }
                var multiSupplierRooms = _reservationPersistance.GetMultiSupplierRoomDetailsResp(rePricerId, reservationId, PageSize, PageNumber);

                var distinctReservationIds = multiSupplierRooms?.search
                    ?.Select(room => room.ReservationId)
                    ?.Distinct()
                    ?.ToList();

                if (distinctReservationIds != null && distinctReservationIds.Count > 0)
                {
                    foreach (var reservationid in distinctReservationIds)
                    {
                        var multisupplierresponse = new MultiSupplierResponse();
                        var multisupplier = multiSupplierRooms.search.Where(x => x.ReservationId == reservationid).ToList();
                        var multisupplierreservationinfo = multisupplier?.FirstOrDefault();
                        var prebookcriteriaresult = multiSupplierRooms.reservation.Where(x => x.ReservationId == reservationid).ToList();
                        if (prebookcriteriaresult != null && prebookcriteriaresult.Count() > 0)
                        {
                            multisupplierresponse.ReservationId = reservationid;
                            multisupplierresponse.RepricerId = rePricerId;
                            multisupplierresponse.CheckIn = prebookcriteriaresult?.FirstOrDefault()?.CheckIn;
                            multisupplierresponse.CheckOut = prebookcriteriaresult?.FirstOrDefault()?.CheckOut;

                            multisupplierresponse.Reservation = ConvertToMultiSupplierReservationResponse(prebookcriteriaresult, rePricerId, reservationid.ToString());
                            multisupplierresponse.Search = ConvertToMultiSupplierSearchResponse(multisupplier, rePricerId, reservationid.ToString());
                        }
                        if (multisupplierresponse != null && multisupplierresponse?.Reservation?.Count() > 0 && multisupplierresponse?.Search?.Count() > 0)
                        {
                            multisupplierresponses.Add(multisupplierresponse);
                        }
                    }
                }

                multiSupplierDetail = new MultiSupplierDetail
                {
                    pageSize = multisupplierresponses.Count(),
                    pageNumber = PageNumber,
                    totalRows = multiSupplierRooms.distinctreservationcount,
                    totalPages = (int)Math.Ceiling((double)multiSupplierRooms.distinctreservationcount / PageSize),
                    multisupplierrooms = multisupplierresponses
                };

                RedisCacheHelper.Set(key, multiSupplierDetail, TimeSpan.FromDays(6));
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetMultiSupplierResponsesAsync),
                };
                _log.Error(irixErrorEntity, ex);
            }
            return multiSupplierDetail;
        }

        /// <summary>
        /// The ConvertToMultiSupplierSearchResponse
        /// </summary>
        /// <param name="multiSupplierRooms">The multiSupplierRooms<see cref="List{MultiSupplierRoomDetails}"/></param>
        /// <param name="RepricerId">The RepricerId<see cref="int"/></param>
        /// <param name="reservationid">The reservationid<see cref="string"/></param>
        /// <returns>The <see cref="List{MultiSupplierReservationResponse}"/></returns>
        private List<MultiSupplierReservationResponse> ConvertToMultiSupplierSearchResponse(List<MultiSupplierRoomDetails> multiSupplierRooms, int RepricerId, string reservationid)
        {
            try
            {
                return multiSupplierRooms.Select(room => new MultiSupplierReservationResponse
                {
                    Id = room.Id,
                    RoomCode = room.RoomCode,
                    //Content = $"{room.RoomName},{room.RoomBoard},{room.RoomInfo},{room.HotelName},{room.Destinations}",
                    RoomName = room.RoomName,
                    RoomBoard = room.RoomBoard,
                    RoomInfo = room.RoomInfo,
                    HotelName = room.HotelName,
                    Descriptions = room?.Descriptions,
                    FacilitiesDescription = room?.FacilitiesDescription,
                    Destination = room?.Destinations,
                    SupplierValue = room?.IssueNetPrice,
                    SupplierCurrency = room?.IssueCurrency,
                    AdultCount = room.PassengerCount,
                    ChildAges = room?.ChildAges,
                    Supplier = room?.SupplierName,
                    MappingRoomId = room?.MappingRoomId ?? 0,
                    giatamappingdetails = _giataService.GiataApiCall(RepricerId, reservationid, room?.HotelName, room?.RoomName, room?.Destinations).GetAwaiter().GetResult()
                }).ToList();
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(ConvertToMultiSupplierSearchResponse),
                };
                _log.Error(irixErrorEntity, ex);
                return new List<MultiSupplierReservationResponse>();
            }
        }

        /// <summary>
        /// The ConvertToMultiSupplierReservationResponse
        /// </summary>
        /// <param name="preBookCriterias">The preBookCriterias<see cref="List{MultiSupplierRoomDetails}"/></param>
        /// <param name="RepricerId">The RepricerId<see cref="int"/></param>
        /// <param name="reservationid">The reservationid<see cref="string"/></param>
        /// <returns>The <see cref="List{MultiSupplierReservationResponse}"/></returns>
        private List<MultiSupplierReservationResponse> ConvertToMultiSupplierReservationResponse(List<MultiSupplierRoomDetails> preBookCriterias, int RepricerId, string reservationid)
        {
            try
            {
                var supplier = preBookCriterias?.FirstOrDefault()?.SupplierName;
                return preBookCriterias.Select(preBookCriteria => new MultiSupplierReservationResponse
                {
                    Id = preBookCriteria.Id,
                    RoomCode = preBookCriteria?.RoomCode,
                    //Content = $"{preBookCriteria.RoomName},{preBookCriteria.RoomBoard},{preBookCriteria.RoomInfo},{preBookCriteria.HotelName},{preBookCriteria.Destinations}",
                    RoomName = preBookCriteria?.RoomName,
                    RoomBoard = preBookCriteria?.RoomBoard,
                    RoomInfo = preBookCriteria?.RoomInfo,
                    HotelName = preBookCriteria?.HotelName,
                    Destination = preBookCriteria?.Destinations,
                    SupplierValue = preBookCriteria?.IssueNetPrice,
                    SupplierCurrency = preBookCriteria?.IssueCurrency,
                    AdultCount = preBookCriteria.PassengerCount,
                    ChildAges = preBookCriteria?.ChildAges,
                    Supplier = supplier,
                    Descriptions = preBookCriteria?.Descriptions,
                    FacilitiesDescription = preBookCriteria?.FacilitiesDescription,
                    MappingRoomId = preBookCriteria?.MappingRoomId ?? 0,
                    CancellationDate = preBookCriteria?.CancellationDate,
                    CancellationChargeValue = preBookCriteria?.CancellationChargeValue,
                    CancellationChargeCurrency = preBookCriteria?.CancellationChargeCurrency,
                    giatamappingdetails = _giataService.GiataApiCall(RepricerId, reservationid, preBookCriteria?.HotelName, preBookCriteria?.RoomName, preBookCriteria?.Destinations).GetAwaiter().GetResult()
                }).ToList();
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(ConvertToMultiSupplierReservationResponse),
                };
                _log.Error(irixErrorEntity, ex);
                return new List<MultiSupplierReservationResponse>();
            }
        }

        private string NormalizeString(string input)
        {
            return (input?.ToLower()?.Trim() ?? string.Empty)
                ?.Replace(Environment.NewLine, " ")
                ?.Replace("\n", " ") ?? string.Empty;
        }

        private GiataRoomMapping FetchGiataMapping(int repricerId, string reservationId, string hotelName, string roomName, string destinations, string supplierCode)
        {
            try
            {
                return _giataService.GiataApiCall(repricerId, reservationId, hotelName, roomName, destinations, supplierCode).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                // Log the error if needed
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetMultiSupplierResponsesAsync),
                    Params = $"Giata API call failed for reservationId: {reservationId}"
                };
                _log.Error(irixErrorEntity, ex);
                return null;
            }
        }

        private SearchRoom_Package CreateRoomPackageInfo(int repricerUserId, int reservationId, SearchRoom room, PrebookCriteria criteria, string supplierCode, string boardGroupName)
        {
            SearchRoom_Package searchRoom_Package = null;
            try
            {
                if (criteria.giataroommappingdetail == null)
                {
                    criteria.giataroommappingdetail = FetchGiataMapping(repricerUserId, reservationId.ToString(), criteria.HotelName, criteria.RoomName, criteria.Destinations, supplierCode);
                }
                var giataMappingSearch = FetchGiataMapping(repricerUserId, reservationId.ToString(), criteria.HotelName, room.name, criteria.Destinations, supplierCode);
                var giataMappingRes = criteria.giataroommappingdetail;

                //if (giataMappingRes != null)
                //{
                //    giataMappingRes.RepricerID = repricerUserId;
                //    giataMappingRes.Supplier = supplierCode;
                //    giataMappingRes.ReservationId = reservationId.ToString();
                //}
                //if (giataMappingSearch != null)
                //{
                //    giataMappingSearch.RepricerID = repricerUserId;
                //    giataMappingSearch.Supplier = supplierCode;
                //    giataMappingSearch.ReservationId = reservationId.ToString();
                //}

                if (giataMappingRes != null
                    && giataMappingSearch != null
                    && giataMappingRes != null
                    && !string.IsNullOrEmpty(giataMappingSearch?.GroupName ?? string.Empty)
                    && !string.IsNullOrEmpty(giataMappingRes?.GroupName ?? string.Empty)
                    && giataMappingSearch?.GroupName?.ToLower().Trim() == giataMappingRes?.GroupName?.ToLower().Trim()
                )
                {
                    searchRoom_Package = new SearchRoom_Package
                    {
                        RoomIndex = room.index,
                        RoomName = room.name,
                        RoomBoard = room.board,
                        RoomInfo = room.info,
                        PassengerCount = criteria.passengerCount,
                        ChildAges = _searchserviceHelper.ExtractIntegers(criteria.ChildAges),
                        SearchRoomPrice = room.price?.components?.supplier?.value,
                        SearchRoomCurrency = room.price?.components?.supplier?.currency,
                        NonRefundable = room.nonRefundable,
                        GiataMappingRoomDetail = giataMappingRes,
                        RoomBoardGroupName = boardGroupName,
                        GiataGroupName = giataMappingRes.GroupName,
                    };
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(CreateRoomPackageInfo),
                    Params = $"RePricerId: {repricerUserId}, ReservationId: {reservationId}, RoomName: {room?.name}"
                };
                _log.Error(irixErrorEntity, ex);
            }
            return searchRoom_Package;
        }

        private List<SearchRoom> GetRoomsMatchingCriteria(OfferInfo offerInfo, PrebookCriteria criteria, BoardMatchEnum boardMatchEnum = BoardMatchEnum.BoardMatch_YES)
        {
            var rooms = offerInfo?.rooms
                ?.Where(room => room != null && NormalizeString(room.board) == NormalizeString(criteria.RoomBoard))
                ?.ToList();

            if (boardMatchEnum == BoardMatchEnum.BoardMatch_NO)
            {
                rooms = offerInfo?.rooms
                    ?.Where(room => room != null)
                    ?.ToList();
            }

            if (boardMatchEnum == BoardMatchEnum.BoardMatch_GROUP)
            {
                if (string.IsNullOrWhiteSpace(criteria.RoomBoardGroupName))
                {
                    criteria.RoomBoardGroupName = _masterService.GetBoardGroupName(criteria.RoomBoard);
                }
                rooms = offerInfo?.rooms
                    ?.Where(room => room != null
                            && !string.IsNullOrWhiteSpace(_masterService.GetBoardGroupName(room.board))
                            && !string.IsNullOrWhiteSpace(criteria.RoomBoardGroupName)
                            && _masterService.GetBoardGroupName(room.board) == criteria.RoomBoardGroupName

                            )
                    ?.ToList();
            }

            // Handle case when RoomBoard is empty
            if (string.IsNullOrEmpty(criteria.RoomBoard))
            {
                rooms = rooms?.Where(room => string.IsNullOrEmpty(room?.board))?.ToList();
            }

            return rooms ?? new List<SearchRoom>();
        }

        private bool UpdateMatchingRoomOffer(
             List<SearchRoom_Package> mainRoomInfoList,
             List<SearchRoom_Package> matchedRoomInfoList,
             OfferInfo offerInfo,
             List<PrebookCriteria> prebookCriterias,
             List<string> selectedRoomTokens,
             ref Package packageInfo,
             int rePricerId,
             int reservationId,
             List<DashboardReportResponseRow> activeTabDashboard,
             BoardMatchEnum boardMatchEnum = BoardMatchEnum.BoardMatch_NO
        )
        {
            bool isRoomFound = false;

            try
            {
                mainRoomInfoList.Clear();
                matchedRoomInfoList.Clear();
                selectedRoomTokens.Clear();
                IEnumerable<PackageInfoResult> packageInfoQuery = new List<PackageInfoResult>();
                var supplierCode = offerInfo?.System?.Code;
                if (string.IsNullOrWhiteSpace(supplierCode) == false)
                {
                    foreach (var criteria in prebookCriterias)
                    {
                        if (criteria != null)
                        {
                            if (string.IsNullOrWhiteSpace(criteria.RoomBoardGroupName))
                            {
                                criteria.RoomBoardGroupName = _masterService.GetBoardGroupName(criteria.RoomBoard);
                            }
                            if (criteria.giataroommappingdetail == null)
                            {
                                criteria.giataroommappingdetail = FetchGiataMapping(rePricerId, reservationId.ToString(), criteria.HotelName, criteria.RoomName, criteria.Destinations, supplierCode);
                            }
                            var rooms = GetRoomsMatchingCriteria(offerInfo, criteria, boardMatchEnum);

                            if (rooms != null && rooms?.Count > 0)
                            {
                                foreach (var room in rooms)
                                {
                                    var roomBoardGroup = _masterService.GetBoardGroupName(room.board);
                                    var roomInfo = CreateRoomPackageInfo(rePricerId, reservationId, room, criteria, offerInfo.System.Code, roomBoardGroup);

                                    if (roomInfo != null && !mainRoomInfoList.Contains(roomInfo))
                                    {
                                        mainRoomInfoList.Add(roomInfo);
                                    }
                                }
                            }
                        }
                    }
                    if (boardMatchEnum == BoardMatchEnum.BoardMatch_YES)
                    {
                        packageInfoQuery = from pack in offerInfo?.packages
                                           from matchedRoom in mainRoomInfoList
                                           from pr in pack?.PackageRooms
                                           from rr in pr?.roomReferences
                                           from criteria in prebookCriterias
                                           let criteriaChildAges = criteria.ChildAges == null || criteria.ChildAges == "[]"
                                           ? new List<int>()
                                           : _searchserviceHelper.ExtractIntegers(criteria.ChildAges).ToList() // Ensure null is treated as empty list
                                           let packageRoomChildrenAges = pr?.occupancy?.childrenAges?.ToList() ?? new List<int>()
                                           let matchedRoomChildrenAges = matchedRoom.ChildAges?.ToList() ?? new List<int>()

                                           where pack != null
                                               && pack.Price?.components?.supplier != null
                                               && pr != null
                                               && rr != null
                                               && rr.roomCode.ToString() == matchedRoom.RoomIndex
                                               && criteria.passengerCount == matchedRoom.PassengerCount
                                               && criteria.passengerCount == pr.occupancy.adults
                                               && (
                                                    criteriaChildAges.Count == 0
                                                    || (
                                                            criteriaChildAges.SequenceEqual(packageRoomChildrenAges)
                                                            && criteriaChildAges.SequenceEqual(matchedRoomChildrenAges)
                                                        )
                                                   )

                                                   && NormalizeString(criteria.giataroommappingdetail.GroupName)
                                                    == NormalizeString(matchedRoom.GiataMappingRoomDetail.GroupName)

                                                   && NormalizeString(matchedRoom.RoomBoard) == NormalizeString(criteria?.RoomBoard)
                                                   && !string.IsNullOrEmpty(NormalizeString(criteria?.giataroommappingdetail?.GroupName ?? string.Empty))
                                                   && !string.IsNullOrEmpty(NormalizeString(matchedRoom?.GiataMappingRoomDetail?.GroupName ?? string.Empty))
                                                   && !string.IsNullOrEmpty(NormalizeString(criteria?.RoomBoard ?? string.Empty))
                                                   && !string.IsNullOrEmpty(NormalizeString(matchedRoom?.RoomBoard ?? string.Empty))
                                           orderby (matchedRoom.SearchRoomPrice > 0 ? matchedRoom.SearchRoomPrice
                                                   : pack.Price.components.supplier.value) ascending
                                           select new PackageInfoResult
                                           {
                                               PackageToken = pack.PackageToken,
                                               MatchedRoom = matchedRoom,
                                               RoomReference = rr,
                                               PackageRoomCode = pr.packageRoomCode,
                                               OfferPackage = new Package
                                               {
                                                   OfferId = offerInfo?.id,
                                                   Supplier = offerInfo?.System?.Code,
                                                   Complete = pack.Complete,
                                                   PackageCode = pack.PackageCode,
                                                   PackageRooms = pack.PackageRooms,
                                                   PackageToken = pack.PackageToken,
                                                   Price = pack.Price,
                                               }
                                           };
                    }
                    else
                        if (boardMatchEnum == BoardMatchEnum.BoardMatch_GROUP)
                    {
                        packageInfoQuery = from pack in offerInfo?.packages
                                           from matchedRoom in mainRoomInfoList
                                           from pr in pack?.PackageRooms
                                           from rr in pr?.roomReferences
                                           from criteria in prebookCriterias
                                           let criteriaChildAges = criteria.ChildAges == null || criteria.ChildAges == "[]"
                                           ? new List<int>()
                                           : _searchserviceHelper.ExtractIntegers(criteria.ChildAges).ToList() // Ensure null is treated as empty list
                                           let packageRoomChildrenAges = pr?.occupancy?.childrenAges?.ToList() ?? new List<int>()
                                           let matchedRoomChildrenAges = matchedRoom.ChildAges?.ToList() ?? new List<int>()

                                           where pack != null
                                               && pack.Price?.components?.supplier != null
                                               && pr != null
                                               && rr != null
                                               && rr.roomCode.ToString() == matchedRoom.RoomIndex
                                               && criteria.passengerCount == matchedRoom.PassengerCount
                                               && criteria.passengerCount == pr.occupancy.adults
                                               && (criteriaChildAges.Count == 0
                                                   || (criteriaChildAges.SequenceEqual(packageRoomChildrenAges)
                                                   && criteriaChildAges.SequenceEqual(matchedRoomChildrenAges)))
                                               && NormalizeString(_masterService.GetBoardGroupName(matchedRoom?.RoomBoard))
                                                    == NormalizeString(_masterService.GetBoardGroupName(criteria?.RoomBoard))

                                                    && NormalizeString(criteria.giataroommappingdetail.GroupName)
                                                    == NormalizeString(matchedRoom.GiataMappingRoomDetail.GroupName)
                                                && !string.IsNullOrEmpty(NormalizeString(criteria?.giataroommappingdetail?.GroupName ?? string.Empty))
                                                && !string.IsNullOrEmpty(NormalizeString(matchedRoom?.GiataMappingRoomDetail?.GroupName ?? string.Empty))
                                                && !string.IsNullOrEmpty(NormalizeString(criteria?.RoomBoard ?? string.Empty))
                                                && !string.IsNullOrEmpty(NormalizeString(matchedRoom?.RoomBoard ?? string.Empty))
                                           orderby (matchedRoom.SearchRoomPrice > 0 ? matchedRoom.SearchRoomPrice
                                                   : pack.Price.components.supplier.value) ascending

                                           select new PackageInfoResult
                                           {
                                               PackageToken = pack.PackageToken,
                                               MatchedRoom = matchedRoom,
                                               RoomReference = rr,
                                               PackageRoomCode = pr.packageRoomCode,
                                               OfferPackage = new Package
                                               {
                                                   OfferId = offerInfo?.id,
                                                   Supplier = offerInfo?.System?.Code,
                                                   Complete = pack.Complete,
                                                   PackageCode = pack.PackageCode,
                                                   PackageRooms = pack.PackageRooms,
                                                   PackageToken = pack.PackageToken,
                                                   Price = pack.Price,
                                               }
                                           };
                    }
                    else
                    if (boardMatchEnum == BoardMatchEnum.BoardMatch_NO)
                    {
                        packageInfoQuery = from pack in offerInfo?.packages
                                           from matchedRoom in mainRoomInfoList
                                           from pr in pack?.PackageRooms
                                           from rr in pr?.roomReferences
                                           from criteria in prebookCriterias
                                           let criteriaChildAges = criteria.ChildAges == null || criteria.ChildAges == "[]"
                                           ? new List<int>()
                                           : _searchserviceHelper.ExtractIntegers(criteria.ChildAges).ToList() // Ensure null is treated as empty list
                                           let packageRoomChildrenAges = pr?.occupancy?.childrenAges?.ToList() ?? new List<int>()
                                           let matchedRoomChildrenAges = matchedRoom.ChildAges?.ToList() ?? new List<int>()

                                           where pack != null
                                               && pack.Price?.components?.supplier != null
                                               && pr != null
                                               && rr != null
                                               && rr.roomCode.ToString() == matchedRoom.RoomIndex
                                               && criteria.passengerCount == matchedRoom.PassengerCount
                                               && criteria.passengerCount == pr.occupancy.adults
                                               && (criteriaChildAges.Count == 0
                                                   || (criteriaChildAges.SequenceEqual(packageRoomChildrenAges)
                                                   && criteriaChildAges.SequenceEqual(matchedRoomChildrenAges)))
                                                && NormalizeString(criteria.giataroommappingdetail.GroupName)
                                                    == NormalizeString(matchedRoom.GiataMappingRoomDetail.GroupName)
                                                && !string.IsNullOrEmpty(NormalizeString(criteria?.giataroommappingdetail?.GroupName ?? string.Empty))
                                                && !string.IsNullOrEmpty(NormalizeString(matchedRoom?.GiataMappingRoomDetail?.GroupName ?? string.Empty))
                                                && !string.IsNullOrEmpty(NormalizeString(criteria?.RoomBoard ?? string.Empty))
                                                && !string.IsNullOrEmpty(NormalizeString(matchedRoom?.RoomBoard ?? string.Empty))

                                           orderby (matchedRoom.SearchRoomPrice > 0 ? matchedRoom.SearchRoomPrice
                                                   : pack.Price.components.supplier.value) ascending

                                           select new PackageInfoResult
                                           {
                                               PackageToken = pack.PackageToken,
                                               MatchedRoom = matchedRoom,
                                               RoomReference = rr,
                                               PackageRoomCode = pr.packageRoomCode,
                                               OfferPackage = new Package
                                               {
                                                   OfferId = offerInfo?.id,
                                                   Supplier = offerInfo?.System?.Code,
                                                   Complete = pack.Complete,
                                                   PackageCode = pack.PackageCode,
                                                   PackageRooms = pack.PackageRooms,
                                                   PackageToken = pack.PackageToken,
                                                   Price = pack.Price,
                                               }
                                           };
                    }

                    packageInfo = packageInfoQuery
                                    ?.OrderBy(p => (p?.MatchedRoom?.SearchRoomPrice) ?? p?.OfferPackage?.Price.components?.supplier?.value)
                                    ?.FirstOrDefault()
                                    ?.OfferPackage;

                    var pi5 = packageInfoQuery?.OrderBy(x => x.MatchedRoom.SearchRoomPrice)
                                    ?.Select(y => new
                                    {
                                        y.MatchedRoom.RoomIndex,
                                        y.MatchedRoom.RoomName,
                                        y.MatchedRoom.RoomBoard,
                                        y.MatchedRoom.RoomBoardGroupName,
                                        GiataGroupName = y.MatchedRoom.GiataMappingRoomDetail.GroupName,
                                        y.MatchedRoom.RoomInfo,
                                        y.MatchedRoom.PassengerCount,
                                        y.MatchedRoom.ChildAges,
                                        y.MatchedRoom.SearchRoomPrice,
                                        y.MatchedRoom.NonRefundable,
                                        y.MatchedRoom.SearchRoomCurrency,
                                        y.RoomReference.selected,
                                        y.RoomReference.roomToken,
                                        y.RoomReference.roomCode,
                                        y.PackageRoomCode,
                                        y.OfferPackage.PackageToken,
                                        y.MatchedRoom.GiataMappingRoomDetail,
                                    }
                                    )?.Distinct().ToList();

                    if (pi5 != null && pi5.Count > 0)
                    {
                        if (prebookCriterias != null && prebookCriterias.Count > 0 && pi5 != null && pi5.Count > 0)
                        {
                            try
                            {
                                // Group rooms by PackageToken
                                var roomsByPackage = pi5.GroupBy(r => r.PackageToken)
                                                        .Select(g => new
                                                        {
                                                            PackageToken = g.Key,
                                                            Rooms = g.ToList(),
                                                            RoomCount = g.Count()
                                                        })
                                                        .ToList();

                                // Filter packages that have the exact number of rooms matching the prebookCriteria
                                var matchingPackages = roomsByPackage
                                                        .Where(p => p.RoomCount == prebookCriterias.Count)
                                                        .Where(p =>
                                                        {
                                                            // Group rooms by room name and board to count them
                                                            var roomGroups = p.Rooms
                                                                .GroupBy(r => new
                                                                {
                                                                    RoomName = NormalizeString(r.GiataGroupName),
                                                                    RoomBoard = NormalizeString(r.RoomBoardGroupName)
                                                                })
                                                                .ToDictionary(g => g.Key, g => g.Count());

                                                            // Group prebookCriteria by room name and board to count them
                                                            var criteriaGroups = prebookCriterias
                                                                .GroupBy(pc => new
                                                                {
                                                                    RoomName = NormalizeString(pc.giataroommappingdetail.GroupName),
                                                                    RoomBoard = NormalizeString(pc.RoomBoardGroupName)
                                                                })
                                                                .ToDictionary(g => g.Key, g => g.Count());

                                                            // Check if the counts match for each room type
                                                            return roomGroups.Count == criteriaGroups.Count &&
                                                                   roomGroups.All(rg =>
                                                                       criteriaGroups.TryGetValue(rg.Key, out int count) &&
                                                                       count == rg.Value);
                                                        })
                                                        .Select(p => p.PackageToken)
                                                        .ToList();

                                // Filter the original rooms list to only include rooms from matching packages
                                if (matchingPackages.Count > 0)
                                {
                                    pi5 = pi5.Where(r => matchingPackages.Contains(r.PackageToken)).ToList();
                                    packageInfo = packageInfoQuery?.FirstOrDefault(x => x.PackageToken == pi5?.FirstOrDefault()?.PackageToken)?.OfferPackage;
                                }
                                else
                                {
                                    pi5 = null;
                                    packageInfo = null;
                                }
                            }
                            catch (Exception ex)
                            {
                                // Log the error but continue with the original list
                                var irixErrorEntity = new IrixErrorEntity
                                {
                                    ClassName = _className,
                                    MethodName = nameof(UpdateMatchingRoomOffer),
                                    Params = $"Error filtering packages: {ex.Message}"
                                };
                                _log.Error(irixErrorEntity, ex);
                            }
                        }
                        foreach (var ct in prebookCriterias)
                        {
                            var selectedList = pi5
                                ?.Where(x => x?.GiataMappingRoomDetail?.GroupName?.ToLower()?.Trim() == ct?.giataroommappingdetail?.GroupName?.ToLower()?.Trim()
                                    && x?.RoomBoardGroupName?.ToLower()?.Trim() == ct?.RoomBoardGroupName?.ToLower()?.Trim()
                                    )
                                ?.ToList();

                            if (selectedList?.Count > 0)
                            {
                                foreach (var selected in selectedList)
                                {
                                    if (selected != null)
                                    {
                                        if (matchedRoomInfoList.Any(x =>
                                         x?.PackageRoomCode == selected?.PackageRoomCode
                                        ) == false)
                                        {
                                            try
                                            {
                                                var room = new SearchRoom_Package
                                                {
                                                    ChildAges = selected.ChildAges,
                                                    RoomName = selected.RoomName,
                                                    PassengerCount = selected.PassengerCount,
                                                    NonRefundable = selected.NonRefundable,
                                                    RoomBoard = selected.RoomBoard,
                                                    RoomIndex = selected.RoomIndex,
                                                    RoomInfo = selected.RoomInfo,
                                                    SearchRoomCurrency = selected.SearchRoomCurrency,
                                                    SearchRoomPrice = selected.SearchRoomPrice,
                                                    PackageRoomCode = selected.PackageRoomCode,
                                                    RoomToken = selected.roomToken,
                                                    PackageToken = selected.PackageToken,
                                                    RoomBoardGroupName = selected.RoomBoardGroupName,
                                                    GiataGroupName = selected.GiataGroupName,
                                                    GiataMappingRoomDetail = selected.GiataMappingRoomDetail,
                                                };

                                                if (matchedRoomInfoList.Any(x =>
                                                    x?.PackageRoomCode == room?.PackageRoomCode
                                                    && x?.RoomName == room?.RoomName
                                                    && x?.RoomIndex == room?.RoomIndex
                                                    && x?.RoomToken == room?.RoomToken
                                                   ) == false)
                                                {
                                                    matchedRoomInfoList.Add(room);
                                                    selectedRoomTokens.Add(selected.roomToken);
                                                    isRoomFound = true;
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                var irixErrorEntity = new IrixErrorEntity
                                                {
                                                    ClassName = _className,
                                                    MethodName = nameof(UpdateMatchingRoomOffer),
                                                    Params = $"RePricerId: {rePricerId}, ReservationId: {reservationId}, RoomName: {selected?.RoomName}"
                                                };
                                                mainRoomInfoList = new List<SearchRoom_Package>();
                                                matchedRoomInfoList = new List<SearchRoom_Package>();
                                                offerInfo = new OfferInfo();
                                                selectedRoomTokens = new List<string>();
                                                _log.Error(irixErrorEntity, ex);
                                                isRoomFound = false;
                                            }
                                            break;
                                        }
                                    }
                                }
                            }
                        }

                        if (mainRoomInfoList?.All(room => room?.SearchRoomPrice != null && room?.SearchRoomPrice != 0) == true)
                        {
                            mainRoomInfoList = mainRoomInfoList?.OrderBy(x => x?.SearchRoomPrice)?.ToList();
                        }
                        if (matchedRoomInfoList?.All(room => room?.SearchRoomPrice != null && room?.SearchRoomPrice != 0) == true)
                        {
                            matchedRoomInfoList = matchedRoomInfoList?.OrderBy(x => x?.SearchRoomPrice)?.ToList();
                        }
                    }
                }
                ;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(UpdateMatchingRoomOffer),
                    Params = $"RePricerId: {rePricerId}, ReservationId: {reservationId}"
                };

                mainRoomInfoList = new List<SearchRoom_Package>();
                matchedRoomInfoList = new List<SearchRoom_Package>();
                selectedRoomTokens = new List<string>();
                _log.Error(irixErrorEntity, ex);
            }

            return isRoomFound;
        }

        private List<OfferInfo> GetOfferListFromSearchResponse
        (
            SearchResponseFromAPI searchResponse,
            List<PrebookCriteria> prebookCriterias,
            int rePricerId,
            int reservationId,
            BoardMatchEnum boardMatchEnum
        )
        {
            List<OfferInfo> offerInfoList = null;
            try
            {
                if (boardMatchEnum == BoardMatchEnum.BoardMatch_YES)
                {
                    var offerInfoListQuery = from hotels in searchResponse?.hotels
                                             from offer in hotels.offers
                                             from room in offer.rooms
                                             from package in offer.packages
                                             from packageRoom in package.PackageRooms
                                             from roomReference in packageRoom.roomReferences
                                             from criteria in prebookCriterias.Where(x => x != null && x.ChildAges != null)?.ToList()

                                             let normalizedCriteriaChildAges = criteria.ChildAges == "[]" ? new List<int>() : (_searchserviceHelper.ExtractIntegers(criteria.ChildAges)).ToList()  // Ensure null is treated as empty list
                                             let normalizedPackageRoomChildrenAges = packageRoom?.occupancy?.childrenAges?.ToList() ?? new List<int>()  // Same for childrenAges
                                             let searchGiataMapping = _giataService.GiataApiCall(rePricerId, reservationId.ToString(), criteria.HotelName, room.name, criteria.Destinations, offer.System.Code)?.GetAwaiter().GetResult()

                                             where criteria.passengerCount == packageRoom.occupancy.adults
                                                    && (
                                                    normalizedCriteriaChildAges.Count > 0 && normalizedPackageRoomChildrenAges.Count > 0 ?
                                                    normalizedCriteriaChildAges.SequenceEqual(normalizedPackageRoomChildrenAges) :
                                                    true
                                                    )
                                                    && searchGiataMapping != null
                                                    && criteria?.giataroommappingdetail != null
                                                    && package != null && package.Price?.components?.supplier?.value != null
                                                    && packageRoom != null
                                                    && roomReference != null
                                                    && roomReference?.roomCode.ToString() == room?.index
                                                    && NormalizeString(criteria?.giataroommappingdetail?.GroupName)
                                                        == NormalizeString(searchGiataMapping?.GroupName)
                                                    && NormalizeString(room?.board)
                                                        == NormalizeString(criteria?.RoomBoard)
                                                    && !string.IsNullOrEmpty(NormalizeString(criteria.giataroommappingdetail.GroupName))
                                                    && !string.IsNullOrEmpty(NormalizeString(searchGiataMapping.GroupName))
                                                    && !string.IsNullOrEmpty(NormalizeString(criteria.RoomBoard))
                                                    && !string.IsNullOrEmpty(NormalizeString(room?.board))
                                             orderby package.Price?.components?.supplier?.value
                                             select offer;
                    offerInfoList = offerInfoListQuery.ToList();
                }
                else if (boardMatchEnum == BoardMatchEnum.BoardMatch_GROUP)
                {
                    var offerInfoListQuery = from hotels in searchResponse?.hotels
                                             from offer in hotels.offers
                                             from room in offer.rooms
                                             from package in offer.packages
                                             from packageRoom in package.PackageRooms
                                             from roomReference in packageRoom.roomReferences
                                             from criteria in prebookCriterias.Where(x => x != null && x.ChildAges != null)?.ToList()

                                             let normalizedCriteriaChildAges = criteria.ChildAges == "[]" ? new List<int>() : (_searchserviceHelper.ExtractIntegers(criteria.ChildAges)).ToList()  // Ensure null is treated as empty list
                                             let normalizedPackageRoomChildrenAges = packageRoom.occupancy?.childrenAges?.ToList() ?? new List<int>()  // Same for childrenAges
                                             let searchGiataMapping = _giataService.GiataApiCall(rePricerId, reservationId.ToString(), criteria.HotelName, room.name, criteria.Destinations, offer.System.Code)?.GetAwaiter().GetResult()

                                             where criteria.passengerCount == packageRoom.occupancy.adults
                                                    && (
                                                    normalizedCriteriaChildAges.Count > 0 && normalizedPackageRoomChildrenAges.Count > 0 ?
                                                    normalizedCriteriaChildAges.SequenceEqual(normalizedPackageRoomChildrenAges) :
                                                    true
                                                    )
                                                    && searchGiataMapping != null
                                                    && criteria?.giataroommappingdetail != null
                                                    && package != null && package.Price?.components?.supplier?.value != null
                                                    && packageRoom != null
                                                    && roomReference != null
                                                    && roomReference?.roomCode.ToString() == room?.index
                                                    && NormalizeString(criteria?.giataroommappingdetail?.GroupName)
                                                        == NormalizeString(searchGiataMapping?.GroupName)
                                                    && NormalizeString(_masterService.GetBoardGroupName(room?.board))
                                                        == NormalizeString(_masterService.GetBoardGroupName(criteria?.RoomBoard))

                                                    && !string.IsNullOrEmpty(NormalizeString(criteria.giataroommappingdetail.GroupName))
                                                    && !string.IsNullOrEmpty(NormalizeString(searchGiataMapping.GroupName))
                                                    && !string.IsNullOrEmpty(NormalizeString(criteria.RoomBoard))
                                                    && !string.IsNullOrEmpty(NormalizeString(room?.board))

                                             orderby package.Price?.components?.supplier?.value
                                             select offer;
                    offerInfoList = offerInfoListQuery.ToList();
                }
                else if (boardMatchEnum == BoardMatchEnum.BoardMatch_NO)
                {
                    var offerInfoListQuery = from hotels in searchResponse?.hotels
                                             from offer in hotels.offers
                                             from room in offer.rooms
                                             from package in offer.packages
                                             from packageRoom in package.PackageRooms
                                             from roomReference in packageRoom.roomReferences
                                             from criteria in prebookCriterias.Where(x => x != null && x.ChildAges != null)?.ToList()

                                             let normalizedCriteriaChildAges = criteria.ChildAges == "[]" ? new List<int>() : (_searchserviceHelper.ExtractIntegers(criteria.ChildAges)).ToList()  // Ensure null is treated as empty list
                                             let normalizedPackageRoomChildrenAges = packageRoom.occupancy?.childrenAges?.ToList() ?? new List<int>()  // Same for childrenAges
                                             let searchGiataMapping = _giataService.GiataApiCall(rePricerId, reservationId.ToString(), criteria.HotelName, room.name, criteria.Destinations, offer.System.Code)?.GetAwaiter().GetResult()

                                             where criteria.passengerCount == packageRoom.occupancy.adults
                                                    && (
                                                        normalizedCriteriaChildAges.Count > 0 && normalizedPackageRoomChildrenAges.Count > 0 ?
                                                        normalizedCriteriaChildAges.SequenceEqual(normalizedPackageRoomChildrenAges) :
                                                        true
                                                    )
                                                    && searchGiataMapping != null
                                                    && criteria?.giataroommappingdetail != null
                                                    && package != null && package.Price?.components?.supplier?.value != null
                                                    && packageRoom != null
                                                    && roomReference != null
                                                    && roomReference?.roomCode.ToString() == room?.index
                                                    && NormalizeString(criteria?.giataroommappingdetail?.GroupName)
                                                        == NormalizeString(searchGiataMapping?.GroupName)
                                                    && !string.IsNullOrEmpty(NormalizeString(criteria.giataroommappingdetail.GroupName))
                                                    && !string.IsNullOrEmpty(NormalizeString(searchGiataMapping.GroupName))
                                                    && !string.IsNullOrEmpty(NormalizeString(criteria.RoomBoard))
                                                    && !string.IsNullOrEmpty(NormalizeString(room?.board))

                                             orderby package.Price?.components?.supplier?.value
                                             select offer;
                    offerInfoList = offerInfoListQuery.ToList();
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetOfferListFromSearchResponse),
                    Params = $"RePricerId: {rePricerId}, ReservationId: {reservationId}"
                };

                _log.Error(irixErrorEntity, ex);
            }
            return offerInfoList;
        }

        private async Task<GiataRoomMapping> GetGiataMappingAsync(int rePricerId, string reservationId, string hotelName, string roomName, string destinations, string systemCode)
        {
            // Make the API call asynchronously
            return await _giataService.GiataApiCall(rePricerId, reservationId, hotelName, roomName, destinations, systemCode);
        }

        private IEnumerable<List<T>> Batch<T>(List<T> source, int batchSize)
        {
            for (int i = 0; i < source.Count; i += batchSize)
            {
                yield return source.GetRange(i, Math.Min(batchSize, source.Count - i));
            }
        }

        public async Task<OfferInfo> _5_FilterOfferRoomsWithInTimeAsync(int repricerId, int reservationId, OfferInfo offer, PrebookCriteria criteria, BoardMatchEnum boardMatchEnum, int waitTimeInMinutes)
        {
            var startTime = DateTime.UtcNow;
            var restult = default(OfferInfo);
            var counterTotal = offer?.rooms?.Length ?? 0;
            var message = string.Empty;
            string supplierName = offer?.System?.Code ?? string.Empty;
            try
            {
                var cancellationTokenSource = new CancellationTokenSource();
                var cancellationToken = cancellationTokenSource.Token;
                if (counterTotal > 0)
                {
                    waitTimeInMinutes = Math.Min(waitTimeInMinutes, 1);

                    var task = Task.Run(() =>
                    {
                        try
                        {
                            offer.rooms = offer.rooms.Where(room => IsBoardGroupMatchesExact(room, criteria, boardMatchEnum)).ToList().ToArray();
                        }
                        catch (Exception ex)
                        {
                            message = ex.ToString();
                        }
                    }, cancellationToken);

                    var timeoutTask = Task.Delay((waitTimeInMinutes * 60 * 1000), cancellationToken);

                    var completedTask = await Task.WhenAny(task, timeoutTask);

                    if (completedTask == task)
                    {
                        restult = offer;
                    }
                    else
                    {
                        cancellationTokenSource.Cancel();
                        restult = offer;
                    }
                }
            }
            catch (Exception ex)
            {
                message = ex.ToString();
            }

            var msg = $"FilterOfferAsync \tSupplierName: {supplierName}\t\tOfferId : {offer?.id} \n({restult?.rooms?.Length ?? 0}\\{counterTotal}) {(DateTime.UtcNow - startTime).TotalMinutes.ToString("F2")}\t\tRepricerId: {repricerId}\t\tReservationId : {reservationId}\t\tMULTI_SUPPLIER";
            if (!string.IsNullOrEmpty(message))
            {
                msg += $"\n{message}";
            }
            if (restult?.rooms?.Length != counterTotal)
            {
                // _log.Info(msg, null, true, true, ConsoleColor.DarkYellow);
            }
            return restult;
        }

        private async Task<List<OfferInfo>> _2_GetOfferInfoListAsync(
            SearchResponseFromAPI searchResponse,
            List<PrebookCriteria> prebookCriterias,
            int repricerId,
            int reservationId,
            BoardMatchEnum boardMatchEnum,
            List<DashboardReportResponseRow> activeTabDashboard,
            bool isOptimizeTriggeredManually = false
            )
        {
            var parallelOptions = Repricer.Util.Constant.GetParallelOptions(65);
            var filteredOffers = new ConcurrentBag<OfferInfo>();
            var filteredRoom = new ConcurrentBag<SearchRoom>();
            var filteredPackage = new ConcurrentBag<Package>();
            var filteredPackageroom = new ConcurrentBag<Packageroom>();
            var filteredRoomreference = new ConcurrentBag<Roomreference>();

            var tasks = new ConcurrentBag<Task>();
            var _tasksLimit = 100;
            var _maxDuration = TimeSpan.FromSeconds(30);
            var _methodName = nameof(_2_GetOfferInfoListAsync);
            var startTime = DateTime.UtcNow;
            var linqStartTime = DateTime.UtcNow;
            double linqElapsedTime = 0;
            double foreachElapsedTime = 0;
            DashboardReportResponseRow selectedPrebook = null;
            var selectedPrebookRoomName = string.Empty;
            var offerFilterationTimeInMinutes = 3;

            #region Initial Checks

            if
                (
                    isOptimizeTriggeredManually
                )
            {
                _tasksLimit = 1000;

                _maxDuration = TimeSpan.FromSeconds(300);
                if (activeTabDashboard?.Count > 0 && _isMock == false)
                {
                    selectedPrebook = activeTabDashboard?.FirstOrDefault(x => x.ReservationId == reservationId && x.RepricerId == repricerId);
                    selectedPrebookRoomName = NormalizeString(selectedPrebook?.Prebook?.FirstOrDefault()?.RoomName?.Trim());
                }
            }
            else if
            (
                boardMatchEnum == BoardMatchEnum.BoardMatch_GROUP
                || boardMatchEnum == BoardMatchEnum.BoardMatch_YES
            )
            {
                _tasksLimit = 500;
                _maxDuration = TimeSpan.FromSeconds(300);
            }

            if (
                isOptimizeTriggeredManually
                && (boardMatchEnum != BoardMatchEnum.BoardMatch_GROUP
                && boardMatchEnum != BoardMatchEnum.BoardMatch_YES)
            )
            {
                return filteredOffers.ToList();
            }

            #endregion Initial Checks

            try
            {
                // Calculate time and task limits per criteria
                var totalCriteriaCount = prebookCriterias.Count;
                var allocatedTimePerCriteria = _maxDuration.TotalSeconds / totalCriteriaCount;
                var allocatedTaskLimitPerCriteria = _tasksLimit / totalCriteriaCount;

                #region Offer filtration logic

                linqStartTime = DateTime.UtcNow;
                // List of thresholds you want to try
                var similarityThresholds = new double[]
                {
                    //1.0, 0.75, 0.5, 0.25,0.0
                    .7
                };

                List<SearchRoom> roomsMachesByName = new List<SearchRoom>();
                try
                {
                    if (boardMatchEnum == BoardMatchEnum.BoardMatch_NO)
                    {
                        similarityThresholds = new double[]
                        {
                            //1.0, 0.75, 0.5, 0.25,0.0
                            .7
                        };
                        foreach (var threshold in similarityThresholds)
                        {
                            var roomsMatches = (from hotel in searchResponse.hotels
                                                from offer in hotel.offers
                                                from room in offer.rooms
                                                from criteria in prebookCriterias
                                                let SimilarityScore = GetSimilarityScore(NormalizeString(room.name), NormalizeString(criteria.RoomName))
                                                let RoomMapping = GetGiataMappingAsync
                                                                   (
                                                                       criteria.RepricerId,
                                                                       criteria.ReservationId.ToString(),
                                                                       criteria?.HotelName,
                                                                       room?.name,
                                                                       criteria.Destinations,
                                                                       string.Empty
                                                                   )?.GetAwaiter().GetResult()
                                                where room?.price?.components?.supplier?.value > 0
                                                      && SimilarityScore >= threshold
                                                      && IsPriceValid(repricerId, reservationId, criteria, null, room)
                                                      && IsBoardGroupMatchesExact(room, criteria, boardMatchEnum)
                                                orderby room?.price?.components?.supplier?.value
                                                select new SearchRoom
                                                {
                                                    OfferId = offer.id,
                                                    Supplier = offer?.System?.Code?.ToString(),
                                                    board = room.board,
                                                    boardBasis = room.boardBasis,
                                                    boardMapping = _masterService.GetBoardGroupName(room.board),
                                                    cancellationDeadline = room.cancellationDeadline,
                                                    index = room.index,
                                                    info = room.info,
                                                    name = room.name,
                                                    nonRefundable = room.nonRefundable,
                                                    price = room.price,
                                                    rateTags = room.rateTags,
                                                    status = room.status,
                                                    PriceSearchUpdated = room?.price?.components?.supplier?.value ?? 0.0,
                                                    PriceSearchCurrency = room?.price?.components?.supplier?.currency,
                                                    SimilarityScore = SimilarityScore,
                                                    MappingRoomGroupName = RoomMapping.GroupName,
                                                    MappingRoomName = RoomMapping.RoomName,
                                                    MappingRoomPropertyName = RoomMapping.PropertyName
                                                }).ToList();

                            if (roomsMatches.Any())
                            {
                                roomsMachesByName.AddRange(roomsMatches);
                                break;
                            }
                        }

                        if (roomsMachesByName.Any())
                        {
                            roomsMachesByName = roomsMachesByName
                                                .OrderBy(y => y.PriceSearchUpdated)
                                                .ToList();
                        }
                        linqElapsedTime = (DateTime.UtcNow - linqStartTime).TotalMinutes;
                        Console.WriteLine($"Repricer {repricerId}, Minute: {linqElapsedTime.ToString("F2")}, RooomsFound : {roomsMachesByName.Count} Reservation:{reservationId}, BoardGroup: {boardMatchEnum.ToString()}");
                        if (roomsMachesByName.Count == 0)
                        {
                            return filteredOffers.ToList();
                        }
                    }
                }
                catch (Exception ex)
                {
                }

                startTime = DateTime.UtcNow;
                foreach (var criteria in prebookCriterias.Where(c => c != null))
                {
                    var criteriaStartTime = DateTime.UtcNow;
                    var criteriaTaskCount = 0;
                    var criteriaTaskCountInnermost = 0;

                    if (DateTime.UtcNow - criteriaStartTime > TimeSpan.FromSeconds(allocatedTimePerCriteria))
                        continue;

                    foreach (var hotel in searchResponse?.hotels ?? Enumerable.Empty<SearchHotel>())
                    {
                        var foreachLoopStartTime = DateTime.UtcNow;

                        if (criteriaTaskCount >= allocatedTaskLimitPerCriteria
                            || DateTime.UtcNow - criteriaStartTime > TimeSpan.FromSeconds(allocatedTimePerCriteria)
                            || DateTime.UtcNow - startTime > _maxDuration)
                        {
                            return filteredOffers.ToList();
                        }

                        foreach (var offer in hotel?.offers ?? Enumerable.Empty<OfferInfo>())
                        {
                            try
                            {
                                if (criteriaTaskCount >= allocatedTaskLimitPerCriteria
                                    || DateTime.UtcNow - criteriaStartTime > TimeSpan.FromSeconds(allocatedTimePerCriteria)
                                    || DateTime.UtcNow - startTime > _maxDuration)
                                {
                                    break;
                                }
                                var offerWithMatchedRooms = await _5_FilterOfferRoomsWithInTimeAsync(repricerId, reservationId, offer, criteria, boardMatchEnum, offerFilterationTimeInMinutes);
                                offer.rooms = offerWithMatchedRooms?.rooms ?? offer.rooms.ToList().Take(1).ToArray();

                                offer.packages = (from room in offer.rooms
                                                  from package in offer.packages
                                                  from packageRoom in package.PackageRooms
                                                  from packageRoomReferences in packageRoom.roomReferences
                                                  where packageRoomReferences?.roomCode.ToString() == room?.index
                                                  && IsPriceValid(repricerId, reservationId, criteria, package, room)
                                                  select package
                                                 ).Distinct().ToList().ToArray();

                                if (roomsMachesByName.Count > 0 && boardMatchEnum == BoardMatchEnum.BoardMatch_NO)
                                {
                                    offer.rooms = roomsMachesByName.Where(r => r.OfferId == offer.id).ToList().ToArray();
                                }
                                if (isOptimizeTriggeredManually && !string.IsNullOrEmpty(selectedPrebookRoomName))
                                {
                                    offer.rooms = offer.rooms.Where(r => selectedPrebookRoomName.Contains(NormalizeString(r.name))).ToList().ToArray();
                                }

                                #region Parallel Processing for Rooms

                                Parallel.ForEach(
                                    offer?.rooms ?? Enumerable.Empty<SearchRoom>(),
                                    parallelOptions,
                                    (room) =>
                                    {
                                        if (criteriaTaskCount >= allocatedTaskLimitPerCriteria
                                            || DateTime.UtcNow - criteriaStartTime > TimeSpan.FromSeconds(allocatedTimePerCriteria)
                                            || DateTime.UtcNow - startTime > _maxDuration)
                                        {
                                            return;
                                        }

                                        bool isBoardValid = IsBoardGroupMatchesExact(room, criteria, boardMatchEnum);
                                        if (isBoardValid)
                                        {
                                            try
                                            {
                                                foreach (var package in offer?.packages ?? Enumerable.Empty<Package>())
                                                {
                                                    if (criteriaTaskCount >= allocatedTaskLimitPerCriteria
                                                        || DateTime.UtcNow - criteriaStartTime > TimeSpan.FromSeconds(allocatedTimePerCriteria)
                                                        || DateTime.UtcNow - startTime > _maxDuration)
                                                    {
                                                        return;
                                                    }

                                                    bool isValidPrice = IsPriceValid(repricerId, reservationId, criteria, package, room);
                                                    if (isValidPrice && isBoardValid)
                                                    {
                                                        foreach (var packageRoom in package?.PackageRooms ?? Enumerable.Empty<Packageroom>())
                                                        {
                                                            if (criteriaTaskCount >= allocatedTaskLimitPerCriteria
                                                                || DateTime.UtcNow - criteriaStartTime > TimeSpan.FromSeconds(allocatedTimePerCriteria)
                                                                || DateTime.UtcNow - startTime > _maxDuration)
                                                            {
                                                                return;
                                                            }

                                                            foreach (var roomReference in packageRoom?.roomReferences ?? Enumerable.Empty<Roomreference>())
                                                            {
                                                                if (criteriaTaskCount >= allocatedTaskLimitPerCriteria
                                                                    || DateTime.UtcNow - criteriaStartTime > TimeSpan.FromSeconds(allocatedTimePerCriteria)
                                                                    || DateTime.UtcNow - startTime > _maxDuration)
                                                                {
                                                                    return;
                                                                }

                                                                try
                                                                {
                                                                    var task =
                                                                    ProcessCriteriaAsync
                                                                    (
                                                                        offer, room, package, packageRoom, roomReference,
                                                                        criteria, repricerId, reservationId, boardMatchEnum,
                                                                        filteredOffers, filteredRoom, filteredPackage, filteredPackageroom, filteredRoomreference
                                                                    );

                                                                    if (task != null)
                                                                    {
                                                                        tasks.Add(task);
                                                                        criteriaTaskCount++;
                                                                        criteriaTaskCountInnermost++;
                                                                    }
                                                                    if (criteriaTaskCountInnermost > 50)
                                                                    {
                                                                        Task.WhenAll(tasks)?.GetAwaiter().GetResult();
                                                                        criteriaTaskCountInnermost = 0;

                                                                        criteriaTaskCountInnermost = 0;
                                                                        if (criteriaTaskCount >= allocatedTaskLimitPerCriteria
                                                                            || DateTime.UtcNow - criteriaStartTime > TimeSpan.FromSeconds(allocatedTimePerCriteria)
                                                                            || DateTime.UtcNow - startTime > _maxDuration)
                                                                        {
                                                                            return;
                                                                        }
                                                                    }

                                                                    if (criteriaTaskCount >= allocatedTaskLimitPerCriteria
                                                                        || DateTime.UtcNow - criteriaStartTime > TimeSpan.FromSeconds(allocatedTimePerCriteria)
                                                                        || DateTime.UtcNow - startTime > _maxDuration)
                                                                    {
                                                                        return;
                                                                    }
                                                                }
                                                                catch (Exception ex)
                                                                {
                                                                    var irixErrorEntity1 = new IrixErrorEntity
                                                                    {
                                                                        ClassName = _className,
                                                                        MethodName = "MultiSupplier",
                                                                        RePricerId = repricerId,
                                                                        ReservationId = Convert.ToInt32(reservationId),
                                                                    };

                                                                    _log.Error(irixErrorEntity1, ex);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                var irixErrorEntity1 = new IrixErrorEntity
                                                {
                                                    ClassName = _className,
                                                    MethodName = "MultiSupplier",
                                                    RePricerId = repricerId,
                                                    ReservationId = Convert.ToInt32(reservationId),
                                                };

                                                // Log the information using _log
                                                _log.Error(irixErrorEntity1, ex);
                                            }
                                        }
                                    });

                                #endregion Parallel Processing for Rooms

                                // Wait for all tasks to Complete
                                await Task.WhenAll(tasks);
                                if (criteriaTaskCount >= allocatedTaskLimitPerCriteria
                                                                || DateTime.UtcNow - criteriaStartTime > TimeSpan.FromSeconds(allocatedTimePerCriteria)
                                                                || DateTime.UtcNow - startTime > _maxDuration)
                                {
                                    break; // Exit this async iteration
                                }
                            }
                            catch (Exception ex)
                            {
                            }
                        }

                        // Track time spent in the foreach loop
                        foreachElapsedTime += (DateTime.UtcNow - foreachLoopStartTime).TotalSeconds;

                        // Exit early if task limit for this criteria has been reached
                        if (criteriaTaskCount >= allocatedTaskLimitPerCriteria
                            || DateTime.UtcNow - criteriaStartTime > TimeSpan.FromSeconds(allocatedTimePerCriteria)
                            || DateTime.UtcNow - startTime > _maxDuration)
                        {
                            break; // Exit this async iteration
                        }
                    }

                    // Track time spent on LINQ operations (e.g., filtering, sorting)
                    linqElapsedTime += (DateTime.UtcNow - linqStartTime).TotalSeconds;
                }

                #endregion Offer filtration logic
            }
            catch (Exception ex)
            {
                #region Exception logging

                var irixErrorEntity1 = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = "MultiSupplier",
                    RePricerId = repricerId,
                    ReservationId = Convert.ToInt32(reservationId),
                };

                // Log the information using _log
                _log.Error(irixErrorEntity1, ex);

                #endregion Exception logging
            }

            // Wait for all tasks to Complete
            await Task.WhenAll(tasks);

            var endTime = DateTime.UtcNow;
            var elapsedTime = endTime - startTime;
            var elapsedTimeInMinutes = elapsedTime.TotalMinutes.ToString("F2");

            var foundOffers = filteredOffers
                .OrderBy(x => x?.rooms?.Select(r => r?.price?.components?.supplier?.value).FirstOrDefault() ??
                    x?.packages?.Select(p => p?.Price?.components?.supplier?.value).FirstOrDefault())
                .ToList();

            #region for Faster processing using Filtered result

            if (boardMatchEnum == BoardMatchEnum.BoardMatch_NO && foundOffers?.Count > 0)
            {
                var foundRoom = filteredRoom.ToList();
                var foundPackage = filteredPackage.ToList();
                var foundPackageroom = filteredPackageroom.ToList();
                var foundRoomreference = filteredRoomreference.ToList();

                foreach (var pr in foundPackageroom)
                {
                    pr.roomReferences = filteredRoomreference.ToArray();
                }

                foreach (var fp in foundPackage)
                {
                    fp.PackageRooms = filteredPackageroom.ToArray();
                }

                foreach (var offerInfo in foundOffers)
                {
                    offerInfo.packages = foundPackage.ToArray();
                    offerInfo.rooms = foundRoom.ToArray();
                }
            }

            #endregion for Faster processing using Filtered result

            #region information Logging

            var color = ConsoleColor.Yellow;
            if (elapsedTime.TotalMinutes > 2)
            {
                color = ConsoleColor.DarkYellow;
            }
            if (foundOffers?.Count > 0 && elapsedTime.TotalMinutes > 10)
            {
                var logInfoEntry = new
                {
                    RepricerId = repricerId,
                    OfferFilter = $"({tasks.Count}) tasks END".ToUpper(),
                    Minute = elapsedTimeInMinutes,
                    OffersFound = foundOffers?.Count ?? 0,
                    boardMatchEnum = boardMatchEnum.ToString(),
                    reservationId,
                    Method = _methodName,
                    StartTime = startTime,
                    LinqElapsedTime = $"{linqElapsedTime:F2}",
                    ForeachElapsedTime = $"{foreachElapsedTime:F2}",
                    EndTime = DateTime.Now.ToString("yyyy-MMM-dd HH:mm:ss"),
                    EndTimeUTC = DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss"),
                    //FoundOffersList = foundOffers
                };

                // Log execution info
                var msg = $"{SerializeDeSerializeHelper.Serialize(logInfoEntry)}";
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = "MultiSupplier_OfferFilter",
                    RePricerId = repricerId,
                    ReservationId = Convert.ToInt32(reservationId),
                    Params = msg
                };

                _log.Info(msg, irixErrorEntity, true, true, color);
            }

            #endregion information Logging

            return foundOffers;
        }

        // Helper method to process criteria asynchronously and store results in logInfoEntry ConcurrentBag
        private async Task ProcessCriteriaAsync(
            OfferInfo offer,
            SearchRoom room,
            Package package,
            Packageroom packageRoom,
            Roomreference roomReference,
            PrebookCriteria criteria,
            int rePricerId,
            int reservationId,
            BoardMatchEnum boardMatchEnum,
            ConcurrentBag<OfferInfo> filteredOffers,
            ConcurrentBag<SearchRoom> filteredRoom,
            ConcurrentBag<Package> filteredPackage,
            ConcurrentBag<Packageroom> filteredPackageroom,
            ConcurrentBag<Roomreference> filteredRoomreference
        ) // Use ConcurrentBag for thread-safe addition
        {
            try
            {
                // Normalize children ages
                var normalizedCriteriaChildAges = criteria.ChildAges == "[]"
                    ? new List<int>()
                    : _searchserviceHelper.ExtractIntegers(criteria.ChildAges).ToList();

                var normalizedPackageRoomChildrenAges = packageRoom?.occupancy?.childrenAges?.ToList() ?? new List<int>();

                // Perform the API call asynchronously
                var searchGiataMapping = await GetGiataMappingAsync(
                    rePricerId,
                    reservationId.ToString(),
                    criteria?.HotelName,
                    room?.name,
                    criteria.Destinations,
                    offer?.System?.Code ?? string.Empty);

                // Apply filtering conditions
                if (criteria.passengerCount == packageRoom.occupancy.adults &&
                    (normalizedCriteriaChildAges.Count == 0 || normalizedCriteriaChildAges.SequenceEqual(normalizedPackageRoomChildrenAges)) &&
                    searchGiataMapping != null &&
                    !string.IsNullOrEmpty(searchGiataMapping?.GroupName) &&
                    criteria?.giataroommappingdetail != null &&
                    !string.IsNullOrEmpty(criteria?.giataroommappingdetail?.GroupName) &&
                    package?.Price?.components?.supplier?.value != null &&
                    roomReference?.roomCode.ToString() == room?.index &&
                    NormalizeString(criteria?.giataroommappingdetail?.GroupName ?? string.Empty) == NormalizeString(searchGiataMapping?.GroupName ?? string.Empty))
                {
                    // Apply board matching condition
                    if (IsBoardGroupMatchesExact(room, criteria, boardMatchEnum))
                    {
                        if (!filteredOffers.Contains(offer))
                        {
                            filteredOffers.Add(offer);
                            filteredRoom.Add(room);
                            filteredPackage.Add(package);
                            filteredPackageroom.Add(packageRoom);
                            filteredRoomreference.Add(roomReference);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Optionally log or handle the exception
                Console.WriteLine($"Error processing criteria: {ex.Message}");
            }
        }

        /// <summary>
        /// Exact board group mapping used
        /// </summary>
        /// <param name="room"></param>
        /// <param name="criteria"></param>
        /// <param name="boardMatchEnum"></param>
        /// <returns></returns>
        private bool IsBoardGroupMatchesExact(SearchRoom room, PrebookCriteria criteria, BoardMatchEnum boardMatchEnum)
        {
            try
            {
                var isboardMatch = false;

                var reservationBoardGroup = NormalizeString(criteria.RoomBoard);
                var prebookBoardGroup = NormalizeString(room.board);
                switch (boardMatchEnum)
                {
                    case BoardMatchEnum.BoardMatch_YES:
                        {
                            isboardMatch = reservationBoardGroup == prebookBoardGroup
                                && !string.IsNullOrEmpty(NormalizeString(reservationBoardGroup))
                                && !string.IsNullOrEmpty(NormalizeString(prebookBoardGroup));
                            break;
                        }

                    case BoardMatchEnum.BoardMatch_GROUP:
                        {
                            isboardMatch = NormalizeString(_masterService.GetBoardGroupName(room.board)) == NormalizeString(_masterService.GetBoardGroupName(criteria.RoomBoard))
                                && !string.IsNullOrEmpty(NormalizeString(reservationBoardGroup))
                                && !string.IsNullOrEmpty(NormalizeString(prebookBoardGroup));
                            break;
                        }

                    case BoardMatchEnum.BoardMatch_NO:
                        {
                            isboardMatch = IsBoardMatchesClosely(room.board, criteria.RoomBoard)
                                && !string.IsNullOrEmpty(NormalizeString(reservationBoardGroup))
                                && !string.IsNullOrEmpty(NormalizeString(prebookBoardGroup));
                            break;
                        }

                    default:
                        {
                            isboardMatch = true;
                            break;
                        }
                }
                var isRoomMatch = false;
                if (isboardMatch)
                {
                    var searchGiataMapping = GetGiataMappingAsync
                    (
                        criteria.RepricerId,
                        criteria.ReservationId.ToString(),
                        criteria?.HotelName,
                        room?.name,
                        criteria.Destinations,
                        string.Empty
                    )?.GetAwaiter().GetResult();

                    var reservationRoomGroup = NormalizeString(criteria?.giataroommappingdetail?.GroupName ?? string.Empty);
                    var prebookRoomGroup = NormalizeString(searchGiataMapping?.GroupName ?? string.Empty);

                    isRoomMatch =
                    (
                        reservationRoomGroup == reservationRoomGroup
                        && !string.IsNullOrWhiteSpace(reservationRoomGroup)
                        && !string.IsNullOrWhiteSpace(prebookRoomGroup)
                    );
                }
                return (isRoomMatch && isboardMatch);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(IsBoardGroupMatchesExact),
                    Params = $"RepricerId: {criteria.RepricerId}, ReservationId: {criteria.ReservationId}"
                };

                _log.Error(irixErrorEntity, ex);
            }
            return false;
        }

        /// <summary>
        /// Similarity based board group mapping used
        /// </summary>
        /// <param name="room"></param>
        /// <param name="criteria"></param>
        /// <param name="boardMatchEnum"></param>
        /// <returns></returns>
        private bool IsBoardMatchesClosely(string boardName1, string boardName2)
        {
            try
            {
                if (string.IsNullOrEmpty(boardName1) || string.IsNullOrEmpty(boardName2))
                    return false;

                // Normalize both board names
                boardName1 = NormalizeBoardName(boardName1);
                boardName2 = NormalizeBoardName(boardName2);

                // Fetch all the board mappings (this will fetch once for both boards)
                var boardMappings = _masterService.GetAllBoardMappingsAsync()?.GetAwaiter().GetResult();

                if (boardMappings == null)
                    return false;

                // Find the closest match for both board names, using both Board and BoardGroupName similarity
                var boardMapping1 = boardMappings
                    .Select(x => new
                    {
                        BoardMapping = x,
                        BoardSimilarity = GetSimilarityScore(NormalizeBoardName(x.Board), boardName1),
                        BoardGroupSimilarity = GetSimilarityScore(NormalizeBoardName(x.BoardGroupName), boardName1),
                        ScoreName = GetSimilarityScore(NormalizeBoardName(x.Board), boardName1),
                        ScreGroup = GetSimilarityScore(NormalizeBoardName(x.BoardGroupName), boardName1),
                        x.Board,
                        x.BoardGroupName
                    })
                    .OrderByDescending(x => x.BoardSimilarity)  // Combine both similarity scores
                    .FirstOrDefault(x => !string.IsNullOrWhiteSpace(x.BoardMapping.Board));

                var boardMapping2 = boardMappings
                    .Select(x => new
                    {
                        BoardMapping = x,
                        BoardSimilarity = GetSimilarityScore(NormalizeBoardName(x.Board), boardName2),
                        BoardGroupSimilarity = GetSimilarityScore(NormalizeBoardName(x.BoardGroupName), boardName2),
                        ScoreName = GetSimilarityScore(NormalizeBoardName(x.Board), boardName2),
                        ScreGroup = GetSimilarityScore(NormalizeBoardName(x.BoardGroupName), boardName2),
                        x.Board,
                        x.BoardGroupName
                    })
                    .OrderByDescending(x => x.BoardSimilarity)  // Combine both similarity scores
                    .FirstOrDefault(x => !string.IsNullOrWhiteSpace(x.BoardMapping.Board));

                // If no close match is found for either board name, return false
                if (boardMapping1 == null || boardMapping2 == null)
                    return false;

                // Compare the board group names for both board mappings (use similarity score for BoardGroupName)
                double boardNameSimilarity = GetSimilarityScore(
                    boardName1,
                    boardName2
                );

                double boardGroupNameSimilarity = GetSimilarityScore(
                    NormalizeBoardName(boardMapping1.BoardMapping.BoardGroupName),
                    NormalizeBoardName(boardMapping2.BoardMapping.BoardGroupName)
                );

                // Return true if the board group names match closely (above the 0.5 threshold)
                return (boardNameSimilarity > 0.5);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(IsBoardMatchesClosely)
                };
                _log.Error(irixErrorEntity, ex);
                return false;
            }
        }

        private string NormalizeBoardName(string boardName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(boardName))
                    return string.Empty;

                var cleanedBoardName = Regex.Replace(boardName, @"[\W_]+", " ");
                cleanedBoardName = cleanedBoardName.Replace(" and ", " ").Replace("&", " and ");
                cleanedBoardName = cleanedBoardName.ToLower().Trim();

                return cleanedBoardName;
            }
            catch (Exception ex)
            {
                return boardName;
            }
        }

        private double GetSimilarityScore(string str1, string str2)
        {
            try
            {
                return 1 - (double)LevenshteinDistance(str1, str2) / Math.Max(str1.Length, str2.Length);
            }
            catch (Exception ex)
            {
                return 0;
            }
        }

        private int LevenshteinDistance(string s1, string s2)
        {
            try
            {
                int[,] d = new int[s1.Length + 1, s2.Length + 1];

                for (int i = 0; i <= s1.Length; i++)
                    d[i, 0] = i;
                for (int j = 0; j <= s2.Length; j++)
                    d[0, j] = j;

                for (int i = 1; i <= s1.Length; i++)
                {
                    for (int j = 1; j <= s2.Length; j++)
                    {
                        int cost = (s1[i - 1] == s2[j - 1]) ? 0 : 1;

                        d[i, j] = Math.Min(Math.Min(d[i - 1, j] + 1, d[i, j - 1] + 1), d[i - 1, j - 1] + cost);
                    }
                }

                return d[s1.Length, s2.Length];
            }
            catch (Exception ex)
            {
                return 0;
            }
        }

        private PreBookResponseResult UpdateErrorAndReason
        (
                int repricerId
            , int reservationId
            , PreBookResponseResult preBookResponseResult
            , OptimizationStatusEnum optimizationStatusEnum
            , ReasonCode reasonCode
            , string customMessgae = null
            , OptimizationBookingResponse optimizedResponse = null
            , OptimizationStatusResponse optimizeStatusCheck = null
            , bool isUpdateDB = false
            , bool isCacheRefresh = false
            , bool isOptimizable = false
            , bool isOptimized = false
            , bool isPrebookSucess = false
            , bool isSearchSucess = false
        )
        {
            var actionMessage = customMessgae
                ?? optimizeStatusCheck?.OptimizationRestrictions?.FirstOrDefault().Value
                ?? optimizationStatusEnum.ToString()
                ?? string.Empty;
            //lock (_lock_UpdateErrorAndReason)
            //{
            try
            {
                if (preBookResponseResult == null)
                {
                    preBookResponseResult = new PreBookResponseResult
                    {
                        Status = new Dictionary<int, string>
                        {
                            { reservationId, actionMessage }
                        },
                        IsOptimizable = isOptimizable,
                        IsOptimized = isOptimized,
                        IsPrebookSucess = isPrebookSucess,
                        IsSearchSucess = isSearchSucess,
                        RepricerId = repricerId,
                        OptimizableStatus = optimizedResponse?.Optimization
                    };
                }

                if (preBookResponseResult != null && preBookResponseResult.OptimizableStatus == null)
                {
                    preBookResponseResult.OptimizableStatus = new OptimizationOptimizationBooking
                    {
                        Status = optimizationStatusEnum.ToString(),
                    };
                }

                if (!string.IsNullOrEmpty(customMessgae))
                {
                    preBookResponseResult.OptimizableStatus = new OptimizationOptimizationBooking
                    {
                        Status = customMessgae,
                    };
                    preBookResponseResult.Status = new Dictionary<int, string>
                    {
                        { reservationId, customMessgae }
                    };
                }
                else
                {
                    if (preBookResponseResult.Status == null)
                    {
                        preBookResponseResult.Status = new Dictionary<int, string>
                        {
                            { reservationId, optimizationStatusEnum.ToString() }
                        };
                    }
                }

                var logEntry = new MultiSupplierlog
                {
                    RePricerId = repricerId,
                    ReservationId = reservationId,
                    ReasonCode = reasonCode
                };
                _masterService.MultiSupplierPreBooklog(logEntry);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(UpdateErrorAndReason),
                    Params = $"RePricerId: {reservationId}, ReservationId: {reservationId}"
                };

                _log.Error(irixErrorEntity, ex);
            }
            //}
            if (isUpdateDB)
            {
                //_optimizationHelper.UpdateBookingActionTakenInDB(repricerId, reservationId,)
            }
            return preBookResponseResult;
        }

        private bool IsPriceValid(int repricerId, int reservationId
            , PrebookCriteria criteria
            , Package packageInfo
            , SearchRoom room
            )
        {
            try
            {
                var prebookcriteriaresult = _reservationPersistance.GetPreBookCriteria(reservationId, repricerId, true)?.GetAwaiter().GetResult();
                var prebookcriteria = prebookcriteriaresult?.PreBookCriteriaList;

                if (prebookcriteria?.Any() == false)
                {
                    return false;
                }
                if (packageInfo == null)
                {
                    try
                    {
                        packageInfo = new Package
                        {
                            Price = new Price3
                            {
                                components = new Components1
                                {
                                    commission = new Commission1
                                    {
                                        currency = room?.price?.components?.commission?.currency,
                                        value = room?.price?.components?.commission?.value ?? 0.0,
                                    },
                                    markup = new Markup1
                                    {
                                        currency = room?.price?.components?.markup?.currency,
                                        value = room?.price?.components?.markup?.value ?? 0.0,
                                    },
                                    supplier = new Supplier1
                                    {
                                        currency = room?.price?.components?.supplier?.currency,
                                        value = room?.price?.components?.supplier?.value ?? 0.0,
                                    }
                                },
                                selling = new Selling1
                                {
                                    currency = room?.price?.components?.supplier?.currency,
                                    value = float.Parse(room?.price?.components?.supplier?.value.ToString())
                                }
                            }
                        };
                    }
                    catch (Exception ex)
                    {
                    }
                }
                var packageinfo = packageInfo;

                double? hotelPrice = 0.0;
                double? RoomlevelPrice = 0.0;
                string? hotelCurrency;
                string? RoomLevelCurrency = null;
                decimal hotellevelfactor = 0.0m;
                var packagelevelprice = packageinfo?.Price?.components?.supplier?.value ?? double.MaxValue;
                var packagelevelcurrency = packageinfo?.Price?.components?.supplier?.currency ?? string.Empty;
                var packagelevelfactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, packagelevelcurrency, criteria.Currency));
                var roomlevelPrice = room.price != null && room.price.components != null && room.price.components.supplier != null && room.price.components.supplier.value != null
                    ? room.price.components.supplier.value
                    : 0;
                if (!(roomlevelPrice > 0))
                {
                    hotelPrice = packagelevelprice;
                    hotelCurrency = packagelevelcurrency;
                    hotellevelfactor = packagelevelfactor;
                }
                else
                {
                    RoomlevelPrice = roomlevelPrice;
                    RoomLevelCurrency = room.price != null && room.price.components != null && room.price.components.supplier != null && room.price.components.supplier.value != null
                    ? room.price.components.supplier.currency
                    : packagelevelcurrency;

                    var RoomlevelFactor = _searchserviceHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(repricerId, RoomLevelCurrency, criteria.Currency));
                    hotelPrice = RoomlevelPrice;
                    hotelCurrency = RoomLevelCurrency;
                    hotellevelfactor = RoomlevelFactor;
                }

                string roomLevelInfo = RoomlevelPrice.ToString() + " " + (RoomLevelCurrency ?? string.Empty);
                string packageLevelInfo = packagelevelprice.ToString() + " " + (packagelevelcurrency ?? string.Empty);

                if (hotelPrice > 0)
                {
                    var originalReservationPrice = prebookcriteriaresult?.IssueNet ?? decimal.MaxValue;
                    var packageToken = packageinfo?.PackageToken ?? string.Empty;

                    if (originalReservationPrice > 0)
                    {
                        var searchsyncprice = _searchserviceHelper.RoundToDecimalPlaces(System.Convert.ToDecimal(hotelPrice) * hotellevelfactor);
                        var profit = _searchserviceHelper.RoundToDecimalPlaces(originalReservationPrice - searchsyncprice);

                        var profitperc = _searchserviceHelper.RoundToDecimalPlaces((profit / originalReservationPrice) * 100);

                        if (searchsyncprice > 0 && originalReservationPrice > searchsyncprice && profit >= UtilCommonConstants.ProfitMinimumGlobalCheck)
                        {
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
            }
            return false;
        }

        public async Task SaveRoomMappingRequestToMongo(SearchResponseFromAPI searchResponse, int reservationId, int repricerId, string propertyName, List<PrebookCriteria> prebookCriterias)
        {
            List<string> allRoomNames = new List<string>();

            try
            {
                allRoomNames = await Task.Run(() =>
                                 searchResponse?.hotels
                                ?.SelectMany(hotel => hotel.offers ?? Enumerable.Empty<OfferInfo>())
                               .SelectMany(offer => offer.rooms ?? Enumerable.Empty<SearchRoom>())
                               .Select(room => room.name)
                               .Distinct()
                               .ToList() ?? new List<string>()
                               );

                var prebookRoomNames = prebookCriterias?.Select(pc => pc.RoomName).ToList() ?? new List<string>();

                allRoomNames = allRoomNames ?? new List<string>();
                prebookRoomNames = prebookRoomNames ?? new List<string>();

                if (prebookRoomNames.Any() && allRoomNames?.Any() == true)
                {
                    allRoomNames.AddRange(prebookRoomNames
                        .Where(prebookRoom => !allRoomNames
                            .Any(existingRoom => string.Equals(existingRoom, prebookRoom, StringComparison.OrdinalIgnoreCase)))
                        .ToList());
                }

                if (allRoomNames != null && allRoomNames.Count() > 0)
                {
                    await MultiSupplierRoom(reservationId, repricerId, allRoomNames, propertyName);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(UpdateErrorAndReason),
                    Params = $"RePricerId: {reservationId}, ReservationId: {reservationId},PropertyName: {propertyName}"
                };

                _log.Error(irixErrorEntity, ex);
            }
        }

        private async Task MultiSupplierRoom(int reservationId, int repricerId, List<string> roomNames, string propertyName)
        {
            try
            {
                await _loggerMongoDb.MultiSupplierRoom(reservationId, repricerId, roomNames, propertyName);
            }
            catch (Exception ex)
            {
            }
        }

        public async Task FilterOffers(SearchResponseFromAPI searchResponse, string suppliername)
        {
            if (searchResponse?.hotels != null && searchResponse.hotels.Length > 0)
            {
                searchResponse.hotels = searchResponse.hotels.Select(hotel =>
                {
                    if (hotel.offers != null)
                    {
                        hotel.offers = hotel.offers.Where(offer => offer?.System?.Code != suppliername).ToArray();
                    }
                    return hotel;
                }).ToArray();
            }

            await Task.CompletedTask;
        }

        public async Task<bool> GiataRoomResponseToMongo(int repricerId = 0, int ReservationId = 0, string propertyName = null, bool UpdateStatus = false, bool isActive = true)
        {
            try
            {
                List<ReservationRoomModel> roomReservations = new List<ReservationRoomModel>();
                if (UpdateStatus)
                {
                    roomReservations = await _reservationPersistance.GetReservationsRoomAsync(repricerId);
                    if (ReservationId > 0)
                    {
                        roomReservations = roomReservations?.Where(x => x.ReservationId == ReservationId).ToList();
                    }
                }

                var giataroomrequest = await _loggerMongoDb.GetMultiSupplierRooms(repricerId, ReservationId, propertyName);
                var counter = 0;
                var counterTotal = giataroomrequest?.Count ?? 0;

                if (giataroomrequest != null && counterTotal > 0)
                {
                    int maxDegreeOfParallelism = UtilCommonConstants.GetCountAsPerProcessorPercent(75);
                    maxDegreeOfParallelism = Math.Min(4, maxDegreeOfParallelism);

                    using (var semaphore = new SemaphoreSlim(maxDegreeOfParallelism))
                    {
                        var tasks = giataroomrequest.Select(async room =>
                        {
                            await semaphore.WaitAsync();

                            counter++;
                            var startTime = DateTime.UtcNow;
                            try
                            {
                                //var logEntry = new
                                //{
                                //    repricerId,
                                //    ReservationId,
                                //    Minutes = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2"),
                                //    room.PropertyName,
                                //    room.RoomNames,
                                //    currentTime = DateTime.Now,
                                //    currentTimeUTC = DateTime.UtcNow,
                                //};
                                //var msg = SerializeDeSerializeHelper.Serialize(logEntry);
                                //var ex = new IrixErrorEntity
                                //{
                                //    RePricerId = repricerId,
                                //    ReservationId = ReservationId,
                                //    ClassName = _className,
                                //    MethodName = $"{nameof(GiataRoomResponseToMongo)}_START",
                                //    Params = msg

                                //};
                                //_log.Info(msg, ex, true);
                                await _giataService.GiataRoomResponseToMongo(room.PropertyName, room.RoomNames, roomReservations?.Select(x => x.RoomName)?.ToList() ?? null, isActive);
                                await _giataService.GetGiataRoomListFromMongoAsync(room.PropertyName, true);

                                var logEntry1 = new
                                {
                                    repricerId,
                                    counter = $"({counter}\\{counterTotal}) END",
                                    Minutes = (DateTime.UtcNow - startTime).TotalMinutes.ToString("F2"),
                                    ReservationId,
                                    room.PropertyName,
                                    room.RoomNames,
                                    currentTime = DateTime.Now,
                                    currentTimeUTC = DateTime.UtcNow,
                                };
                                var msg1 = SerializeDeSerializeHelper.Serialize(logEntry1);
                                var ex1 = new IrixErrorEntity
                                {
                                    RePricerId = repricerId,
                                    ReservationId = ReservationId,
                                    ClassName = _className,
                                    MethodName = $"{nameof(GiataRoomResponseToMongo)}_END",
                                    Params = msg1
                                };
                                if (counter == 1 || counter == counterTotal)
                                {
                                    _log.Info(msg1, ex1, true);
                                }
                            }
                            finally
                            {
                                semaphore.Release();
                                await Task.Delay(TimeSpan.FromMilliseconds(300));
                            }
                        }).ToList();

                        // Wait for all tasks to Complete
                        await Task.WhenAll(tasks);
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GiataRoomResponseToMongo),
                    Params = $"RepricerId: {repricerId}"
                };
                _log.Error(irixErrorEntity, ex);
                return false;
            }
        }

        public async Task SaveMappingIdToDbAndUpdateCache(List<string> roomNames, string propertyName)
        {
            var cacheKey = $"GiataRoomList_{propertyName}";

            var giataDetails = await _giataService.GetGiataRooms(propertyName, roomNames);

            if (giataDetails == null || !giataDetails.Any())
            {
                return;
            }

            var bulkGiataRooms = giataDetails.Select(MapToBulkGiataRoom).ToList();

            var giatarespdb = await _masterService.InsertGiataRoomsBulkAsync(bulkGiataRooms);

            var giatarespcache = await _giataService.GetGiataRoomListFromMongoAsync(propertyName);
            if (giatarespcache != null && giatarespcache.Any())
            {
                foreach (var cacheItem in giatarespcache)
                {
                    var dbItem = giatarespdb.FirstOrDefault(db => db.RoomName == cacheItem.RoomName);
                    if (dbItem != null)
                    {
                        cacheItem.MappingId = dbItem.MappingId;
                    }
                }
            }
            _memoryCache.Set(cacheKey, giatarespcache, TimeSpan.FromMinutes(240));
            _loggerMongoDb.UpsertGiataRoomResponseForMappingIdorAsync(giatarespcache, propertyName);
        }

        public async Task<List<GiataRoomMapping>> GetGiataRoom(int ReservationId = 0, string propertyName = null, List<string> RoomName = null)
        {
            List<GiataRoomMapping> result = null;
            try
            {
                // If ReservationId is provided, retrieve the corresponding property name
                if (ReservationId > 0)
                {
                    var giataroomrequest = await _loggerMongoDb.GetMultiSupplierRooms(0, ReservationId);
                    if (giataroomrequest != null)
                    {
                        var giataroom = giataroomrequest.FirstOrDefault(x => x.ReservationId == ReservationId);
                        propertyName = giataroom?.PropertyName;
                    }
                }

                result = await _giataService.GetGiataRooms(propertyName, RoomName);

                var giataroommappingstatus = await _loggerMongoDb.GetAllGiataRoomStatusesAsync();

                if (result != null && giataroommappingstatus != null)
                {
                    var tasks = result.Select(async room =>
                    {
                        var matchingStatus = giataroommappingstatus.FirstOrDefault(status =>
                            status.RoomName == room.RoomName &&
                            status.PropertyName == propertyName &&
                            status.Status == false);

                        if (matchingStatus != null)
                        {
                            room.isActive = false;
                            room.Remarks = matchingStatus.Remarks;
                        }
                        else
                        {
                            room.isActive = true;
                            room.Remarks = matchingStatus?.Remarks ?? string.Empty;
                        }
                    }).ToList();

                    // Wait for all tasks to Complete
                    await Task.WhenAll(tasks);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetGiataRoom),
                    Params = $"ReservationId: {ReservationId}, PropertyName: {propertyName}"
                };
                _log.Error(irixErrorEntity, ex);
            }

            return result;
        }

        public BulkGiataRoom MapToBulkGiataRoom(GiataRoomMapping giataRoomMapping)
        {
            return new BulkGiataRoom
            {
                PropertyName = giataRoomMapping.PropertyName,
                GroupName = giataRoomMapping.GroupName,
                GroupID = giataRoomMapping.GroupID,
                RoomName = giataRoomMapping.RoomName,
                GroupConfidence = giataRoomMapping.GroupConfidence,
                BedDetailDescription = giataRoomMapping.BedDetailDescription,
                RoomCount = giataRoomMapping.RoomCount,
                Accessible = giataRoomMapping.Accessible,
                NonRefundable = giataRoomMapping.NonRefundable,
                Annex = giataRoomMapping.Annex,
                SingleUse = giataRoomMapping.SingleUse,
                SharedBed = giataRoomMapping.SharedBed,
                AverageRoomType = giataRoomMapping.AverageRoomType,
                AverageRoomClasses = giataRoomMapping.AverageRoomClasses,
                AverageRoomViews = giataRoomMapping.AverageRoomViews,
                CreateDate = DateTime.Now
            };
        }

        public async Task<List<ReservationRoomModel>> GetRoomReservationsAsync(int repricerId, int reservationId = 0)
        {
            try
            {
                List<ReservationRoomModel> roomReservations = new List<ReservationRoomModel>();

                roomReservations = await _reservationPersistance.GetReservationsRoomAsync(repricerId);

                if (reservationId > 0 && roomReservations != null && roomReservations.Count() > 0)
                {
                    roomReservations = roomReservations?.Where(x => x.ReservationId == reservationId).ToList();
                }

                return roomReservations;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching room reservations: {ex.Message}");
                return new List<ReservationRoomModel>(); // Return an empty list on error
            }
        }

        public async Task<GiataRoomMapping> GetGiataRoom(string propertyName, string roomname)
        {
            try
            {
                var giataroommappinglist = await _giataService.GetGiataRoomListFromMongoAsync(propertyName);
                var giataroommapping = giataroommappinglist?
                                    .FirstOrDefault(x => x.RoomName.Equals(roomname, StringComparison.OrdinalIgnoreCase));
                if (giataroommapping?.isActive == false)
                {
                    return null;
                }

                return giataroommapping;
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = nameof(GiataService),
                    MethodName = nameof(GetGiataRoom), // Corrected method name
                    Params = $"RoomName: {roomname},propertyName: {propertyName}"
                };
                _log.Error(irixErrorEntity, ex);
                return null;
            }
        }
    } //Class end
}//namespace end