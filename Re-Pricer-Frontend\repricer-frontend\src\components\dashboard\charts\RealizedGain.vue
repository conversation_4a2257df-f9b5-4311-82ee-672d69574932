<script>
import moment from 'moment';
import Button from 'primevue/button';
import Card from 'primevue/card';
import Chart from 'primevue/chart';
import Menu from 'primevue/menu';
import Tag from 'primevue/tag';

export default {
    name: "realizedgain",
    components: { <PERSON>, Chart, Button, Menu, Tag },
    props: ['preBookResponse', 'getDayWiseData'],
    data() {
        return {
            cardGainProgress: {
                body: {
                    class: 'flex-fill justify-content-between'
                }
            },
            chartData: null,
            chartOptions: null,
            chartPassThrough: {
                root: {
                    // class: 'w-100'
                },
                canvas: {
                    style: 'max-width: 100%; max-height: 180px',
                }
            },
            selectedPeriod: null,
            items: [
                {
                    label: 'Last 30 days',
                    days: 30,
                    short: '1M',
                    command: this.updatePeriodFilter
                },
                {
                    label: 'Last 60 days',
                    days: 60,
                    short: '2M',
                    command: this.updatePeriodFilter
                },
                {
                    label: 'Last 90 days',
                    days: 90,
                    short: '3M',
                    command: this.updatePeriodFilter
                },
                {
                    label: 'Last 365 days',
                    days: 365,
                    short: '1Y',
                    command: this.updatePeriodFilter
                },
                {
                    label: 'All',
                    days: 0,
                    short: 'All',
                    command: this.updatePeriodFilter
                }
            ],
            realizedGain: 0,
            unrealizedGain: 0,
            unrealizedGainPT: {
                root: {
                    style: `background-color: var(--text-color-secondary);color: white`
                }
            },
            realizedGainPT: {
                root: {
                    style: `background-color: var(--primary-color);color: white`
                }
            }
        }
    },
    mounted() {
        this.selectedPeriod = this.items[3];

        // this.bindPeriodWiseData(this.selectedPeriod.days);

        this.chartData = this.setChartData();
        this.chartOptions = this.setChartOptions();
    },
    methods: {
        updatePeriodFilter ({item}) {
			this.selectedPeriod = item;
            this.chartData = this.setChartData();
		},
        setChartData() {
            this.realizedGain = 0;
            this.unrealizedGain = 0;
            const documentStyle = getComputedStyle(document.documentElement);
            const barProperties = {
                barPercentage: 1,
                categoryPercentage: 1,
                barThickness: 15,
            }

            let filteredData = this.preBookResponse.notifiedPreBook;
            if (this.selectedPeriod.days != 0) {
                filteredData = this.getDayWiseData(this.preBookResponse.notifiedPreBook, moment().subtract(this.selectedPeriod.days, 'days').format('YYYY-MM-DD'), true);
            }
            filteredData.map(data => {
                if (data.actionsTakens && data.actionsTakens.length > 0) {
                    // check if Y done
                    const Yactions = data.actionsTakens.find(a => a.actionId == 1);
                    if (Yactions?.length > 0) {
                        this.realizedGain = this.realizedGain + data.optimizationProfit;
                    } else {
                        this.unrealizedGain = this.unrealizedGain + data.optimizationProfit;
                    }
                } else {
                    this.unrealizedGain = this.unrealizedGain + data.optimizationProfit;
                }
                return data
            })

            return {
                labels: ['Gain'],
                datasets: [
                    {
                        label: 'Realized',
                        backgroundColor: documentStyle.getPropertyValue('--primary-color'),
                        borderColor: documentStyle.getPropertyValue('--primary-color'),
                        data: [this.realizedGain],
                        ...barProperties
                    },
                    {
                        label: 'Unrealized',
                        backgroundColor: documentStyle.getPropertyValue('--text-color-secondary'),
                        borderColor: documentStyle.getPropertyValue('--text-color-secondary'),
                        data: [this.unrealizedGain],
                        ...barProperties
                    }
                ]
            };
        },
        setChartOptions() {
            const documentStyle = getComputedStyle(document.documentElement);
            const textColor = documentStyle.getPropertyValue('--text-color');
            const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
            const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

            return {
                indexAxis: 'x',
                maintainAspectRatio: true,
                aspectRatio: 0.8,
                barPercentage: 0.5,
                categoryPercentage: 1,
                layout: {
                    autoPadding: false
                },
                plugins: {
                    legend: {
                        labels: {
                            color: textColor
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: textColorSecondary,
                            font: {
                                weight: 500
                            }
                        },
                        grid: {
                            display: false,
                            drawBorder: false
                        }
                    },
                    y: {
                        ticks: {
                            color: textColorSecondary
                        },
                        grid: {
                            color: surfaceBorder,
                            drawBorder: false
                        }
                    }
                }
            };
        },
        toggle(event) {
            this.$refs.PBGain.toggle(event);
        },
    }
}

</script>

<template>
    <Card class="flex-fill" v-if="preBookResponse" :pt="cardGainProgress" style="min-height: 145px;">
        <template #title>
            <div class="d-flex justify-content-between">
                <div class="d-flex justify-content-between flex-column">
                    <span>Active Gain</span>
                    <span class="fs-4 fw-bold text-primary">{{$filters.priceDisplay((realizedGain + unrealizedGain), preBookResponse.data[0].currency, true) }}</span>
                </div>
                <div v-if="selectedPeriod">
                    <Button type="button" text icon="pi pi-filter-fill" @click="toggle" aria-haspopup="true"
                        size="small" aria-controls="PBGain_menu" :label="selectedPeriod.short" />
                    <Menu ref="PBGain" id="PBGain_menu" :model="items" :popup="true" style="min-width: auto;" />
                </div>
            </div>
        </template>
        <template #content>
            <Chart type="bar" :data="chartData" :options="chartOptions" :pt="chartPassThrough" />
            <div class="d-flex gap-2" style="">
                <Tag class="gap-1" :pt="unrealizedGainPT" >Unrealized: <span class="text-nowrap">{{ $filters.priceDisplay(unrealizedGain, preBookResponse.data[0].currency, true) }}</span></Tag>
                <Tag class="gap-1" :pt="realizedGainPT" severity="success">Realized: <span class="text-nowrap">{{ $filters.priceDisplay(realizedGain, preBookResponse.data[0].currency, true) }}</span></Tag>
            </div>
        </template>
    </Card>
</template>
