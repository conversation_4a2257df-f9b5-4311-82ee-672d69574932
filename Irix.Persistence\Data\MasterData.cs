﻿using Irix.Entities;
using Microsoft.Data.SqlClient;
using RePricer.Util;
using System.Data;
using System.Text.RegularExpressions;
using constants = RePricer.Constants.ServiceConstants;

namespace Irix.Persistence.Data
{
    public class MasterData
    {
        public CurrencyExchangeRates ExchangeRangeData(IDataReader reader)
        {
            var currencyExchangeRate = new CurrencyExchangeRates
            {
                FromCurrencyCode = DbPropertyHelper.StringPropertyFromRow(reader, "from_cur"),
                ToCurrencyCode = DbPropertyHelper.StringPropertyFromRow(reader, "to_cur"),
                ExchangeRate = DbPropertyHelper.DecimalPropertyFromRow(reader, "exchangeratevalue")
            };

            return currencyExchangeRate;
        }

        public EmailTemplate GetEmailTemplates(IDataReader reader)
        {
            var emailTemplate = new EmailTemplate
            {
                ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                EmailBody = DbPropertyHelper.StringPropertyFromRow(reader, "Emailbody"),
                // Include PrebookData properties
                RePricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RePricerId"),
                BookingDate = DbPropertyHelper.DateTimePropertyFromRow(reader, "BookingDate"),
                ReportFechedOn = DbPropertyHelper.DateTimePropertyFromRow(reader, "ReportFechedOn"),
                PrebookDate = DbPropertyHelper.DateTimePropertyFromRow(reader, "PrebookDate"),
                PrebookDateTime = DbPropertyHelper.DateTimePropertyFromRow(reader, "PrebookDateTime"),
                PreBookPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "PreBookPrice"),
                IssueNet = DbPropertyHelper.DecimalPropertyFromRow(reader, "IssueNet"),
                IssueSelling = DbPropertyHelper.DecimalPropertyFromRow(reader, "IssueSelling"),
                PossibleGainAsPerIssueNet = DbPropertyHelper.DecimalPropertyFromRow(reader, "PossibleGainAsPerIssueNet"),
                PossibleGainPercentage = DbPropertyHelper.DecimalPropertyFromRow(reader, "PossibleGainPercentage"),
                Currency = DbPropertyHelper.StringPropertyFromRow(reader, "Currency"),
                RateDetailsName = DbPropertyHelper.StringPropertyFromRow(reader, "RateDetailsName"),
                IsMailSent = DbPropertyHelper.BoolPropertyFromRow(reader, "IsMailSent"),
                AccommodationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "AccomdationId"),
                CheckIn = DbPropertyHelper.DateTimePropertyFromRow(reader, "CheckIn"),
                CheckOut = DbPropertyHelper.DateTimePropertyFromRow(reader, "CheckOut"),
                LeaderNationality = DbPropertyHelper.StringPropertyFromRow(reader, "LeaderNationality"),
                AdultCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "AdultCount"),
                PreBookAdultCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "PreBook_AdultCount"),
                ChildrenAges = DbPropertyHelper.StringPropertyFromRow(reader, "ChildrenAges"),
                PreBookChildrenAges = DbPropertyHelper.StringPropertyFromRow(reader, "PreBook_ChildrenAges"),
                CountryId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "CountryId"),
                CountryName = DbPropertyHelper.StringPropertyFromRow(reader, "CountryName"),
                HotelId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "HotelId"),
                HotelName = DbPropertyHelper.StringPropertyFromRow(reader, "HotelName"),
                CityId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "CityId"),
                RoomId = DbPropertyHelper.StringPropertyFromRow(reader, "RoomId"),
                RoomName = DbPropertyHelper.StringPropertyFromRow(reader, "RoomName"),
                PreBookRoomName = DbPropertyHelper.StringPropertyFromRow(reader, "PreBook_roomName"),
                PreBookRoomIndex = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "PreBook_roomIndex"),
                RoomType = DbPropertyHelper.StringPropertyFromRow(reader, "RoomType"),
                RoomBoard = DbPropertyHelper.StringPropertyFromRow(reader, "RoomBoard"),
                PreBookRoomBoard = DbPropertyHelper.StringPropertyFromRow(reader, "PreBook_roomBoard"),
                RoomInfo = DbPropertyHelper.StringPropertyFromRow(reader, "RoomInfo"),
                AvailabilityToken = DbPropertyHelper.StringPropertyFromRow(reader, "AvailabilityToken"),
                PreBookPackageRoomCode = DbPropertyHelper.StringPropertyFromRow(reader, "PreBook_packageRoomCode"),
                PreBookRoomToken = DbPropertyHelper.StringPropertyFromRow(reader, "PreBook_roomToken"),
                PreBookSelected = DbPropertyHelper.StringPropertyFromRow(reader, "PreBook_selected"),
                PreBookStatus = DbPropertyHelper.StringPropertyFromRow(reader, "PreBook_status"),
                PreBookNonRefundable = DbPropertyHelper.StringPropertyFromRow(reader, "PreBook_nonRefundable")
            };

            return emailTemplate;
        }

        public EmailContentResult GetEmailContent(IDataReader reader)
        {
            var emailTemplate = new EmailContentResult
            {
                ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                ReservationEmailBody = DbPropertyHelper.StringPropertyFromRow(reader, "reservationemailbody"),
                PreBookEmailBody = DbPropertyHelper.StringPropertyFromRow(reader, "prebookemailbody"),
                RePricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RePricerId"),
            };
            return emailTemplate;
        }

        public List<ReservationTableModel> GetReservationTableData(IDataReader reader)
        {
            var reservationTables = new List<ReservationTableModel>();

            while (reader.Read())
            {
                var reservationTable = new ReservationTableModel
                {
                    ReservationId = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationId"),
                    RePricerId = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "RePricerId"),
                    CreatedDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "createdate"),
                    ReservationAdultCount = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationAdultCount"),
                    PreBookAdultCount = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookAdultCount"),
                    ReservationChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationChildAges"),
                    PreBookChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookChildAges"),
                    Providers = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Providers"),
                    ReservationProviders = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationProviders"),
                    BookingDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "BookingDate"),
                    ClientConfig_MarginThreshold_PercentageUsed = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "IsUsePercentage"),
                    ClientConfig_MarginThreshold_Value = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PriceDifferenceValue"),
                    ClientConfig_TravelDaysMaxSearchInDays = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "TravelDaysMaxSearchInDays"),
                    ClientConfig_TravelDaysMinSearchInDays = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "TravelDaysMinSearchInDays"),
                    ClientConfig_DaysDifferenceInPreBookCreation = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ClientConfig_DaysDifferenceInPreBookCreation"),
                    PreBookCount = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCount"),
                    ReservationPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "ReservationPrice"),
                    PreBookPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "PreBookPrice"),
                    OptimizationProfit = DbPropertyHelper.DecimalPropertyFromRow(reader, "Profit"),
                    Currency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Currency"),
                    CurrencyFactorToEur = DbPropertyHelper.DecimalPropertyFromRow(reader, "CurrencyFactorToEur"),
                    Reservation_RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomName"),
                    PreBook_RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomName"),
                    Reservation_RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomBoard"),
                    PreBook_RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomBoard"),
                    Reservation_RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomInfo"),
                    PreBook_RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomInfo"),
                    PreBook_RoomIndex = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomIndex"),
                    MatchedReservationCancellationDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationDate"),
                    MatchedPreBookCancellationDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationDate"),
                    MatchedReservationCancellationChargeByPolicy = ExtractNumericPart(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationChargeByPolicy")),
                    MatchedReservationCancellationCurrencyByPolicy = ExtractCurrencyCode(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationChargeByPolicy")),
                    MatchedPreBookCancellationChargeByPolicy = ExtractNumericPart(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationChargeByPolicy")),
                    MatchedPreBookCancellationCurrencyByPolicy = ExtractCurrencyCode(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationChargeByPolicy")),
                    IsCancellationPolicyMatched = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "IsCancellationPolicyMatched"),
                    CPStatus = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CPStatus"),
                    CPDaysGain = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CPDaysGain"),
                    MatchedCancellationPolicyGain = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedCancellationPolicyGain"),
                    Token = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Token"),
                    AvailabilityToken = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "AvailabilityToken"),
                    PreBookResponseJson = string.Empty, //DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookResponseJson"),
                    SearchSyncJson = string.Empty, //DbPropertyHelper.StringDefaultPropertyFromRow(reader, "SearchSyncJson"),
                    ReservationHotelName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationHotelName"),
                    PreBookHotelName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookHotelName"),
                    ReservationCheckIn = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCheckIn"),
                    ReservationCheckOut = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCheckOut"),
                    PreBookCheckIn = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCheckIn"),
                    PreBookCheckOut = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCheckOut"),
                    ReservationRoomType = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomType"),
                    ReservationNumberOfRooms = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "NumberOfRooms")
                };

                reservationTables.Add(reservationTable);
            }

            return reservationTables;
        }

        public List<ReservationTableLogModel> GetReservationTableLogData(IDataReader reader)
        {
            var reservationTableLogs = new List<ReservationTableLogModel>();

            while (reader.Read())
            {
                var reservationTableLog = new ReservationTableLogModel
                {
                    ReservationId = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationId"),
                    RePricerId = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "RePricerId"),
                    CreatedDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "createdate"),
                    ReservationAdultCount = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationAdultCount"),
                    PreBookAdultCount = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookAdultCount"),
                    ReservationChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationChildAges"),
                    PreBookChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookChildAges"),
                    Providers = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Providers"),
                    ReservationProviders = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationProviders"),
                    ClientConfig_MarginThreshold_PercentageUsed = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "IsUsePercentage"),
                    ClientConfig_MarginThreshold_Value = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PriceDifferenceValue"),
                    ClientConfig_TravelDaysMaxSearchInDays = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "TravelDaysMaxSearchInDays"),
                    ClientConfig_TravelDaysMinSearchInDays = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "TravelDaysMinSearchInDays"),
                    BookingDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "BookingDate"),
                    ReservationPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "ReservationPrice"),
                    PreBookPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "PreBookPrice"),
                    OptimizationProfit = DbPropertyHelper.DecimalPropertyFromRow(reader, "Profit"),
                    Currency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Currency"),
                    CurrencyFactortoEur = DbPropertyHelper.DecimalPropertyFromRow(reader, "CurrencyFactorToEur"),
                    Reservation_roomname = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomName"),
                    PreBook_RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomName"),
                    Reservation_RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomBoard"),
                    PreBook_RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomBoard"),
                    Reservation_RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomInfo"),
                    Prebook_RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomInfo"),
                    PreBook_RoomIndex = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomIndex"),
                    MatchedReservationCancellationDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationDate"),
                    MatchedPreBookCancellationDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationDate"),
                    MatchedReservationCancellationChargeByPolicy = ExtractNumericPart(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationChargeByPolicy")),
                    MatchedReservationCancellationCurrencyByPolicy = ExtractCurrencyCode(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationChargeByPolicy")),
                    MatchedPreBookCancellationChargeByPolicy = ExtractNumericPart(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationChargeByPolicy")),
                    MatchedPreBookCancellationCurrencyByPolicy = ExtractCurrencyCode(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationChargeByPolicy")),
                    IsCancellationPolicyMatched = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "IsCancellationPolicyMatched"),
                    CPStatus = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CPStatus"),
                    Cpdaysgain = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CPDaysGain"),
                    MatchedCancellationPolicyGain = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedCancellationPolicyGain"),
                    Token = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Token"),
                    AvailabilityToken = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "AvailabilityToken"),
                    PreBookResponseJson = string.Empty, //DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookResponseJson"),
                    SearchSyncJson = string.Empty, //DbPropertyHelper.StringDefaultPropertyFromRow(reader, "SearchSyncJson"),
                    ReservationHotelName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationHotelName"),
                    PreBookHotelName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookHotelName"),
                    ReservationCheckIn = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCheckIn"),
                    ReservationCheckOut = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCheckOut"),
                    PreBookCheckIn = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCheckIn"),
                    PreBookCheckOut = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCheckOut"),
                    ReservationRoomType = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomType"),
                    ReservationNumberOfRooms = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "NumberOfRooms")
                };

                reservationTableLogs.Add(reservationTableLog);
            }

            return reservationTableLogs;
        }

        private static string ExtractNumericPart(string input)
        {
            string pattern = @"([\d.]+)";

            Match match = Regex.Match(input, pattern);
            if (match.Success)
            {
                return match.Groups[1].Value;
            }
            else
            {
                return string.Empty;
            }
        }

        private static string ExtractCurrencyCode(string input)
        {
            string pattern = @"(?<=\d+\.?\d*)[A-Za-z]+";

            Match match = Regex.Match(input, pattern);

            if (match.Success)
            {
                return match.Groups[0].Value; // Group[0] is the entire match
            }
            else
            {
                return string.Empty;
            }
        }

        public PreBookLogModel GetPreBookLogData(IDataReader reader)
        {
            var preBookLog = new PreBookLogModel
            {
                ReservationId = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationId"),
                RePricerId = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "RePricerId"),
                CreatedDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "createdate"),
                ReservationAdultCount = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationAdultCount"),
                PreBookAdultCount = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookAdultCount"),
                ReservationChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationChildAges"),
                PreBookChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookChildAges"),
                Providers = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Providers"),
                ReservationProviders = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationProviders"),
                BookingDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "BookingDate"),
                ReservationPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "ReservationPrice"),
                PreBookPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "PreBookPrice"),
                OptimizationProfit = DbPropertyHelper.DecimalPropertyFromRow(reader, "Profit"),
                Currency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Currency"),
                CurrencyFactorToEur = DbPropertyHelper.DecimalPropertyFromRow(reader, "CurrencyFactorToEur"),
                Reservation_RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomName"),
                PreBook_RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomName"),
                Reservation_RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomBoard"),
                PreBook_RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomBoard"),
                Reservation_RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomInfo"),
                Prebook_RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomInfo"),
                PreBook_RoomIndex = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomIndex"),
                SearchSyncJson = string.Empty, //DbPropertyHelper.StringDefaultPropertyFromRow(reader, "SearchSyncJson"),
                SearchSyncCriteriaJson = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "SearchSyncCriteriaJson"),
                ReservationHotelName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationHotelName"),
                PreBookHotelName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookHotelName"),
                ReservationCheckIn = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCheckIn"),
                ReservationCheckOut = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCheckOut"),
                PreBookCheckIn = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCheckIn"),
                PreBookCheckOut = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCheckOut"),
                ReservationRoomType = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomType"),
                ReservationNumberOfRooms = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "NumberOfRooms")
            };

            return preBookLog;
        }

        public List<PreBookLogModel> GetPreBookLogDataList(IDataReader reader)
        {
            var preBookLogs = new List<PreBookLogModel>();

            while (reader.Read())
            {
                var preBookLog = GetPreBookLogData(reader);
                preBookLogs.Add(preBookLog);
            }

            return preBookLogs;
        }

        public List<ActionInfo> ActionData(IDataReader reader)
        {
            var actioninfos = new List<ActionInfo>();

            while (reader.Read())
            {
                var actioninfo = new ActionInfo
                {
                    ActionId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ActionId"),
                    ActionName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ActionName")
                };

                actioninfos.Add(actioninfo);
            }

            return actioninfos;
        }

        public List<NotifiedPreBookResult> GetPreBookResult(IDataReader reader)
        {
            var preBookResults = new List<NotifiedPreBookResult>();
            try
            {
                while (reader.Read())
                {
                    var preBookResult = new NotifiedPreBookResult
                    {
                        ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                        RePricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RePricerId"),
                        CreatedDate = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "createdate")).ToString(constants.datetypeDB),
                        ReservationAdultCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationAdultCount"),
                        PreBookAdultCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "PreBookAdultCount"),
                        ReservationChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationChildAges"),
                        PreBookChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookChildAges"),
                        Providers = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Providers"),
                        BookingDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "BookingDate"),
                        ClientConfig_MarginThreshold_PercentageUsed = DbPropertyHelper.BoolDefaultPropertyFromRow(reader, "IsUsePercentage"),
                        ClientConfig_MarginThreshold_Value = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "PriceDifferenceValue"),
                        ClientConfig_TravelDaysMaxSearchInDays = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "TravelDaysMaxSearchInDays"),
                        ClientConfig_TravelDaysMinSearchInDays = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "TravelDaysMinSearchInDays"),
                        ClientConfig_DaysDifferenceInPreBookCreation = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ClientConfig_DaysDifferenceInPreBookCreation"),
                        PreBookCount = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCount"),
                        ReservationPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "ReservationPrice"),
                        PreBookPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "PreBookPrice"),
                        OptimizationProfit = DbPropertyHelper.DecimalPropertyFromRow(reader, "Profit"),
                        Currency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Currency"),
                        CurrencyFactorToEur = DbPropertyHelper.DecimalPropertyFromRow(reader, "CurrencyFactorToEur"),
                        Reservation_RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomName"),
                        PreBook_RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomName"),
                        Reservation_RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomBoard"),
                        PreBook_RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomBoard"),
                        Reservation_RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomInfo"),
                        PreBook_RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomInfo"),
                        PreBook_RoomIndex = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomIndex"),
                        MatchedReservationCancellationDate = string.IsNullOrEmpty(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationDate")) ? "" : System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationDate")).ToString(constants.datetypeDB) ?? "",
                        MatchedPreBookCancellationDate = string.IsNullOrEmpty(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationDate")) ? "" : System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationDate")).ToString(constants.datetypeDB),
                        MatchedReservationCancellationChargeByPolicy = ExtractNumericPart(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationChargeByPolicy")),
                        MatchedReservationCancellationCurrencyByPolicy = ExtractCurrencyCode(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationChargeByPolicy")),
                        MatchedPreBookCancellationChargeByPolicy = ExtractNumericPart(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationChargeByPolicy")),
                        MatchedPreBookCancellationCurrencyByPolicy = ExtractCurrencyCode(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationChargeByPolicy")),
                        IsCancellationPolicyMatched = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "IsCancellationPolicyMatched"),
                        CPStatus = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CPStatus"),
                        Cpdaysgain = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CPDaysGain"),
                        MatchedCancellationPolicyGain = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedCancellationPolicyGain"),
                        //ExtraData = DbPropertyHelper.StringDefaultPropertyFromRow(reader,"ExtraData"),
                        //HMNotes = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "HMNotes"),
                        //NewBookingId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "NewBookingId"),
                        //ActionId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ActionId"),
                        //NewBookingPrice = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "NewBookingPrice"),
                        ClientConfig_MaxNumberOfTimesOptimization = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "MaxNumberOfTimesOptimization"),
                        ReservationDestination = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationDestination"),
                        PreBookDestination = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookDestination"),
                        ReservationHotelName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationHotelName"),
                        PreBookHotelName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookHotelName"),
                        ReservationCheckIn = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCheckIn")).ToString(constants.datetype),
                        ReservationCheckOut = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCheckOut")).ToString(constants.datetype),
                        PreBookCheckIn = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCheckIn")).ToString(constants.datetype),
                        PreBookCheckOut = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCheckOut")).ToString(constants.datetype),
                        ReservationRoomType = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomType"),
                        ReservationNumberOfRooms = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "NumberOfRooms"),
                        PreBookNumberOfRooms = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "NumberOfRooms"),
                        ReservationStatus = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationStatus")
                    };

                    preBookResults.Add(preBookResult);
                }
            }
            catch (Exception ex)
            {
            }

            return preBookResults;
        }

        public List<NotifiedPreBookResult> GetBenchMark(IDataReader reader)
        {
            var preBookResults = new List<NotifiedPreBookResult>();
            try
            {
                while (reader.Read())
                {
                    var preBookResult = new NotifiedPreBookResult
                    {
                        ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                        RePricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RePricerId"),
                        CreatedDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "createdate"),
                        ReservationAdultCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationAdultCount"),
                        PreBookAdultCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "PreBookAdultCount"),
                        ReservationChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationChildAges"),
                        PreBookChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookChildAges"),
                        Providers = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Providers"),
                        BookingDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "BookingDate"),
                        ClientConfig_MarginThreshold_PercentageUsed = DbPropertyHelper.BoolDefaultPropertyFromRow(reader, "IsUsePercentage"),
                        ClientConfig_MarginThreshold_Value = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "PriceDifferenceValue"),
                        ClientConfig_TravelDaysMaxSearchInDays = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "TravelDaysMaxSearchInDays"),
                        ClientConfig_TravelDaysMinSearchInDays = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "TravelDaysMinSearchInDays"),
                        ClientConfig_DaysDifferenceInPreBookCreation = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ClientConfig_DaysDifferenceInPreBookCreation"),
                        PreBookCount = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCount"),
                        ReservationPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "ReservationPrice"),
                        PreBookPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "PreBookPrice"),
                        OptimizationProfit = DbPropertyHelper.DecimalPropertyFromRow(reader, "Profit"),
                        Currency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Currency"),
                        CurrencyFactorToEur = DbPropertyHelper.DecimalPropertyFromRow(reader, "CurrencyFactorToEur"),
                        Reservation_RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomName"),
                        PreBook_RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomName"),
                        Reservation_RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomBoard"),
                        PreBook_RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomBoard"),
                        Reservation_RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomInfo"),
                        PreBook_RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomInfo"),
                        PreBook_RoomIndex = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomIndex"),
                        MatchedReservationCancellationDate = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationDate")).ToString(constants.datetypeDB),
                        MatchedPreBookCancellationDate = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationDate")).ToString(constants.datetype),
                        MatchedReservationCancellationChargeByPolicy = ExtractNumericPart(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationChargeByPolicy")),
                        MatchedReservationCancellationCurrencyByPolicy = ExtractCurrencyCode(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationChargeByPolicy")),
                        MatchedPreBookCancellationChargeByPolicy = ExtractNumericPart(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationChargeByPolicy")),
                        MatchedPreBookCancellationCurrencyByPolicy = ExtractCurrencyCode(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationChargeByPolicy")),
                        IsCancellationPolicyMatched = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "IsCancellationPolicyMatched"),
                        CPStatus = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CPStatus"),
                        Cpdaysgain = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CPDaysGain"),
                        MatchedCancellationPolicyGain = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedCancellationPolicyGain"),
                        //ExtraData = DbPropertyHelper.StringDefaultPropertyFromRow(reader,"ExtraData"),
                        //HMNotes = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "HMNotes"),
                        //NewBookingId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "NewBookingId"),
                        //ActionId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ActionId"),
                        //NewBookingPrice = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "NewBookingPrice"),
                        ClientConfig_MaxNumberOfTimesOptimization = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "MaxNumberOfTimesOptimization")
                    };

                    preBookResults.Add(preBookResult);
                }
            }
            catch (Exception ex)
            {
            }

            return preBookResults;
        }

        public List<NotifiedPreBookResult> GetCancellation(IDataReader reader)
        {
            var preBookResults = new List<NotifiedPreBookResult>();
            try
            {
                while (reader.Read())
                {
                    var preBookResult = new NotifiedPreBookResult
                    {
                        ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                        RePricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RePricerId"),
                        CreatedDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "createdate"),
                        ReservationAdultCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationAdultCount"),
                        PreBookAdultCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "PreBookAdultCount"),
                        ReservationChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationChildAges"),
                        PreBookChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookChildAges"),
                        Providers = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Providers"),
                        BookingDate = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "BookingDate"),
                        ClientConfig_MarginThreshold_PercentageUsed = DbPropertyHelper.BoolDefaultPropertyFromRow(reader, "IsUsePercentage"),
                        ClientConfig_MarginThreshold_Value = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "PriceDifferenceValue"),
                        ClientConfig_TravelDaysMaxSearchInDays = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "TravelDaysMaxSearchInDays"),
                        ClientConfig_TravelDaysMinSearchInDays = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "TravelDaysMinSearchInDays"),
                        ClientConfig_DaysDifferenceInPreBookCreation = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ClientConfig_DaysDifferenceInPreBookCreation"),
                        PreBookCount = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCount"),
                        ReservationPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "ReservationPrice"),
                        PreBookPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "PreBookPrice"),
                        OptimizationProfit = DbPropertyHelper.DecimalPropertyFromRow(reader, "Profit"),
                        Currency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Currency"),
                        CurrencyFactorToEur = DbPropertyHelper.DecimalPropertyFromRow(reader, "CurrencyFactorToEur"),
                        Reservation_RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomName"),
                        PreBook_RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomName"),
                        Reservation_RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomBoard"),
                        PreBook_RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomBoard"),
                        Reservation_RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomInfo"),
                        PreBook_RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomInfo"),
                        PreBook_RoomIndex = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookRoomIndex"),
                        MatchedReservationCancellationDate = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationDate")).ToString(constants.datetypeDB),
                        MatchedPreBookCancellationDate = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationDate")).ToString(constants.datetypeDB),
                        MatchedReservationCancellationChargeByPolicy = ExtractNumericPart(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationChargeByPolicy")),
                        MatchedReservationCancellationCurrencyByPolicy = ExtractCurrencyCode(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedReservationCancellationChargeByPolicy")),
                        MatchedPreBookCancellationChargeByPolicy = ExtractNumericPart(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationChargeByPolicy")),
                        MatchedPreBookCancellationCurrencyByPolicy = ExtractCurrencyCode(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedPreBookCancellationChargeByPolicy")),
                        IsCancellationPolicyMatched = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "IsCancellationPolicyMatched"),
                        CPStatus = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CPStatus"),
                        Cpdaysgain = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CPDaysGain"),
                        MatchedCancellationPolicyGain = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "MatchedCancellationPolicyGain"),
                        //ExtraData = DbPropertyHelper.StringDefaultPropertyFromRow(reader,"ExtraData"),
                        //HMNotes = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "HMNotes"),
                        //NewBookingId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "NewBookingId"),
                        //ActionId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ActionId"),
                        //NewBookingPrice = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "NewBookingPrice"),
                        ClientConfig_MaxNumberOfTimesOptimization = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "MaxNumberOfTimesOptimization")
                    };

                    preBookResults.Add(preBookResult);
                }
            }
            catch (Exception ex)
            {
            }

            return preBookResults;
        }

        public Summary GetSummary(IDataReader reader)
        {
            var summary = new Summary();
            try
            {
                while (reader.Read())
                {
                    try
                    {
                        summary.Optimized_Reservations_Unrealized_Gain = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "Profit");
                        summary.Currency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Currency");
                        summary.ReservationOptimisedPercentage = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PROFITPERC");
                        summary.Optimized_Reservations_Realized_Gain = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "NewBookingPrice");
                        summary.Differce_In_Gain = DbPropertyHelper.DecimalPropertyFromRow(reader, "DifferenceinGain");
                        summary.Number_Of_Optimized_Reservations = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "totalReservationID");
                    }
                    catch (Exception ex)
                    {
                    }
                }

                return summary;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public List<ActionsTaken> ReadActionTakenFromDB(IDataReader reader)
        {
            var actiontakens = new List<ActionsTaken>();

            while (reader.Read())
            {
                try
                {
                    var actionsTaken = new ActionsTaken
                    {
                        repricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerId"),
                        reservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                        ExtraData = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ExtraData"),
                        HMNotes = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "HMNotes"),
                        NewBookingId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "NewBookingId"),
                        ActionId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ActionId"),
                        RealizedGain = DbPropertyHelper.DecimalDefaultNullablePropertyFromRow(reader, "NewBookingPrice"),
                        createdById = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "createdById"),
                        createdByName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "createdByName"),
                        ActionName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ActionName"),
                        createdOn = DbPropertyHelper.DateTimePropertyFromRow(reader, "createdOn"),
                        ResponseBody = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ResponseBody"),
                    };
                    try
                    {
                        actionsTaken.IsUseCustomerCurrencyFactor = DbPropertyHelper.BoolDefaultPropertyFromRow(reader, "isUseCustomerCurrencyFactor");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.Message);
                    }
                    actiontakens.Add(actionsTaken);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                }
            }

            return actiontakens;
        }

        public List<MaxProfit> GetmaxProfit(IDataReader reader)
        {
            var maxProfits = new List<MaxProfit>();

            while (reader.Read())
            {
                try
                {
                    var maxProfit = new MaxProfit
                    {
                        RepricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RePricerId"),
                        ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                        CreatedDate = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookDate")).ToString(constants.datetypeDB),
                        PreBookPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "ReservationPrice"),
                        ReservationPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "PreBookPrice"),
                        OptimizationProfit = DbPropertyHelper.DecimalPropertyFromRow(reader, "Profit")
                    };

                    maxProfits.Add(maxProfit);
                }
                catch (Exception ex)
                {
                }
            }

            return maxProfits;
        }

        public List<DashboardReportResponseRow> GetReservationReports(IDataReader reader)
        {
            var reservationReports = new List<DashboardReportResponseRow>();

            while (reader.Read())
            {
                var reservationReport = new DashboardReportResponseRow();
                try
                {
                    var optimizationDate = string.Empty;

                    if (!String.IsNullOrWhiteSpace(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "LastActionTakenDate")))
                    {
                        optimizationDate = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "LastActionTakenDate")).ToString(constants.datetypeDB);
                    }

                    reservationReport = new DashboardReportResponseRow
                    {
                        ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "reservationid"),
                        NewReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "NewReservationID"),
                        NewReservationStatus = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "NewReservationStatus"),
                        OptimizationDate = optimizationDate,
                        RepricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "repricerid"),
                        CreatedDate = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Createdate")).ToString(constants.datetypeDB),

                        BookingDate = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "BookingDate")).ToString(constants.datetypeDB),
                        ProfitAfterCancellation = DbPropertyHelper.DecimalPropertyFromRow(reader, "ProfitAfterCancellation"),
                        OptimizationProfit = DbPropertyHelper.DecimalPropertyFromRow(reader, "Profit"),
                        CurrencyFactorToEur = DbPropertyHelper.DecimalPropertyFromRow(reader, "CurrencyFactorToEur"),
                        IsCancellationPolicyMatched = DbPropertyHelper.BoolDefaultPropertyFromRow(reader, "IsCancellationPolicyMatched"),

                        CPStatus = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CPStatus"),
                        CPDaysGain = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "CPDaysGain"),
                        MatchedCancellationPolicyGain = DbPropertyHelper.DecimalPropertyFromRow(reader, "MatchedCancellationPolicyGain"),
                        Token = string.Empty,//DbPropertyHelper.StringDefaultPropertyFromRow(reader, "Token"),
                        AvailabilityToken = string.Empty,//DbPropertyHelper.StringDefaultPropertyFromRow(reader, "AvailabilityToken"),
                        Currency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCurrency"),
                        IsReservationActionTaken = DbPropertyHelper.BoolNullablePropertyFromRow(reader, "IsBookingActionTaken"),
                        ActionId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ActionId"),
                        ReportType = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reportType"),

                        CustomerAmount = DbPropertyHelper.DecimalPropertyFromRow(reader, "CustomerAmount"),
                        CustomerCurrency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "CustomerCurrency"),
                        CustomerCurrencyFactor = DbPropertyHelper.DecimalPropertyFromRow(reader, "CustomerCurrencyFactor")
                    };

                    reservationReport.Reservation = new ReservationAndPreBookCompare
                    {
                        AdultCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "reservationadultcount"),
                        ChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reservationchildages"),

                        Supplier = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reservationSupplier"),

                        Price = DbPropertyHelper.DecimalPropertyFromRow(reader, "reservationprice"),
                        RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reservationroomname"),
                        RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reservationroomboard"),
                        BoardMapping = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationRoomBoardGroup"),
                        RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reservationroominfo"),
                        RoomIndex = string.Empty /*##TODO*/,
                        CancellationPolicyStartDate = string.IsNullOrEmpty(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "matchedreservationcancellationdate")) ? "" : System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "matchedreservationcancellationdate")).ToString(constants.datetypeDB) ?? "",
                        CancellationCharge = DbPropertyHelper.DecimalPropertyFromRow(reader, "matchedreservationcancellationchargebypolicy"),
                        CancellationCurrency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCurrency"),

                        NumberOfRooms = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "numberofrooms"),
                        Status = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reservationstatus"),

                        CheckIn = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reservationcheckin")).ToString(constants.datetype),
                        CheckOut = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reservationcheckout")).ToString(constants.datetype),

                        Destination = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reservationdestination"),
                        HotelName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reservationhotelname"),
                        RoomType = DbPropertyHelper.StringPropertyFromRow(reader, "roomType"),
                        Currency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCurrency"),
                        CancellationPolicyType = DbPropertyHelper.StringPropertyFromRow(reader, "ReservationCancellationType"),
                        MappingId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationGiataMappingId"),
                        PropertyName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationGiataPropertyName")
                    };

                    // Create prebook as List with rank 1 (primary prebook)
                    var primaryPrebook = new ReservationAndPreBookCompare
                    {
                        AdultCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "prebookadultcount"),
                        ChildAges = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "prebookchildages"),

                        Supplier = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "prebooksupplier"),

                        Price = DbPropertyHelper.DecimalPropertyFromRow(reader, "prebookprice"),
                        RoomName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "prebookroomname"),
                        RoomBoard = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "prebookroomboard"),
                        BoardMapping = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PrebookRoomBoardGroup"),
                        RoomInfo = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PrebookRoomInfo"),
                        RoomIndex = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "prebookroomindex"),
                        CancellationPolicyStartDate = string.IsNullOrEmpty(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "matchedprebookcancellationdate")) ? "" : System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "matchedprebookcancellationdate")).ToString(constants.datetypeDB),
                        CancellationCharge = DbPropertyHelper.DecimalPropertyFromRow(reader, "matchedprebookcancellationchargebypolicy"),
                        CancellationCurrency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCurrency"),

                        NumberOfRooms = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "numberofrooms"),
                        Status = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "NewReservationStatus") ?? "OK",
                        CheckIn = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reservationcheckin")).ToString(constants.datetype),
                        CheckOut = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "reservationcheckout")).ToString(constants.datetype),

                        Destination = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "prebookdestination"),
                        HotelName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "prebookhotelname"),
                        Currency = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PreBookCurrency"),
                        CancellationPolicyType = DbPropertyHelper.StringPropertyFromRow(reader, "PreBookCancellationType"),
                        MappingId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "SearchGiataMappingId"),
                        PropertyName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "SearchGiataPropertyName"),
                        Rank = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "PrebookRank") // Use rank from database
                    };

                    // Set primary prebook (backward compatibility)
                    reservationReport.Prebook = primaryPrebook;

                    try
                    {
                        reservationReport.ResellerDetail = new ResellerDetails
                        {
                            ResellerName = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ResellerName"),
                            ResellerCode = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ResellerCode"),
                            ResellerType = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ResellerType"),
                        };
                    }
                    catch (Exception ex)
                    {
                    }

                    try
                    {
                        reservationReport.Reservation.LastActivity = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationLastActivity")).ToString(constants.datetypeDB);
                        reservationReport.Prebook.LastActivity = System.Convert.ToDateTime(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PrebookLastActivity")).ToString(constants.datetypeDB);
                        var resCD = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCancelledOnDate");
                        var preCD = DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PrebookCancelledOnDate");
                        if (!string.IsNullOrEmpty(resCD))
                        {
                            reservationReport.Reservation.CancelledOnDate = System.Convert.ToDateTime(resCD).ToString(constants.datetypeDB);
                        }
                        if (!string.IsNullOrEmpty(preCD))
                        {
                            reservationReport.Prebook.CancelledOnDate = System.Convert.ToDateTime(preCD).ToString(constants.datetypeDB);
                        }
                    }
                    catch (Exception ex)
                    {
                    }

                    try
                    {
                        var reservationCpJSON = System.Convert.ToString(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "ReservationCpJSON")).ToString();
                        reservationReport.Reservation.CancellationPoliciesBySource = SerializeDeSerializeHelper.DeSerialize<List<CancellationPolicyBySource>>(reservationCpJSON);
                        reservationReport.Reservation.CancellationPoliciesInSupplierCurrency = SerializeDeSerializeHelper.DeSerialize<List<CancellationPolicyBySource>>(reservationCpJSON);
                    }
                    catch (Exception ex)
                    {
                    }

                    try
                    {
                        var prebookCpJSON = System.Convert.ToString(DbPropertyHelper.StringDefaultPropertyFromRow(reader, "PrebookCpJSON")).ToString();
                        reservationReport.Prebook.CancellationPoliciesBySource = SerializeDeSerializeHelper.DeSerialize<List<CancellationPolicyBySource>>(prebookCpJSON);
                        reservationReport.Prebook.CancellationPoliciesInSupplierCurrency = SerializeDeSerializeHelper.DeSerialize<List<CancellationPolicyBySource>>(prebookCpJSON);
                        if (reservationReport.NewReservationId > 0)
                        {
                            var newReservationCP = reservationReport?.Prebook?.CancellationPoliciesBySource?
                                                    .FirstOrDefault
                                                     (x =>
                                                           x.RepricerId == reservationReport.RepricerId
                                                        && x.ReservationId == reservationReport.NewReservationId
                                                     );
                            if (newReservationCP != null)
                            {
                                if (!string.IsNullOrEmpty(newReservationCP.CancellationPolicyStartDate) && reservationReport?.Prebook?.CancellationPolicyStartDate != newReservationCP.CancellationPolicyStartDate)
                                {
                                    reservationReport.Prebook.CancellationPolicyStartDate = newReservationCP.CancellationPolicyStartDate;
                                }
                                if (!string.IsNullOrEmpty(newReservationCP.CancellationPolicyType) && reservationReport?.Prebook?.CancellationPolicyType != newReservationCP.CancellationPolicyType)
                                {
                                    reservationReport.Prebook.CancellationPolicyType = newReservationCP.CancellationPolicyType;
                                }
                                if (newReservationCP.CancellationCharge > 0 && reservationReport?.Prebook?.CancellationCharge != Math.Round(newReservationCP.CancellationCharge, 2))
                                {
                                    reservationReport.Prebook.CancellationCharge = Math.Round(newReservationCP.CancellationCharge * reservationReport.CurrencyFactorToEur, 2);
                                }
                                //if (!string.IsNullOrEmpty(newReservationCP.Currency) && reservationReport?.Prebook?.CancellationCurrency != newReservationCP?.Currency)
                                //{
                                //    reservationReport.Prebook.CancellationCurrency = newReservationCP.Currency;
                                //}
                                try
                                {
                                    string datetypeDB = "yyyy-MM-dd HH:mm:ss";

                                    var resCPdate = reservationReport.Reservation.CancellationPolicyStartDate;
                                    var preCPdate = reservationReport.Prebook.CancellationPolicyStartDate;

                                    var reserCPDate_withoutTimezone = DateTime.ParseExact(resCPdate, datetypeDB, System.Globalization.CultureInfo.InvariantCulture);
                                    var preCPDate_withoutTimezone = DateTime.ParseExact(preCPdate, datetypeDB, System.Globalization.CultureInfo.InvariantCulture);

                                    if (preCPDate_withoutTimezone >= reserCPDate_withoutTimezone)
                                    {
                                        reservationReport.CPStatus = "loose";
                                    }
                                    reservationReport.MatchedCancellationPolicyGain = reservationReport.Reservation.CancellationCharge - reservationReport.Prebook.CancellationCharge;
                                    reservationReport.CPDaysGain = (preCPDate_withoutTimezone.Date - reserCPDate_withoutTimezone.Date).Days;
                                }
                                catch
                                {
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                    }

                    reservationReports.Add(reservationReport);
                }
                catch (Exception ex)
                {
                }
            }

            return reservationReports;
        }

        public List<MultiSupplierRoomDetails> GetMultiSupplierRoomDetails(IDataReader reader)
        {
            var multiSupplierRoomDetailsList = new List<MultiSupplierRoomDetails>();

            while (reader.Read())
            {
                var multiSupplierRoomDetails = new MultiSupplierRoomDetails
                {
                    RepricerId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "RepricerId"),
                    ReservationId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "ReservationId"),
                    CheckIn = DbPropertyHelper.DateTimePropertyFromRow(reader, "checkIn"),
                    CheckOut = DbPropertyHelper.DateTimePropertyFromRow(reader, "checkout"),
                    PassengerCount = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "passengerCount"),
                    ChildAges = DbPropertyHelper.StringPropertyFromRow(reader, "ChildAges"),
                    CountryName = DbPropertyHelper.StringPropertyFromRow(reader, "CountryName"),
                    Destinations = DbPropertyHelper.StringPropertyFromRow(reader, "Destinations"),
                    HotelName = DbPropertyHelper.StringPropertyFromRow(reader, "hotelname"),
                    RoomName = DbPropertyHelper.StringPropertyFromRow(reader, "RoomName"),
                    RoomBoard = DbPropertyHelper.StringPropertyFromRow(reader, "RoomBoard"),
                    RoomInfo = DbPropertyHelper.StringPropertyFromRow(reader, "RoomInfo"),
                    RoomCode = DbPropertyHelper.StringPropertyFromRow(reader, "RoomCode"),
                    Descriptions = DbPropertyHelper.StringPropertyFromRow(reader, "Descriptions"),
                    FacilitiesDescription = DbPropertyHelper.StringPropertyFromRow(reader, "FacilitiesDescription"),
                    RoomStatus = DbPropertyHelper.StringPropertyFromRow(reader, "RoomStatus"),
                    RoomBoardBasis = DbPropertyHelper.StringPropertyFromRow(reader, "RoomBoardBasis"),
                    IssueNetPrice = DbPropertyHelper.DecimalPropertyFromRow(reader, "IssueNetPrice"),
                    IssueCurrency = DbPropertyHelper.StringPropertyFromRow(reader, "IssueCurrency"),
                    RoomRateTags = DbPropertyHelper.StringPropertyFromRow(reader, "RoomRateTags"),
                    SupplierName = DbPropertyHelper.StringPropertyFromRow(reader, "SupplierName"),
                    Id = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "Id"),
                    MappingRoomId = DbPropertyHelper.Int32DefaultPropertyFromRow(reader, "MappingRoomId"),
                    CancellationDate = DbPropertyHelper.StringPropertyFromRow(reader, "CancellationDate"),
                    CancellationChargeValue = DbPropertyHelper.DoublePropertyFromRow(reader, "CancellationChargeValue"),
                    CancellationChargeCurrency = DbPropertyHelper.StringPropertyFromRow(reader, "CancellationChargeCurrency")
                };

                multiSupplierRoomDetailsList.Add(multiSupplierRoomDetails);
            }

            return multiSupplierRoomDetailsList;
        }

        public List<ChangeLogEntry> GetChangeLogEntries(IDataReader reader)
        {
            var changeLogEntries = new List<ChangeLogEntry>();
            while (reader.Read())
            {
                var entry = new ChangeLogEntry
                {
                    ChangeLogID = reader.GetInt32(reader.GetOrdinal("ChangeLogID")),
                    ColumnName = reader.GetString(reader.GetOrdinal("ColumnName")),
                    OldValue = reader.GetString(reader.GetOrdinal("OldValue")),
                    NewValue = reader.GetString(reader.GetOrdinal("NewValue")),
                    ChangeDate = reader.GetDateTime(reader.GetOrdinal("ChangeDate")),
                    ChangedBy = reader.GetString(reader.GetOrdinal("ChangedBy")),
                    RePricerId = reader.IsDBNull(reader.GetOrdinal("RePricerId")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("RePricerId"))
                };
                changeLogEntries.Add(entry);
            }
            return changeLogEntries;
        }

        public List<SupplierCount> GetSupplierCounts(IDataReader reader)
        {
            var supplierCounts = new List<SupplierCount>();

            while (reader.Read())
            {
                SupplierCount supplierCount = null;
                try
                {
                    supplierCount = new SupplierCount
                    {
                        RepricerID = reader.GetInt32(reader.GetOrdinal("RepricerID")),
                        Month = null,//reader.GetString(reader.GetOrdinal("Month")),
                        PrebookSupplier = reader.GetString(reader.GetOrdinal("PrebookSupplier")),
                        Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                        Count = reader.GetInt32(reader.GetOrdinal("SupplierCount")),
                    };

                    if (supplierCount != null)
                    {
                        if (supplierCount.RepricerID == 0)
                        {
                            supplierCount.RepricerName = constants.SuperAdmin;
                        }

                        supplierCounts.Add(supplierCount);
                    }
                }
                catch (Exception ex)
                {
                }
            }

            return supplierCounts;
        }

        public ExternalApi GetExternalApi(IDataReader reader)
        {
            return new ExternalApi
            {
                Id = reader.GetInt32(reader.GetOrdinal("Id")),
                Url = reader.GetString(reader.GetOrdinal("Url")),
                Username = reader.GetString(reader.GetOrdinal("Username")),
                Password = reader.GetString(reader.GetOrdinal("Password")),
                CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate")),
                Service = reader.GetString(reader.GetOrdinal("Service"))
            };
        }

        internal List<GiataRoomMapping> GiataMappingRoomDetailsReader(SqlDataReader reader)
        {
            List<GiataRoomMapping> roomDetails = new List<GiataRoomMapping>();
            while (reader.Read())
            {
                try
                {
                    GiataMappingRoomDetails roomDetail = new GiataMappingRoomDetails
                    {
                        GIATAMappingID = reader.GetInt32(reader.GetOrdinal("GIATAMappingID")),
                        PropertyName = reader.IsDBNull(reader.GetOrdinal("PropertyName")) ? null : reader.GetString(reader.GetOrdinal("PropertyName")),
                        GroupName = reader.IsDBNull(reader.GetOrdinal("GroupName")) ? null : reader.GetString(reader.GetOrdinal("GroupName")),
                        GroupID = reader.GetInt32(reader.GetOrdinal("GroupID")),
                        RoomName = reader.IsDBNull(reader.GetOrdinal("RoomName")) ? null : reader.GetString(reader.GetOrdinal("RoomName")),
                        GroupConfidence = reader.GetInt32(reader.GetOrdinal("GroupConfidence")),
                        BedDetailDescription = reader.IsDBNull(reader.GetOrdinal("BedDetailDescription")) ? null : reader.GetString(reader.GetOrdinal("BedDetailDescription")),
                        RoomCount = reader.GetInt32(reader.GetOrdinal("RoomCount")),
                        Accessible = reader.GetBoolean(reader.GetOrdinal("Accessible")),
                        NonRefundable = reader.GetBoolean(reader.GetOrdinal("NonRefundable")),
                        Annex = reader.GetBoolean(reader.GetOrdinal("Annex")),
                        SingleUse = reader.GetBoolean(reader.GetOrdinal("SingleUse")),
                        SharedBed = reader.GetBoolean(reader.GetOrdinal("SharedBed")),
                        AverageRoomType = reader.IsDBNull(reader.GetOrdinal("AverageRoomType")) ? null : reader.GetString(reader.GetOrdinal("AverageRoomType")),
                        AverageRoomClasses = reader.IsDBNull(reader.GetOrdinal("AverageRoomClasses")) ? null : reader.GetString(reader.GetOrdinal("AverageRoomClasses")),
                        AverageRoomViews = reader.IsDBNull(reader.GetOrdinal("AverageRoomViews")) ? null : reader.GetString(reader.GetOrdinal("AverageRoomViews")),
                        RepricerID = reader.GetInt32(reader.GetOrdinal("RepricerID")),
                        ReservationId = reader.IsDBNull(reader.GetOrdinal("ReservationID")) ? null : reader.GetString(reader.GetOrdinal("ReservationID")),
                        Supplier = reader.IsDBNull(reader.GetOrdinal("Supplier")) ? null : reader.GetString(reader.GetOrdinal("Supplier"))
                    };
                    GiataRoomMapping giataRoomMapping = new GiataRoomMapping
                    {
                        MappingId = roomDetail.GIATAMappingID,
                        PropertyName = roomDetail.PropertyName,
                        GroupName = roomDetail.GroupName,
                        GroupID = roomDetail.GroupID,
                        RoomName = roomDetail.RoomName,
                        GroupConfidence = roomDetail.GroupConfidence,
                        BedDetailDescription = roomDetail.BedDetailDescription,
                        RoomCount = roomDetail.RoomCount,
                        Accessible = roomDetail.Accessible,
                        NonRefundable = roomDetail.NonRefundable,
                        Annex = roomDetail.Annex,
                        SingleUse = roomDetail.SingleUse,
                        SharedBed = roomDetail.SharedBed,
                        AverageRoomType = roomDetail.AverageRoomType,
                        AverageRoomClasses = roomDetail.AverageRoomClasses,
                        AverageRoomViews = roomDetail.AverageRoomViews,
                    };

                    roomDetails.Add(giataRoomMapping);
                }
                catch (Exception ex)
                {
                }
            }
            return roomDetails;
        }
    }
}