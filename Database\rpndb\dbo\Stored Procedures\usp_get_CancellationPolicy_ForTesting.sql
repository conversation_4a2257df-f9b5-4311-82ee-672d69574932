﻿CREATE Procedure dbo.usp_get_CancellationPolicy_ForTesting      
--declare      
@RePricerId Int     ,
@ReservationId Int  = null
As          
begin         
        
    DECLARE @CurrentDate DATE = GETUTCDATE();                                   
        
         
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
	SELECT rcp.ReservationId,
		   rcp.Cancellation_type,
		   DATEDIFF(DAY, GETUTCDATE(), rcp.date) AS DaysDifference,
		   rcp.date as cancellationdate,
		   rcp.Cancellation_Charge,
		   rcp.Currency,
		   rm.cancellation_date as maincancellationdate
	from [dbo].[ReservationCancellationPolicy] rcp
		inner join dbo.ReservationMain rm
			on rm.ReservationId = rcp.reservationId
			   and rm.repricerid = rcp.repricerid
	where rm.RePricerId = @RePricerId
		  -- AND Cast(rm.checkIn AS DATE) >= cast(@CurrentDate as date)
		  -- AND rm.ReservationStatus = 'OK'
		  --   AND DATEDIFF(DAY, @CurrentDate, CAST(rm.checkIn AS DATE)) >= 10       
		  --   AND DATEDIFF(DAY, @CurrentDate, CAST(rm.checkIn AS DATE)) <= 60       
		  -- AND cast(rm.cancellation_date as date) > cast(@CurrentDate as date)
		  and (rm.ReservationId = @ReservationId or @ReservationId is null)
	order by rm.ReservationId,
			 DaysDifference       

   



End     
